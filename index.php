<?php
// التحقق من إعداد قاعدة البيانات أولاً
try {
    require_once 'config.php';

    // محاولة الاتصال بقاعدة البيانات والتحقق من الجداول
    $pdo = getDBConnection();
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");

    if ($stmt->rowCount() == 0) {
        // إعادة توجيه لإعداد قاعدة البيانات
        header('Location: setup_database.php');
        exit();
    }

    // إعادة توجيه حسب نوع المستخدم إذا كان مسجل دخول
    if (isUserLoggedIn()) {
        $user_type = $_SESSION['user_type'];

        switch ($user_type) {
            case 'admin':
                header('Location: admin_dashboard.php');
                break;
            case 'supervisor':
                header('Location: supervisor_dashboard.php');
                break;
            case 'employee':
                header('Location: employee_dashboard.php');
                break;
            default:
                header('Location: login.php');
        }
        exit();
    }

    // إعادة توجيه إلى صفحة تسجيل الدخول
    header('Location: login.php');
    exit();

} catch (Exception $e) {
    // في حالة أي خطأ، إعادة توجيه لإعداد قاعدة البيانات
    header('Location: setup_database.php');
    exit();
}
?>
