<?php
require_once 'config.php';

// إعادة توجيه حسب نوع المستخدم إذا كان مسجل دخول
if (isUserLoggedIn()) {
    $user_type = $_SESSION['user_type'];

    switch ($user_type) {
        case 'admin':
            header('Location: admin_dashboard.php');
            break;
        case 'supervisor':
            header('Location: supervisor_dashboard.php');
            break;
        case 'employee':
            header('Location: employee_dashboard.php');
            break;
        default:
            header('Location: login.php');
    }
    exit();
}

// إعادة توجيه إلى صفحة تسجيل الدخول
header('Location: login.php');
exit();
?>
