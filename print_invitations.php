<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
requireSupervisor();

$pdo = getDBConnection();
$guests = [];
$title = 'طباعة الدعوات';

// جلب الدعوات حسب المعايير
$batchPrefix = sanitizeInput($_GET['batch_prefix'] ?? '');
$batchDate = sanitizeInput($_GET['batch_date'] ?? '');
$guestIds = $_GET['ids'] ?? '';

try {
    if (!empty($batchPrefix) && !empty($batchDate)) {
        // جلب مجموعة محددة
        $stmt = $pdo->prepare("SELECT * FROM guests WHERE name LIKE ? AND DATE(created_at) = ? ORDER BY name");
        $stmt->execute(["%{$batchPrefix}%", $batchDate]);
        $guests = $stmt->fetchAll();
        $title = "طباعة دعوات: {$batchPrefix} - {$batchDate}";
        
    } elseif (!empty($guestIds)) {
        // جلب دعوات محددة بالمعرفات
        $ids = explode(',', $guestIds);
        $ids = array_filter(array_map('intval', $ids));
        
        if (!empty($ids)) {
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            $stmt = $pdo->prepare("SELECT * FROM guests WHERE id IN ({$placeholders}) ORDER BY name");
            $stmt->execute($ids);
            $guests = $stmt->fetchAll();
            $title = "طباعة دعوات مختارة";
        }
        
    } else {
        // جلب جميع الدعوات
        $stmt = $pdo->query("SELECT * FROM guests ORDER BY created_at DESC LIMIT 100");
        $guests = $stmt->fetchAll();
        $title = "طباعة جميع الدعوات (آخر 100)";
    }
    
} catch (PDOException $e) {
    $guests = [];
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: white;
            color: #333;
            line-height: 1.4;
        }
        
        .no-print {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
        }
        
        .no-print h1 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .no-print .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            cursor: pointer;
            display: inline-block;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        
        .print-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 10mm;
        }
        
        .invitation-card {
            width: 90mm;
            height: 55mm;
            border: 2px solid #333;
            border-radius: 8px;
            padding: 5mm;
            margin: 5mm;
            display: inline-block;
            vertical-align: top;
            page-break-inside: avoid;
            position: relative;
            background: white;
        }
        
        .invitation-header {
            text-align: center;
            border-bottom: 1px solid #ddd;
            padding-bottom: 3mm;
            margin-bottom: 3mm;
        }
        
        .invitation-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 2mm;
        }
        
        .invitation-subtitle {
            font-size: 10px;
            color: #666;
        }
        
        .invitation-body {
            text-align: center;
        }
        
        .guest-name {
            font-size: 12px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 2mm;
            min-height: 8mm;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .invitation-code {
            font-size: 8px;
            font-family: monospace;
            background: #f8f9fa;
            padding: 1mm;
            border-radius: 3px;
            margin-bottom: 2mm;
            word-break: break-all;
        }
        
        .qr-placeholder {
            width: 15mm;
            height: 15mm;
            border: 1px dashed #ccc;
            margin: 2mm auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            color: #999;
        }
        
        .invitation-footer {
            position: absolute;
            bottom: 2mm;
            left: 2mm;
            right: 2mm;
            text-align: center;
            font-size: 7px;
            color: #999;
        }
        
        .invitation-url {
            font-size: 6px;
            color: #666;
            word-break: break-all;
            margin-top: 1mm;
        }
        
        /* تخطيط الطباعة */
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
            }
            
            .print-container {
                max-width: none;
                margin: 0;
                padding: 5mm;
            }
            
            .invitation-card {
                margin: 2mm;
            }
            
            @page {
                size: A4;
                margin: 10mm;
            }
        }
        
        /* تخطيط متجاوب */
        @media (max-width: 768px) {
            .invitation-card {
                width: calc(50% - 10mm);
                height: auto;
                min-height: 55mm;
            }
        }
        
        @media (max-width: 480px) {
            .invitation-card {
                width: calc(100% - 10mm);
            }
        }
        
        .stats {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- أدوات التحكم (لا تطبع) -->
    <div class="no-print">
        <h1>🖨️ <?php echo $title; ?></h1>
        
        <?php if (!empty($guests)): ?>
            <div class="stats">
                📊 عدد الدعوات: <strong><?php echo count($guests); ?></strong> |
                📅 تاريخ الطباعة: <strong><?php echo date('Y-m-d H:i'); ?></strong>
            </div>
        <?php endif; ?>
        
        <div class="controls">
            <button onclick="window.print()" class="btn btn-primary">
                🖨️ طباعة
            </button>
            
            <button onclick="window.history.back()" class="btn btn-secondary">
                🔙 العودة
            </button>
            
            <a href="dashboard.php" class="btn btn-info">
                📋 لوحة التحكم
            </a>
            
            <?php if (!empty($guests)): ?>
                <button onclick="selectPrintOptions()" class="btn btn-success">
                    ⚙️ خيارات الطباعة
                </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- محتوى الطباعة -->
    <div class="print-container">
        <?php if (empty($guests)): ?>
            <div style="text-align: center; padding: 50px; color: #666;">
                <h2>📭 لا توجد دعوات للطباعة</h2>
                <p>يرجى اختيار دعوات صالحة للطباعة</p>
                <br>
                <a href="dashboard.php" class="btn btn-primary">العودة للوحة التحكم</a>
            </div>
        <?php else: ?>
            <?php foreach ($guests as $guest): ?>
                <div class="invitation-card">
                    <!-- رأس الدعوة -->
                    <div class="invitation-header">
                        <div class="invitation-title">🎫 دعوة حضور</div>
                        <div class="invitation-subtitle">نتشرف بحضوركم</div>
                    </div>
                    
                    <!-- محتوى الدعوة -->
                    <div class="invitation-body">
                        <div class="guest-name">
                            <?php echo htmlspecialchars($guest['name']); ?>
                        </div>
                        
                        <div class="invitation-code">
                            كود الدعوة: <?php echo htmlspecialchars($guest['code']); ?>
                        </div>
                        
                        <!-- مكان كود QR -->
                        <div class="qr-placeholder">
                            QR CODE
                        </div>
                        
                        <?php if (!empty($guest['notes'])): ?>
                            <div style="font-size: 8px; color: #666; margin-top: 2mm;">
                                <?php echo htmlspecialchars($guest['notes']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- تذييل الدعوة -->
                    <div class="invitation-footer">
                        <div>تاريخ الإنشاء: <?php echo date('Y-m-d', strtotime($guest['created_at'])); ?></div>
                        <div class="invitation-url">
                            <?php echo SITE_URL . '/verify.php?code=' . $guest['code']; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <script>
        function selectPrintOptions() {
            const options = [
                'طباعة جميع الدعوات',
                'طباعة الدعوات غير المستخدمة فقط',
                'طباعة الدعوات المستخدمة فقط'
            ];
            
            let choice = prompt('اختر نوع الطباعة:\n1. ' + options[0] + '\n2. ' + options[1] + '\n3. ' + options[2] + '\n\nأدخل رقم الخيار (1-3):');
            
            if (choice) {
                choice = parseInt(choice);
                if (choice >= 1 && choice <= 3) {
                    filterAndPrint(choice);
                }
            }
        }
        
        function filterAndPrint(option) {
            const cards = document.querySelectorAll('.invitation-card');
            
            cards.forEach(card => {
                const guestName = card.querySelector('.guest-name').textContent;
                // هنا يمكن إضافة منطق الفلترة حسب الحالة
                // لكن نحتاج معلومات إضافية من PHP
            });
            
            window.print();
        }
        
        // تحسين الطباعة
        window.addEventListener('beforeprint', function() {
            document.title = '<?php echo $title; ?> - <?php echo date("Y-m-d"); ?>';
        });
        
        window.addEventListener('afterprint', function() {
            document.title = '<?php echo $title; ?>';
        });
    </script>
</body>
</html>
