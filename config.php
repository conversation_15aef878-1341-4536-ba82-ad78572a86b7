<?php
/**
 * ملف إعدادات قاعدة البيانات
 * Database Configuration File
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'invitation_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الجلسة
define('SESSION_NAME', 'invitation_admin');

// إعدادات الموقع
define('SITE_URL', 'http://localhost/دعوات حظور');
define('ADMIN_USERNAME', 'admin');
define('ADMIN_PASSWORD', 'admin123');

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}

/**
 * الاتصال بقاعدة البيانات
 */
function getDBConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch (PDOException $e) {
        // إذا كانت قاعدة البيانات غير موجودة، إعادة توجيه لإعداد النظام
        if (strpos($e->getMessage(), 'Unknown database') !== false) {
            header('Location: setup_database.php');
            exit();
        }
        die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br><a href='setup_database.php'>إعداد قاعدة البيانات</a>");
    }
}

/**
 * التحقق من تسجيل دخول المستخدم
 */
function isUserLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_type']);
}

/**
 * الحصول على معلومات المستخدم الحالي
 */
function getCurrentUser() {
    if (!isUserLoggedIn()) {
        return null;
    }

    return [
        'id' => $_SESSION['user_id'],
        'username' => $_SESSION['username'],
        'full_name' => $_SESSION['full_name'],
        'user_type' => $_SESSION['user_type'],
        'email' => $_SESSION['email'] ?? null
    ];
}

/**
 * التحقق من نوع المستخدم
 */
function isAdmin() {
    return isUserLoggedIn() && $_SESSION['user_type'] === 'admin';
}

function isSupervisor() {
    return isUserLoggedIn() && $_SESSION['user_type'] === 'supervisor';
}

function isEmployee() {
    return isUserLoggedIn() && $_SESSION['user_type'] === 'employee';
}

/**
 * التحقق من الصلاحيات
 */
function hasPermission($permission) {
    if (isAdmin()) {
        return true; // المدير له جميع الصلاحيات
    }

    if (!isUserLoggedIn()) {
        return false;
    }

    // التحقق من الصلاحيات المخصصة
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM permissions WHERE user_id = ? AND permission = ?");
    $stmt->execute([$_SESSION['user_id'], $permission]);

    return $stmt->fetchColumn() > 0;
}

/**
 * إعادة توجيه إلى صفحة تسجيل الدخول
 */
function redirectToLogin() {
    header('Location: login.php');
    exit();
}

/**
 * التحقق من تسجيل الدخول (عام)
 */
function requireLogin() {
    if (!isUserLoggedIn()) {
        redirectToLogin();
    }
}

/**
 * التحقق من صلاحية المدير
 */
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header('Location: unauthorized.php');
        exit();
    }
}

/**
 * التحقق من صلاحية المشرف أو أعلى
 */
function requireSupervisor() {
    requireLogin();
    if (!isAdmin() && !isSupervisor()) {
        header('Location: unauthorized.php');
        exit();
    }
}

/**
 * للتوافق مع النظام القديم
 */
function isAdminLoggedIn() {
    return isAdmin();
}

function checkAdminAuth() {
    requireSupervisor(); // المشرف يمكنه الوصول لمعظم الوظائف
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * توليد كود فريد
 */
function generateUniqueCode() {
    return 'INV_' . strtoupper(uniqid()) . '_' . rand(1000, 9999);
}

/**
 * تسجيل العمليات
 */
function logActivity($action, $target_type = null, $target_id = null, $details = null) {
    try {
        $pdo = getDBConnection();
        $user_id = isUserLoggedIn() ? $_SESSION['user_id'] : null;
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;

        $stmt = $pdo->prepare("
            INSERT INTO activity_log (user_id, action, target_type, target_id, details, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([$user_id, $action, $target_type, $target_id, $details, $ip_address, $user_agent]);
    } catch (Exception $e) {
        // تجاهل أخطاء التسجيل لعدم تعطيل النظام
    }
}

/**
 * التحقق من صحة كلمة المرور
 */
function validatePassword($password) {
    return strlen($password) >= 6;
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * إنشاء مستخدم جديد
 */
function createUser($username, $password, $full_name, $email, $phone, $user_type) {
    $pdo = getDBConnection();

    // التحقق من عدم وجود اسم المستخدم
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$username, $email]);

    if ($stmt->fetchColumn() > 0) {
        return ['success' => false, 'message' => 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً'];
    }

    // إنشاء المستخدم
    $hashed_password = hashPassword($password);
    $created_by = isUserLoggedIn() ? $_SESSION['user_id'] : null;

    $stmt = $pdo->prepare("
        INSERT INTO users (username, password, full_name, email, phone, user_type, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");

    if ($stmt->execute([$username, $hashed_password, $full_name, $email, $phone, $user_type, $created_by])) {
        $user_id = $pdo->lastInsertId();
        logActivity('create_user', 'user', $user_id, "تم إنشاء مستخدم جديد: {$username} ({$user_type})");
        return ['success' => true, 'user_id' => $user_id];
    }

    return ['success' => false, 'message' => 'خطأ في إنشاء المستخدم'];
}

/**
 * تسجيل دخول المستخدم
 */
function loginUser($username, $password) {
    try {
        $pdo = getDBConnection();

        // التحقق من وجود جدول users
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() == 0) {
            return ['success' => false, 'message' => 'النظام غير مُعد بعد. يرجى إعداد قاعدة البيانات أولاً.', 'redirect' => 'setup_database.php'];
        }

        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? AND status = 'active'");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if ($user && verifyPassword($password, $user['password'])) {
            // تسجيل الدخول
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['user_type'] = $user['user_type'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['login_time'] = time();

            // تحديث آخر دخول
            $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $stmt->execute([$user['id']]);

            logActivity('login', 'user', $user['id'], "تسجيل دخول ناجح");

            return ['success' => true, 'user' => $user];
        }

        logActivity('login_failed', null, null, "محاولة دخول فاشلة: {$username}");
        return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];

    } catch (PDOException $e) {
        if (strpos($e->getMessage(), "doesn't exist") !== false) {
            return ['success' => false, 'message' => 'النظام غير مُعد بعد. يرجى إعداد قاعدة البيانات أولاً.', 'redirect' => 'setup_database.php'];
        }
        return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
    }
}

/**
 * عرض رسالة تنبيه
 */
function showAlert($message, $type = 'info') {
    return "<div class='alert alert-{$type}'>{$message}</div>";
}
?>
