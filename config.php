<?php
/**
 * ملف إعدادات قاعدة البيانات
 * Database Configuration File
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'invitation_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الجلسة
define('SESSION_NAME', 'invitation_admin');

// إعدادات الموقع
define('SITE_URL', 'http://localhost/دعوات حظور');
define('ADMIN_USERNAME', 'admin');
define('ADMIN_PASSWORD', 'admin123');

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}

/**
 * الاتصال بقاعدة البيانات
 */
function getDBConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch (PDOException $e) {
        die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
    }
}

/**
 * التحقق من تسجيل دخول المسؤول
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

/**
 * إعادة توجيه إلى صفحة تسجيل الدخول
 */
function redirectToLogin() {
    header('Location: login.php');
    exit();
}

/**
 * التحقق من صحة المسؤول
 */
function checkAdminAuth() {
    if (!isAdminLoggedIn()) {
        redirectToLogin();
    }
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * توليد كود فريد
 */
function generateUniqueCode() {
    return 'INV_' . strtoupper(uniqid()) . '_' . rand(1000, 9999);
}

/**
 * عرض رسالة تنبيه
 */
function showAlert($message, $type = 'info') {
    return "<div class='alert alert-{$type}'>{$message}</div>";
}
?>
