# 👥 دليل النظام متعدد المستويات

## 🎯 نظرة عامة

تم تطوير النظام ليصبح متعدد المستويات مع ثلاثة أنواع من المستخدمين، كل نوع له صلاحيات وواجهة مخصصة.

## 👑 أنواع المستخدمين

### 1️⃣ المدير (Admin)
**الصلاحيات الكاملة:**
- ✅ إدارة جميع المستخدمين (إنشاء، تفعيل، تعطيل)
- ✅ عرض وإدارة جميع الدعوات
- ✅ الوصول لجميع التقارير والإحصائيات
- ✅ إدارة النظام بالكامل

**لوحة التحكم:** `admin_dashboard.php`

### 2️⃣ المشرف (Supervisor)
**صلاحيات إدارة الدعوات:**
- ✅ إنشاء وإدارة الدعوات الخاصة به
- ✅ إنشاء دعوات متعددة
- ✅ طباعة وتصدير دعواته
- ✅ عرض إحصائيات دعواته
- ❌ لا يمكنه رؤية دعوات المشرفين الآخرين
- ❌ لا يمكنه إدارة المستخدمين

**لوحة التحكم:** `supervisor_dashboard.php`

### 3️⃣ الموظف (Employee)
**صلاحيات مسح الحضور:**
- ✅ مسح أكواد QR لتسجيل الحضور
- ✅ عرض إحصائيات مسحه الشخصية
- ✅ عرض آخر عمليات المسح
- ❌ لا يمكنه إنشاء أو تعديل الدعوات
- ❌ لا يمكنه الوصول لإدارة النظام

**لوحة التحكم:** `employee_dashboard.php`

## 🔐 نظام تسجيل الدخول

### صفحة تسجيل الدخول الموحدة
- **الرابط:** `login.php`
- **عرض أنواع المستخدمين** مع شرح مختصر لكل نوع
- **توجيه تلقائي** حسب نوع المستخدم بعد الدخول

### بيانات الدخول الافتراضية
```
👑 المدير:
اسم المستخدم: admin
كلمة المرور: admin123
```

## 🏗️ هيكل قاعدة البيانات الجديد

### جدول المستخدمين (`users`)
```sql
- id: معرف فريد
- username: اسم المستخدم
- password: كلمة المرور المشفرة
- full_name: الاسم الكامل
- email: البريد الإلكتروني
- phone: رقم الجوال
- user_type: نوع المستخدم (admin/supervisor/employee)
- status: الحالة (active/inactive)
- created_by: من أنشأ هذا المستخدم
- created_at: تاريخ الإنشاء
- last_login: آخر دخول
```

### جدول الضيوف المحدث (`guests`)
```sql
- created_by: من أنشأ الدعوة
- scanned_by: من قام بمسح الكود
```

### جدول سجل العمليات (`activity_log`)
```sql
- user_id: المستخدم الذي قام بالعملية
- action: نوع العملية
- target_type: نوع الهدف
- target_id: معرف الهدف
- details: تفاصيل العملية
- ip_address: عنوان IP
- created_at: وقت العملية
```

## 🚀 كيفية الاستخدام

### للمدير:

#### 1. إنشاء مستخدمين جدد
```
admin_dashboard.php → نموذج إنشاء مستخدم جديد
```

**خطوات الإنشاء:**
1. أدخل اسم المستخدم (فريد)
2. أدخل كلمة مرور قوية (6 أحرف على الأقل)
3. أدخل الاسم الكامل
4. اختر نوع المستخدم (مشرف/موظف)
5. أدخل البريد الإلكتروني (اختياري)
6. أدخل رقم الجوال (اختياري)

#### 2. إدارة المستخدمين
- **تفعيل/تعطيل** المستخدمين
- **عرض آخر دخول** لكل مستخدم
- **تتبع من أنشأ** كل مستخدم

### للمشرف:

#### 1. إدارة الدعوات
```
supervisor_dashboard.php → جميع أدوات إدارة الدعوات
```

**المتاح:**
- إضافة ضيف جديد
- إنشاء دعوات متعددة
- إدارة المجموعات
- طباعة وتصدير الدعوات
- إعادة توليد أكواد QR

#### 2. القيود
- يرى **دعواته فقط**
- لا يمكنه تعديل/حذف دعوات الآخرين
- لا يمكنه الوصول لإدارة المستخدمين

### للموظف:

#### 1. مسح أكواد الحضور
```
employee_dashboard.php → ماسح الباركود
```

**المميزات:**
- **مسح تلقائي** عند إدخال كود كامل
- **تركيز تلقائي** على حقل الإدخال
- **مسح الحقل** بعد التسجيل الناجح
- **عرض معلومات الضيف** بعد المسح

#### 2. الإحصائيات الشخصية
- عدد ما مسحه اليوم
- إجمالي ما مسحه
- آخر عمليات المسح

## 🔒 نظام الأمان

### الصلاحيات
- **فصل كامل** بين صلاحيات كل نوع مستخدم
- **حماية البيانات** - كل مشرف يرى دعواته فقط
- **تسجيل العمليات** - تتبع جميع الأنشطة

### الحماية
- **كلمات مرور مشفرة** باستخدام `password_hash()`
- **تنظيف البيانات** المدخلة
- **حماية من SQL Injection**
- **جلسات آمنة**

### تسجيل العمليات
يتم تسجيل جميع العمليات المهمة:
- تسجيل الدخول/الخروج
- إنشاء المستخدمين
- إنشاء/حذف الدعوات
- مسح أكواد الحضور

## 📱 واجهات المستخدم

### لوحة تحكم المدير
- **إحصائيات شاملة** للمستخدمين والدعوات
- **نموذج إنشاء مستخدمين**
- **جدول إدارة المستخدمين**
- **روابط سريعة** لجميع الوظائف

### لوحة تحكم المشرف
- **إحصائيات الدعوات الشخصية**
- **جميع أدوات إدارة الدعوات**
- **فلترة تلقائية** لعرض دعواته فقط

### لوحة تحكم الموظف
- **ماسح باركود متقدم**
- **إحصائيات المسح الشخصية**
- **عرض آخر العمليات**
- **واجهة مبسطة** ومركزة

## 🔄 التحديثات المطلوبة

### قاعدة البيانات
```sql
-- تشغيل ملف database.sql المحدث
-- سيتم إنشاء الجداول الجديدة وتحديث الموجودة
```

### الملفات الجديدة
- `admin_dashboard.php` - لوحة تحكم المدير
- `supervisor_dashboard.php` - لوحة تحكم المشرف  
- `employee_dashboard.php` - لوحة تحكم الموظف
- `unauthorized.php` - صفحة عدم التصريح

### الملفات المحدثة
- `config.php` - وظائف إدارة المستخدمين والصلاحيات
- `login.php` - واجهة تسجيل دخول محدثة
- `index.php` - توجيه حسب نوع المستخدم
- `database.sql` - هيكل قاعدة البيانات الجديد

## 🎯 الفوائد

### للمؤسسات الكبيرة
- **تقسيم المسؤوليات** بوضوح
- **أمان أفضل** للبيانات
- **تتبع دقيق** للعمليات
- **مرونة في الإدارة**

### لفرق العمل
- **كل شخص له دوره** المحدد
- **واجهات مخصصة** لكل نوع مستخدم
- **عدم تداخل الصلاحيات**
- **سهولة التدريب**

---

## 🎉 النظام الآن جاهز للاستخدام المؤسسي!

يمكن للمؤسسات الكبيرة استخدام النظام مع:
- ✅ **مدير واحد** يدير النظام
- ✅ **عدة مشرفين** كل منهم يدير فعالياته
- ✅ **عدة موظفين** لمسح الحضور في نقاط مختلفة
- ✅ **أمان كامل** وفصل الصلاحيات
- ✅ **تتبع شامل** لجميع العمليات
