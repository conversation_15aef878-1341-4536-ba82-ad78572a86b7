<?php
require_once 'config.php';
require_once 'qr_generator.php';

// التحقق من تسجيل الدخول
requireSupervisor();

$pdo = getDBConnection();
$message = '';
$results = [];
$totalCreated = 0;

// معالجة إنشاء الدعوات المتعددة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $quantity = (int)($_POST['quantity'] ?? 0);
    $prefix = sanitizeInput($_POST['prefix'] ?? 'ضيف');
    $notes = sanitizeInput($_POST['notes'] ?? '');
    $generateQR = isset($_POST['generate_qr']);
    
    // التحقق من صحة البيانات
    if ($quantity <= 0 || $quantity > 1000) {
        $message = showAlert('يرجى إدخال عدد صحيح بين 1 و 1000', 'error');
    } else {
        try {
            $pdo->beginTransaction();
            
            $success = 0;
            $failed = 0;
            $qrSuccess = 0;
            $qrFailed = 0;
            
            for ($i = 1; $i <= $quantity; $i++) {
                // إنشاء اسم مرقم
                $guestName = $prefix . ' رقم ' . $i;
                
                // توليد كود فريد
                $code = generateUniqueCode();
                
                // التأكد من أن الكود فريد
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE code = ?");
                $stmt->execute([$code]);
                
                while ($stmt->fetchColumn() > 0) {
                    $code = generateUniqueCode();
                    $stmt->execute([$code]);
                }
                
                try {
                    // إدراج الضيف في قاعدة البيانات
                    $created_by = isUserLoggedIn() ? $_SESSION['user_id'] : null;
                    $stmt = $pdo->prepare("INSERT INTO guests (name, notes, code, created_by) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$guestName, $notes, $code, $created_by]);
                    
                    $guestId = $pdo->lastInsertId();
                    $qrPath = null;
                    
                    // توليد كود QR إذا كان مطلوباً
                    if ($generateQR) {
                        $verifyUrl = SITE_URL . "/verify.php?code=" . $code;
                        $qrPath = generateAdvancedQRCode($code, $verifyUrl);
                        
                        if ($qrPath) {
                            $qrSuccess++;
                        } else {
                            $qrFailed++;
                        }
                    }
                    
                    $results[] = [
                        'id' => $guestId,
                        'name' => $guestName,
                        'code' => $code,
                        'qr_path' => $qrPath,
                        'status' => 'success'
                    ];
                    
                    $success++;
                    
                } catch (PDOException $e) {
                    $results[] = [
                        'name' => $guestName,
                        'code' => $code,
                        'qr_path' => null,
                        'status' => 'failed',
                        'error' => $e->getMessage()
                    ];
                    $failed++;
                }
            }
            
            $pdo->commit();
            $totalCreated = $success;
            
            $messageText = "تم إنشاء {$success} دعوة بنجاح";
            if ($failed > 0) {
                $messageText .= "، فشل في إنشاء {$failed} دعوة";
            }
            if ($generateQR) {
                $messageText .= "، تم توليد {$qrSuccess} كود QR، فشل في {$qrFailed} كود";
            }
            
            $message = showAlert($messageText, $failed == 0 ? 'success' : 'warning');
            
        } catch (PDOException $e) {
            $pdo->rollBack();
            $message = showAlert('خطأ في إنشاء الدعوات: ' . $e->getMessage(), 'error');
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء دعوات متعددة - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .bulk-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .quantity-input {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            color: #007bff;
        }
        
        .preview-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .result-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .result-card.failed {
            border-left-color: #dc3545;
        }
        
        .result-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .result-card .code {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>📦 إنشاء دعوات متعددة</h1>
                <div class="header-actions">
                    <a href="dashboard.php" class="btn btn-secondary">🔙 العودة للوحة التحكم</a>
                    <a href="add_guest.php" class="btn btn-info">➕ إضافة ضيف واحد</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <!-- نموذج إنشاء الدعوات -->
        <div class="bulk-form">
            <h3>📋 إعدادات الدعوات المتعددة</h3>
            
            <form method="POST" id="bulkForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="quantity">عدد الدعوات المطلوبة: <span class="required">*</span></label>
                        <input type="number" id="quantity" name="quantity" class="quantity-input" 
                               min="1" max="1000" value="<?php echo htmlspecialchars($_POST['quantity'] ?? '10'); ?>" 
                               required placeholder="مثال: 50">
                        <small>الحد الأقصى: 1000 دعوة</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="prefix">بادئة الاسم:</label>
                        <input type="text" id="prefix" name="prefix" 
                               value="<?php echo htmlspecialchars($_POST['prefix'] ?? 'ضيف'); ?>"
                               placeholder="مثال: ضيف، مدعو، حاضر">
                        <small>سيتم إضافة رقم تلقائياً</small>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="notes">ملاحظات عامة:</label>
                    <textarea id="notes" name="notes" rows="3" 
                              placeholder="ملاحظات ستطبق على جميع الدعوات..."><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="generate_qr" value="1" 
                               <?php echo isset($_POST['generate_qr']) ? 'checked' : ''; ?>>
                        توليد أكواد QR تلقائياً (قد يستغرق وقتاً أطول)
                    </label>
                </div>
                
                <!-- معاينة -->
                <div class="preview-section">
                    <h4>📋 معاينة الأسماء:</h4>
                    <div id="namePreview">
                        <span class="code">ضيف رقم 1</span>
                        <span class="code">ضيف رقم 2</span>
                        <span class="code">ضيف رقم 3</span>
                        <span>...</span>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" onclick="return confirmBulkCreate()">
                        🚀 إنشاء الدعوات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                        🔄 إعادة تعيين
                    </button>
                </div>
            </form>
        </div>

        <!-- نتائج الإنشاء -->
        <?php if (!empty($results)): ?>
            <div class="bulk-form">
                <h3>📊 نتائج الإنشاء</h3>
                
                <!-- إحصائيات سريعة -->
                <div class="stats-grid">
                    <div class="stat-card success">
                        <div class="stat-icon">✅</div>
                        <div class="stat-info">
                            <h3><?php echo count(array_filter($results, function($r) { return $r['status'] === 'success'; })); ?></h3>
                            <p>دعوة تم إنشاؤها</p>
                        </div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-icon">❌</div>
                        <div class="stat-info">
                            <h3><?php echo count(array_filter($results, function($r) { return $r['status'] === 'failed'; })); ?></h3>
                            <p>دعوة فشلت</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📱</div>
                        <div class="stat-info">
                            <h3><?php echo count(array_filter($results, function($r) { return !empty($r['qr_path']); })); ?></h3>
                            <p>كود QR تم إنشاؤه</p>
                        </div>
                    </div>
                </div>
                
                <!-- أدوات سريعة -->
                <div class="controls" style="margin: 20px 0;">
                    <div class="controls-left">
                        <a href="dashboard.php" class="btn btn-primary">📋 عرض جميع الدعوات</a>
                        <a href="export.php" class="btn btn-info">📥 تصدير البيانات</a>
                    </div>
                    <div class="controls-right">
                        <button onclick="toggleResults()" class="btn btn-secondary" id="toggleBtn">
                            👁️ إخفاء التفاصيل
                        </button>
                    </div>
                </div>
                
                <!-- تفاصيل النتائج -->
                <div id="detailedResults">
                    <div class="results-grid">
                        <?php foreach (array_slice($results, 0, 50) as $result): ?>
                            <div class="result-card <?php echo $result['status']; ?>">
                                <h4><?php echo htmlspecialchars($result['name']); ?></h4>
                                <p><strong>الكود:</strong> <span class="code"><?php echo htmlspecialchars($result['code']); ?></span></p>
                                
                                <?php if ($result['status'] === 'success'): ?>
                                    <p><strong>الحالة:</strong> <span style="color: #28a745;">✅ تم الإنشاء</span></p>
                                    
                                    <?php if (!empty($result['qr_path'])): ?>
                                        <p><strong>QR:</strong> 
                                            <a href="<?php echo $result['qr_path']; ?>" target="_blank" class="btn btn-sm btn-info">
                                                📱 عرض
                                            </a>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <p><strong>رابط التحقق:</strong></p>
                                    <input type="text" value="<?php echo SITE_URL . '/verify.php?code=' . $result['code']; ?>" 
                                           readonly onclick="this.select()" style="font-size: 10px; width: 100%;">
                                           
                                <?php else: ?>
                                    <p><strong>الحالة:</strong> <span style="color: #dc3545;">❌ فشل</span></p>
                                    <?php if (!empty($result['error'])): ?>
                                        <p><strong>الخطأ:</strong> <small><?php echo htmlspecialchars($result['error']); ?></small></p>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <?php if (count($results) > 50): ?>
                        <div class="alert alert-info">
                            📋 يتم عرض أول 50 نتيجة فقط. لعرض جميع النتائج، اذهب إلى لوحة التحكم.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // تحديث معاينة الأسماء
        function updatePreview() {
            const quantity = document.getElementById('quantity').value || 10;
            const prefix = document.getElementById('prefix').value || 'ضيف';
            const preview = document.getElementById('namePreview');
            
            let html = '';
            const showCount = Math.min(quantity, 5);
            
            for (let i = 1; i <= showCount; i++) {
                html += `<span class="code">${prefix} رقم ${i}</span> `;
            }
            
            if (quantity > 5) {
                html += '<span>...</span> ';
                html += `<span class="code">${prefix} رقم ${quantity}</span>`;
            }
            
            preview.innerHTML = html;
        }
        
        // تأكيد الإنشاء
        function confirmBulkCreate() {
            const quantity = document.getElementById('quantity').value;
            const generateQR = document.querySelector('input[name="generate_qr"]').checked;
            
            let message = `هل أنت متأكد من إنشاء ${quantity} دعوة؟`;
            if (generateQR) {
                message += '\n\nتوليد أكواد QR قد يستغرق وقتاً طويلاً.';
            }
            
            return confirm(message);
        }
        
        // إعادة تعيين النموذج
        function resetForm() {
            document.getElementById('bulkForm').reset();
            updatePreview();
        }
        
        // إخفاء/إظهار النتائج التفصيلية
        function toggleResults() {
            const results = document.getElementById('detailedResults');
            const btn = document.getElementById('toggleBtn');
            
            if (results.style.display === 'none') {
                results.style.display = 'block';
                btn.textContent = '👁️ إخفاء التفاصيل';
            } else {
                results.style.display = 'none';
                btn.textContent = '👁️ إظهار التفاصيل';
            }
        }
        
        // تحديث المعاينة عند تغيير القيم
        document.getElementById('quantity').addEventListener('input', updatePreview);
        document.getElementById('prefix').addEventListener('input', updatePreview);
        
        // تحديث المعاينة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', updatePreview);
    </script>
</body>
</html>
