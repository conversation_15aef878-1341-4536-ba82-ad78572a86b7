/**
 * ماسح QR Code متقدم
 * يدعم مسح QR Code والباركود العادي
 */

class QRScanner {
    constructor(options = {}) {
        this.videoElement = null;
        this.canvasElement = null;
        this.canvasContext = null;
        this.stream = null;
        this.isScanning = false;
        this.cameras = [];
        this.currentCameraIndex = 0;
        this.onScanSuccess = options.onScanSuccess || function() {};
        this.onScanError = options.onScanError || function() {};
        this.scanInterval = null;
        
        this.init();
    }
    
    async init() {
        try {
            // الحصول على قائمة الكاميرات
            await this.getCameras();
        } catch (error) {
            console.error('خطأ في تهيئة الماسح:', error);
        }
    }
    
    async getCameras() {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            this.cameras = devices.filter(device => device.kind === 'videoinput');
            return this.cameras;
        } catch (error) {
            console.error('خطأ في الحصول على الكاميرات:', error);
            return [];
        }
    }
    
    async startScanning(containerElement) {
        try {
            // إنشاء عناصر الفيديو والكانفاس
            this.createElements(containerElement);
            
            // بدء الكاميرا
            await this.startCamera();
            
            // بدء المسح
            this.startScanLoop();
            
            this.isScanning = true;
            
        } catch (error) {
            console.error('خطأ في بدء المسح:', error);
            this.onScanError('فشل في بدء المسح: ' + error.message);
        }
    }
    
    createElements(container) {
        // مسح المحتوى السابق
        container.innerHTML = '';
        
        // إنشاء عنصر الفيديو
        this.videoElement = document.createElement('video');
        this.videoElement.style.width = '100%';
        this.videoElement.style.height = 'auto';
        this.videoElement.style.borderRadius = '10px';
        this.videoElement.autoplay = true;
        this.videoElement.muted = true;
        this.videoElement.playsInline = true;
        
        // إنشاء عنصر الكانفاس (مخفي)
        this.canvasElement = document.createElement('canvas');
        this.canvasElement.style.display = 'none';
        this.canvasContext = this.canvasElement.getContext('2d');
        
        // إضافة العناصر للحاوي
        container.appendChild(this.videoElement);
        container.appendChild(this.canvasElement);
        
        // إضافة إطار المسح
        this.addScanFrame(container);
    }
    
    addScanFrame(container) {
        const frame = document.createElement('div');
        frame.className = 'qr-scan-frame';
        frame.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 250px;
            height: 250px;
            border: 2px solid #00ff00;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
            pointer-events: none;
        `;
        
        // إضافة زوايا الإطار
        const corners = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];
        corners.forEach(corner => {
            const cornerElement = document.createElement('div');
            cornerElement.className = `corner ${corner}`;
            cornerElement.style.cssText = `
                position: absolute;
                width: 30px;
                height: 30px;
                border: 3px solid #00ff00;
            `;
            
            switch(corner) {
                case 'top-left':
                    cornerElement.style.top = '-3px';
                    cornerElement.style.left = '-3px';
                    cornerElement.style.borderRight = 'none';
                    cornerElement.style.borderBottom = 'none';
                    break;
                case 'top-right':
                    cornerElement.style.top = '-3px';
                    cornerElement.style.right = '-3px';
                    cornerElement.style.borderLeft = 'none';
                    cornerElement.style.borderBottom = 'none';
                    break;
                case 'bottom-left':
                    cornerElement.style.bottom = '-3px';
                    cornerElement.style.left = '-3px';
                    cornerElement.style.borderRight = 'none';
                    cornerElement.style.borderTop = 'none';
                    break;
                case 'bottom-right':
                    cornerElement.style.bottom = '-3px';
                    cornerElement.style.right = '-3px';
                    cornerElement.style.borderLeft = 'none';
                    cornerElement.style.borderTop = 'none';
                    break;
            }
            
            frame.appendChild(cornerElement);
        });
        
        // إضافة خط المسح المتحرك
        const scanLine = document.createElement('div');
        scanLine.style.cssText = `
            position: absolute;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00ff00, transparent);
            animation: qr-scan 2s linear infinite;
        `;
        
        // إضافة CSS للحركة
        if (!document.getElementById('qr-scan-styles')) {
            const style = document.createElement('style');
            style.id = 'qr-scan-styles';
            style.textContent = `
                @keyframes qr-scan {
                    0% { top: 0%; }
                    100% { top: 100%; }
                }
            `;
            document.head.appendChild(style);
        }
        
        frame.appendChild(scanLine);
        
        // جعل الحاوي نسبي لوضع الإطار
        container.style.position = 'relative';
        container.appendChild(frame);
    }
    
    async startCamera() {
        try {
            // إعدادات الكاميرا
            const constraints = {
                video: {
                    facingMode: this.cameras.length > 1 ? 'environment' : 'user',
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                }
            };
            
            // استخدام كاميرا محددة إذا كانت متاحة
            if (this.cameras[this.currentCameraIndex]) {
                constraints.video.deviceId = this.cameras[this.currentCameraIndex].deviceId;
            }
            
            // الحصول على تدفق الكاميرا
            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            this.videoElement.srcObject = this.stream;
            
            // انتظار تحميل الفيديو
            await new Promise((resolve) => {
                this.videoElement.onloadedmetadata = resolve;
            });
            
            // تشغيل الفيديو
            await this.videoElement.play();
            
        } catch (error) {
            throw new Error('فشل في تشغيل الكاميرا: ' + error.message);
        }
    }
    
    startScanLoop() {
        this.scanInterval = setInterval(() => {
            this.scanFrame();
        }, 100); // مسح كل 100ms
    }
    
    scanFrame() {
        if (!this.videoElement || !this.canvasElement || !this.isScanning) {
            return;
        }
        
        try {
            // تحديث حجم الكانفاس
            this.canvasElement.width = this.videoElement.videoWidth;
            this.canvasElement.height = this.videoElement.videoHeight;
            
            // رسم الإطار الحالي على الكانفاس
            this.canvasContext.drawImage(
                this.videoElement, 
                0, 0, 
                this.canvasElement.width, 
                this.canvasElement.height
            );
            
            // الحصول على بيانات الصورة
            const imageData = this.canvasContext.getImageData(
                0, 0, 
                this.canvasElement.width, 
                this.canvasElement.height
            );
            
            // محاولة مسح QR Code
            if (window.jsQR) {
                const code = jsQR(imageData.data, imageData.width, imageData.height);
                if (code) {
                    this.onCodeDetected(code.data);
                    return;
                }
            }
            
        } catch (error) {
            console.error('خطأ في مسح الإطار:', error);
        }
    }
    
    onCodeDetected(code) {
        console.log('تم مسح الكود:', code);
        
        // إيقاف المسح مؤقتاً
        this.stopScanning();
        
        // استدعاء دالة النجاح
        this.onScanSuccess(code);
    }
    
    stopScanning() {
        this.isScanning = false;
        
        // إيقاف حلقة المسح
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
            this.scanInterval = null;
        }
        
        // إيقاف الكاميرا
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }
        
        // مسح عناصر الفيديو
        if (this.videoElement) {
            this.videoElement.srcObject = null;
        }
    }
    
    async switchCamera() {
        if (this.cameras.length > 1) {
            this.currentCameraIndex = (this.currentCameraIndex + 1) % this.cameras.length;
            
            if (this.isScanning) {
                // إعادة تشغيل المسح بالكاميرا الجديدة
                this.stopScanning();
                await this.startCamera();
                this.startScanLoop();
                this.isScanning = true;
            }
        }
    }
    
    // تنظيف الموارد
    destroy() {
        this.stopScanning();
        
        if (this.videoElement) {
            this.videoElement.remove();
        }
        
        if (this.canvasElement) {
            this.canvasElement.remove();
        }
    }
}

// تصدير الكلاس للاستخدام العام
window.QRScanner = QRScanner;
