# 🔧 حل مشاكل أكواد QR

## ❓ المشكلة الأساسية

عند إضافة ضيف جديد، قد تظهر رسالة:
```
تم إضافة الضيف ولكن فشل في إنشاء كود QR
```

## 🔍 أسباب المشكلة

1. **عدم وجود اتصال بالإنترنت** - النظام يحتاج الإنترنت لتوليد أكواد QR
2. **حجب الخدمات الخارجية** - بعض الشبكات تحجب Google Charts API
3. **مشاكل في الخادم** - خدمات توليد QR قد تكون معطلة مؤقتاً
4. **مشاكل في صلاحيات المجلد** - مجلد qrcodes غير قابل للكتابة

## ✅ الحلول المتوفرة

### 1️⃣ الحل التلقائي (تم تطبيقه)

النظام الآن يحاول عدة طرق لتوليد أكواد QR:

- **الطريقة الأولى:** QR Server API
- **الطريقة الثانية:** Google Charts API  
- **الطريقة الثالثة:** QuickChart API
- **الطريقة الرابعة:** إنشاء ملف HTML تفاعلي

### 2️⃣ استخدام مولد QR المحسن

يمكنك الوصول إلى مولد QR المحسن عبر:
```
http://localhost/اسم_المجلد/qr_generator.php?code=كود_الضيف&url=رابط_التحقق
```

### 3️⃣ إعادة توليد أكواد QR المفقودة

- اذهب إلى لوحة التحكم
- اضغط على "🔄 إعادة توليد QR"
- سيتم محاولة إعادة توليد جميع الأكواد المفقودة

### 4️⃣ استخدام الروابط مباشرة

حتى بدون أكواد QR، الضيوف يمكنهم:
- استخدام الرابط المطبوع على الدعوة
- إدخال الكود يدوياً في الموقع

## 🛠️ خطوات الإصلاح اليدوي

### 1. تحقق من صلاحيات المجلد

```bash
# في Windows (Command Prompt)
icacls "C:\xampp\htdocs\دعوات حظور\qrcodes" /grant Everyone:F

# أو في PowerShell
Set-ItemProperty -Path "C:\xampp\htdocs\دعوات حظور\qrcodes" -Name IsReadOnly -Value $false
```

### 2. تحقق من الاتصال بالإنترنت

افتح المتصفح واذهب إلى:
```
https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=test
```

إذا ظهرت صورة QR، فالاتصال يعمل.

### 3. اختبار النظام

اذهب إلى:
```
http://localhost/اسم_المجلد/test_system.php
```

### 4. إنشاء QR يدوياً

يمكنك استخدام أي موقع لتوليد QR مثل:
- https://www.qr-code-generator.com/
- https://qr.io/
- https://www.the-qrcode-generator.com/

## 📱 بدائل أكواد QR

### 1. الروابط المباشرة
```
http://localhost/اسم_المجلد/verify.php?code=كود_الضيف
```

### 2. الروابط المختصرة
يمكنك استخدام خدمات تقصير الروابط مثل:
- bit.ly
- tinyurl.com
- t.ly

### 3. أكواد نصية
النظام ينشئ ملفات نصية تحتوي على:
- كود الدعوة
- رابط التحقق
- تاريخ الإنشاء

## 🔄 إعادة التشغيل

إذا استمرت المشاكل:

1. **أعد تشغيل XAMPP**
2. **امسح مجلد qrcodes وأعد إنشاءه**
3. **تأكد من إعدادات الشبكة**
4. **جرب من متصفح مختلف**

## 📞 الدعم الفني

إذا لم تنجح الحلول أعلاه:

1. تحقق من ملف `test_system.php`
2. راجع رسائل الخطأ في PHP
3. تأكد من إعدادات قاعدة البيانات
4. جرب إنشاء ضيف جديد

## ✨ المميزات الجديدة

بعد التحديث، النظام يدعم:

- ✅ **عدة طرق لتوليد QR**
- ✅ **أكواد QR بصيغة HTML تفاعلية**
- ✅ **إعادة توليد الأكواد المفقودة**
- ✅ **عرض محسن للأكواد في لوحة التحكم**
- ✅ **روابط احتياطية دائماً متوفرة**

---

**💡 تذكر:** حتى لو فشل توليد كود QR، النظام سيعمل بشكل طبيعي والضيوف يمكنهم استخدام الروابط المباشرة!
