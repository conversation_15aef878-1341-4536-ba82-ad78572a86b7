<?php
require_once 'config.php';

// التحقق من تسجيل دخول المسؤول
checkAdminAuth();

$pdo = getDBConnection();
$message = '';

// معالجة حذف ضيف
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $guestId = (int)$_GET['delete'];
    
    try {
        // حذف ملف QR إذا كان موجوداً
        $stmt = $pdo->prepare("SELECT code FROM guests WHERE id = ?");
        $stmt->execute([$guestId]);
        $guest = $stmt->fetch();
        
        if ($guest) {
            $qrFile = "qrcodes/{$guest['code']}.png";
            if (file_exists($qrFile)) {
                unlink($qrFile);
            }
            
            // حذف الضيف من قاعدة البيانات
            $stmt = $pdo->prepare("DELETE FROM guests WHERE id = ?");
            $stmt->execute([$guestId]);
            
            $message = showAlert('تم حذف الضيف بنجاح', 'success');
        }
    } catch (PDOException $e) {
        $message = showAlert('خطأ في حذف الضيف: ' . $e->getMessage(), 'error');
    }
}

// جلب إحصائيات
try {
    $totalGuests = $pdo->query("SELECT COUNT(*) FROM guests")->fetchColumn();
    $scannedGuests = $pdo->query("SELECT COUNT(*) FROM guests WHERE scanned = 1")->fetchColumn();
    $pendingGuests = $totalGuests - $scannedGuests;
} catch (PDOException $e) {
    $totalGuests = $scannedGuests = $pendingGuests = 0;
}

// جلب قائمة الضيوف
$searchTerm = sanitizeInput($_GET['search'] ?? '');
$whereClause = '';
$params = [];

if (!empty($searchTerm)) {
    $whereClause = "WHERE name LIKE ? OR phone LIKE ?";
    $params = ["%{$searchTerm}%", "%{$searchTerm}%"];
}

try {
    $sql = "SELECT * FROM guests {$whereClause} ORDER BY created_at DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $guests = $stmt->fetchAll();
} catch (PDOException $e) {
    $guests = [];
    $message = showAlert('خطأ في جلب البيانات: ' . $e->getMessage(), 'error');
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>🎫 لوحة تحكم الدعوات</h1>
                <div class="header-actions">
                    <span>مرحباً، <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                    <a href="logout.php" class="btn btn-secondary">تسجيل الخروج</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <!-- الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-info">
                    <h3><?php echo $totalGuests; ?></h3>
                    <p>إجمالي المدعوين</p>
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon">✅</div>
                <div class="stat-info">
                    <h3><?php echo $scannedGuests; ?></h3>
                    <p>تم الحضور</p>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">⏳</div>
                <div class="stat-info">
                    <h3><?php echo $pendingGuests; ?></h3>
                    <p>لم يحضر بعد</p>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم -->
        <div class="controls">
            <div class="controls-left">
                <a href="add_guest.php" class="btn btn-primary">
                    ➕ إضافة ضيف جديد
                </a>
                <a href="bulk_invitations.php" class="btn btn-success">
                    📦 إنشاء دعوات متعددة
                </a>
                <a href="manage_bulk.php" class="btn btn-info">
                    📊 إدارة المجموعات
                </a>
                <a href="export.php" class="btn btn-info">
                    📥 تصدير البيانات
                </a>
                <a href="regenerate_qr.php" class="btn btn-warning">
                    🔄 إعادة توليد QR
                </a>
                <a href="print_invitations.php" target="_blank" class="btn btn-secondary">
                    🖨️ طباعة الدعوات
                </a>
            </div>
            <div class="controls-right">
                <form method="GET" class="search-form">
                    <input type="text" name="search" placeholder="البحث بالاسم أو رقم الجوال..." 
                           value="<?php echo htmlspecialchars($searchTerm); ?>">
                    <button type="submit" class="btn btn-secondary">🔍 بحث</button>
                    <?php if (!empty($searchTerm)): ?>
                        <a href="dashboard.php" class="btn btn-light">✖️ إلغاء</a>
                    <?php endif; ?>
                </form>
            </div>
        </div>

        <!-- جدول الضيوف -->
        <div class="table-container">
            <table class="guests-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>رقم الجوال</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                        <th>تاريخ الحضور</th>
                        <th>كود QR</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($guests)): ?>
                        <tr>
                            <td colspan="8" class="no-data">
                                <?php echo empty($searchTerm) ? 'لا توجد دعوات مضافة بعد' : 'لا توجد نتائج للبحث'; ?>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($guests as $index => $guest): ?>
                            <tr class="<?php echo $guest['scanned'] ? 'scanned' : ''; ?>">
                                <td><?php echo $index + 1; ?></td>
                                <td class="guest-name"><?php echo htmlspecialchars($guest['name']); ?></td>
                                <td><?php echo htmlspecialchars($guest['phone'] ?? '-'); ?></td>
                                <td>
                                    <?php if ($guest['scanned']): ?>
                                        <span class="status-badge success">✅ حضر</span>
                                    <?php else: ?>
                                        <span class="status-badge pending">⏳ لم يحضر</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($guest['created_at'])); ?></td>
                                <td>
                                    <?php echo $guest['scanned_at'] ? date('Y-m-d H:i', strtotime($guest['scanned_at'])) : '-'; ?>
                                </td>
                                <td>
                                    <?php
                                    $qrFile = "qrcodes/{$guest['code']}.png";
                                    $qrHtmlFile = "qrcodes/{$guest['code']}.html";

                                    if (file_exists($qrFile)): ?>
                                        <a href="<?php echo $qrFile; ?>" target="_blank" class="btn btn-sm btn-info">
                                            📱 عرض QR
                                        </a>
                                    <?php elseif (file_exists($qrHtmlFile)): ?>
                                        <a href="<?php echo $qrHtmlFile; ?>" target="_blank" class="btn btn-sm btn-success">
                                            🔗 عرض QR
                                        </a>
                                    <?php else: ?>
                                        <a href="qr_generator.php?code=<?php echo $guest['code']; ?>&url=<?php echo urlencode(SITE_URL . "/verify.php?code=" . $guest['code']); ?>"
                                           target="_blank" class="btn btn-sm btn-warning">
                                            ⚡ إنشاء QR
                                        </a>
                                    <?php endif; ?>
                                </td>
                                <td class="actions">
                                    <a href="edit_guest.php?id=<?php echo $guest['id']; ?>" 
                                       class="btn btn-sm btn-warning">✏️ تعديل</a>
                                    <a href="?delete=<?php echo $guest['id']; ?>" 
                                       class="btn btn-sm btn-danger"
                                       onclick="return confirm('هل أنت متأكد من حذف هذا الضيف؟')">🗑️ حذف</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
