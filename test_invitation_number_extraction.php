<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار استخراج رقم الإجازة من QR Code</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .example-box {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .result-box {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        
        .warning-box {
            background: #fff3cd;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        
        .code {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 5px;
            color: #495057;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .demo-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            margin: 10px 0;
        }
        
        .demo-output {
            background: #f8f9fa;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-weight: bold;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار استخراج رقم الإجازة من QR Code</h1>
        <p>هذه الصفحة توضح كيف يعمل النظام الجديد لاستخراج رقم الإجازة من QR Code</p>
        
        <div class="example-box">
            <h3>🎯 كيف يعمل النظام الآن:</h3>
            <ol>
                <li><strong>إنشاء QR Code:</strong> يحتوي على رابط كامل مثل <span class="code">http://localhost/verify.php?code=INV_123...</span></li>
                <li><strong>مسح QR Code:</strong> الكاميرا تقرأ الرابط الكامل</li>
                <li><strong>استخراج رقم الإجازة:</strong> النظام يستخرج <span class="code">INV_123...</span> من الرابط</li>
                <li><strong>عرض في الحقل:</strong> يظهر رقم الإجازة فقط في حقل الإدخال</li>
                <li><strong>تسجيل الحضور:</strong> يتم البحث والتسجيل باستخدام رقم الإجازة</li>
            </ol>
        </div>
        
        <div class="warning-box">
            <h3>⚠️ ملاحظة مهمة:</h3>
            <p>عند مسح QR Code بالكاميرا، ستحصل على <strong>رقم الإجازة فقط</strong> في حقل الإدخال، وليس الرابط الكامل.</p>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🔍 اختبار تفاعلي لاستخراج رقم الإجازة</h2>
        
        <div id="demoSection">
            <h3>جرب بنفسك:</h3>
            <p>الصق رابط QR Code في الحقل أدناه لترى كيف يتم استخراج رقم الإجازة:</p>
            
            <input type="text" id="demoInput" class="demo-input" 
                   placeholder="الصق رابط QR Code هنا (مثل: http://localhost/verify.php?code=INV_123...)"
                   oninput="extractDemo()">
            
            <div id="demoOutput" class="demo-output" style="display: none;">
                رقم الإجازة المستخرج: <span id="extractedCode"></span>
            </div>
        </div>
        
        <h3>📋 أمثلة للاختبار:</h3>
        
        <div class="example-box">
            <h4>✅ روابط QR Code صحيحة:</h4>
            <div style="font-family: monospace; font-size: 12px; line-height: 1.6;">
                <div onclick="testExample(this.textContent)" style="cursor: pointer; padding: 5px; background: #e9ecef; margin: 5px 0; border-radius: 3px;">
                    http://localhost/دعوات حظور/verify.php?code=INV_685CC13E3FBCE_4786
                </div>
                <div onclick="testExample(this.textContent)" style="cursor: pointer; padding: 5px; background: #e9ecef; margin: 5px 0; border-radius: 3px;">
                    https://example.com/verify.php?code=INV_685CC2AC95E90_6360
                </div>
                <div onclick="testExample(this.textContent)" style="cursor: pointer; padding: 5px; background: #e9ecef; margin: 5px 0; border-radius: 3px;">
                    verify.php?code=INV_685CC3899E412_4694
                </div>
                <div onclick="testExample(this.textContent)" style="cursor: pointer; padding: 5px; background: #e9ecef; margin: 5px 0; border-radius: 3px;">
                    code=INV_TEST_123456789_0000
                </div>
            </div>
            <p><small>💡 اضغط على أي مثال لاختباره</small></p>
        </div>
        
        <div class="example-box">
            <h4>📱 أرقام إجازة مباشرة:</h4>
            <div style="font-family: monospace; font-size: 14px; line-height: 1.6;">
                <div onclick="testExample(this.textContent)" style="cursor: pointer; padding: 5px; background: #e9ecef; margin: 5px 0; border-radius: 3px;">
                    INV_685CC13E3FBCE_4786
                </div>
                <div onclick="testExample(this.textContent)" style="cursor: pointer; padding: 5px; background: #e9ecef; margin: 5px 0; border-radius: 3px;">
                    INV_685CC2AC95E90_6360
                </div>
                <div onclick="testExample(this.textContent)" style="cursor: pointer; padding: 5px; background: #e9ecef; margin: 5px 0; border-radius: 3px;">
                    INV_TEST_123456789_0000
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🎨 المؤشرات البصرية في لوحة الموظف</h2>
        
        <div class="result-box">
            <h4>عند مسح QR Code بالكاميرا:</h4>
            <p><strong>ما يحدث:</strong></p>
            <ol>
                <li>الكاميرا تقرأ: <span class="code">http://localhost/verify.php?code=INV_123...</span></li>
                <li>النظام يستخرج: <span class="code">INV_123...</span></li>
                <li>يظهر في الحقل: <span class="code">INV_123...</span> فقط</li>
                <li>رسالة الحالة: <span style="color: #28a745;">"✅ تم مسح رابط واستخراج رقم الإجازة: INV_123..."</span></li>
            </ol>
        </div>
        
        <div class="result-box">
            <h4>عند الإدخال اليدوي:</h4>
            <p><strong>ما يحدث:</strong></p>
            <ol>
                <li>المستخدم يكتب أو يلصق: أي نوع من الإدخال</li>
                <li>النظام يكتشف النوع ويستخرج رقم الإجازة</li>
                <li>يظهر معاينة للرقم المستخرج</li>
                <li>رسالة الحالة توضح نوع الإدخال</li>
            </ol>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🔗 روابط مفيدة</h2>
        <div style="text-align: center;">
            <a href="employee_dashboard.php" class="btn btn-primary">📱 لوحة تحكم الموظف</a>
            <a href="test_camera.php" class="btn btn-info">📷 اختبار الكاميرا</a>
            <a href="test_code_extraction.php" class="btn btn-success">🔍 اختبار استخراج الكود</a>
        </div>
    </div>

    <script>
        // وظيفة استخراج رقم الإجازة (نفس الوظيفة في النظام)
        function extractCodeFromScannedData(scannedData) {
            const data = scannedData.trim();
            
            // إذا كان رابطاً، استخرج الكود من المعامل
            if (data.includes('verify.php?code=')) {
                const match = data.match(/code=([^&\s]+)/);
                if (match) {
                    return match[1];
                }
            }
            
            // إذا كان رابطاً بصيغة أخرى، حاول استخراج الكود
            if (data.includes('code=')) {
                const match = data.match(/code=([^&\s]+)/);
                if (match) {
                    return match[1];
                }
            }
            
            // إذا كان كود مباشر، أرجعه كما هو
            return data;
        }
        
        // اختبار تفاعلي
        function extractDemo() {
            const input = document.getElementById('demoInput').value;
            const output = document.getElementById('demoOutput');
            const extractedCode = document.getElementById('extractedCode');
            
            if (input.trim()) {
                const result = extractCodeFromScannedData(input);
                extractedCode.textContent = result;
                output.style.display = 'block';
                
                // تلوين حسب النتيجة
                if (result.startsWith('INV_')) {
                    output.style.borderColor = '#28a745';
                    output.style.background = '#d4edda';
                    output.style.color = '#155724';
                } else {
                    output.style.borderColor = '#dc3545';
                    output.style.background = '#f8d7da';
                    output.style.color = '#721c24';
                }
            } else {
                output.style.display = 'none';
            }
        }
        
        // اختبار مثال
        function testExample(example) {
            document.getElementById('demoInput').value = example;
            extractDemo();
            
            // تمرير إلى منطقة النتيجة
            document.getElementById('demoOutput').scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
