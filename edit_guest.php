<?php
require_once 'config.php';

// التحقق من تسجيل الدخول (مشرف أو مدير)
requireSupervisor();

$pdo = getDBConnection();
$message = '';
$guest = null;

// معالجة رسالة من URL
if (isset($_GET['message'])) {
    $message = showAlert(htmlspecialchars($_GET['message']), 'success');
}

// التحقق من معرف الضيف
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: supervisor_dashboard.php');
    exit();
}

$guestId = (int)$_GET['id'];

// جلب بيانات الضيف
try {
    if (isAdmin()) {
        // المدير يمكنه تعديل أي ضيف
        $stmt = $pdo->prepare("
            SELECT g.*, u.full_name as created_by_name 
            FROM guests g 
            LEFT JOIN users u ON g.created_by = u.id 
            WHERE g.id = ?
        ");
        $stmt->execute([$guestId]);
    } else {
        // المشرف يمكنه تعديل ضيوفه فقط
        $stmt = $pdo->prepare("
            SELECT g.*, u.full_name as created_by_name 
            FROM guests g 
            LEFT JOIN users u ON g.created_by = u.id 
            WHERE g.id = ? AND g.created_by = ?
        ");
        $stmt->execute([$guestId, $_SESSION['user_id']]);
    }
    
    $guest = $stmt->fetch();
    
    if (!$guest) {
        $message = showAlert('الضيف غير موجود أو ليس لديك صلاحية لتعديله', 'error');
    }
} catch (PDOException $e) {
    $message = showAlert('خطأ في جلب بيانات الضيف: ' . $e->getMessage(), 'error');
}

// معالجة تحديث بيانات الضيف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $guest) {
    $name = trim($_POST['name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($name)) {
        $message = showAlert('اسم الضيف مطلوب', 'error');
    } else {
        try {
            // تحديث بيانات الضيف
            $stmt = $pdo->prepare("
                UPDATE guests 
                SET name = ?, phone = ?, notes = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $success = $stmt->execute([$name, $phone, $notes, $guestId]);
            
            if ($success) {
                // تحديث بيانات الضيف المحلية
                $guest['name'] = $name;
                $guest['phone'] = $phone;
                $guest['notes'] = $notes;
                
                // تسجيل النشاط
                try {
                    logActivity('update_guest', 'guest', $guestId, "تم تحديث بيانات الضيف: {$name}");
                } catch (Exception $e) {
                    // تجاهل خطأ التسجيل
                }
                
                $message = showAlert("تم تحديث بيانات الضيف بنجاح!", 'success');
            } else {
                $message = showAlert('حدث خطأ أثناء تحديث البيانات، حاول مرة أخرى', 'error');
            }
        } catch (PDOException $e) {
            $message = showAlert('خطأ في قاعدة البيانات: ' . $e->getMessage(), 'error');
        }
    }
}

// معالجة إعادة تعيين حالة المسح
if (isset($_GET['reset_scan']) && $guest) {
    try {
        // التحقق من صلاحية إعادة التعيين
        if (isAdmin() || $guest['created_by'] == $_SESSION['user_id']) {
            $stmt = $pdo->prepare("
                UPDATE guests
                SET scanned = 0, scanned_at = NULL, scanned_by = NULL, updated_at = NOW()
                WHERE id = ?
            ");
            $success = $stmt->execute([$guestId]);

            if ($success) {
                // تحديث بيانات الضيف المحلية
                $guest['scanned'] = 0;
                $guest['scanned_at'] = null;
                $guest['scanned_by'] = null;

                // تسجيل النشاط
                try {
                    logActivity('reset_guest_scan', 'guest', $guestId, "تم إعادة تعيين حالة المسح للضيف: {$guest['name']}");
                } catch (Exception $e) {
                    // تجاهل خطأ التسجيل
                }

                $message = showAlert("تم إعادة تعيين حالة المسح بنجاح! يمكن للضيف الحضور مرة أخرى", 'success');
            } else {
                $message = showAlert('حدث خطأ أثناء إعادة تعيين حالة المسح', 'error');
            }
        } else {
            $message = showAlert('ليس لديك صلاحية لإعادة تعيين حالة هذا الضيف', 'error');
        }
    } catch (PDOException $e) {
        $message = showAlert('خطأ في قاعدة البيانات: ' . $e->getMessage(), 'error');
    }
}

// معالجة حذف الضيف
if (isset($_GET['delete']) && $guest) {
    try {
        // التحقق من صلاحية الحذف
        if (isAdmin() || $guest['created_by'] == $_SESSION['user_id']) {
            // حذف ملف QR إن وجد
            $qrFile = "qrcodes/{$guest['code']}.png";
            $qrHtmlFile = "qrcodes/{$guest['code']}.html";
            if (file_exists($qrFile)) {
                unlink($qrFile);
            }
            if (file_exists($qrHtmlFile)) {
                unlink($qrHtmlFile);
            }

            // حذف الضيف من قاعدة البيانات
            $stmt = $pdo->prepare("DELETE FROM guests WHERE id = ?");
            $success = $stmt->execute([$guestId]);

            if ($success) {
                // تسجيل النشاط
                try {
                    logActivity('delete_guest', 'guest', $guestId, "تم حذف الضيف: {$guest['name']}");
                } catch (Exception $e) {
                    // تجاهل خطأ التسجيل
                }

                // إعادة توجيه للوحة التحكم
                header('Location: supervisor_dashboard.php?message=' . urlencode('تم حذف الضيف بنجاح'));
                exit();
            } else {
                $message = showAlert('حدث خطأ أثناء حذف الضيف', 'error');
            }
        } else {
            $message = showAlert('ليس لديك صلاحية لحذف هذا الضيف', 'error');
        }
    } catch (PDOException $e) {
        $message = showAlert('خطأ في قاعدة البيانات: ' . $e->getMessage(), 'error');
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل بيانات الضيف</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .form-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        .form-group.required label::after {
            content: " *";
            color: #dc3545;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .guest-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .guest-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-scanned {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .qr-section {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>✏️ تعديل بيانات الضيف</h1>
                <div class="header-actions">
                    <span>مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                    <a href="supervisor_dashboard.php" class="btn btn-secondary">← العودة للوحة المشرف</a>
                    <a href="logout.php" class="btn btn-secondary">تسجيل الخروج</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <?php if ($guest): ?>
            <!-- معلومات الضيف الحالية -->
            <div class="guest-info">
                <h3>📋 المعلومات الحالية</h3>
                <div class="form-row">
                    <div>
                        <p><strong>الاسم:</strong> <?php echo htmlspecialchars($guest['name']); ?></p>
                        <p><strong>رقم الجوال:</strong> <?php echo htmlspecialchars($guest['phone'] ?: 'غير محدد'); ?></p>
                        <p><strong>كود الدعوة:</strong> <code><?php echo htmlspecialchars($guest['code']); ?></code></p>
                    </div>
                    <div>
                        <p><strong>الحالة:</strong> 
                            <?php if ($guest['scanned']): ?>
                                <span class="guest-status status-scanned">✅ حضر</span>
                            <?php else: ?>
                                <span class="guest-status status-pending">⏳ لم يحضر</span>
                            <?php endif; ?>
                        </p>
                        <p><strong>أنشأه:</strong> <?php echo htmlspecialchars($guest['created_by_name'] ?: 'غير محدد'); ?></p>
                        <p><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d H:i', strtotime($guest['created_at'])); ?></p>
                        <?php if ($guest['scanned']): ?>
                            <p><strong>وقت الحضور:</strong> <?php echo date('Y-m-d H:i', strtotime($guest['scanned_at'])); ?></p>
                            <?php if ($guest['scanned_by']): ?>
                                <?php
                                // جلب اسم الموظف الذي قام بالمسح
                                try {
                                    $stmt = $pdo->prepare("SELECT full_name FROM users WHERE id = ?");
                                    $stmt->execute([$guest['scanned_by']]);
                                    $scannedByUser = $stmt->fetch();
                                    if ($scannedByUser) {
                                        echo "<p><strong>مسجل بواسطة:</strong> " . htmlspecialchars($scannedByUser['full_name']) . "</p>";
                                    }
                                } catch (PDOException $e) {
                                    // تجاهل الخطأ
                                }
                                ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- QR Code -->
            <div class="qr-section">
                <h4>📱 كود QR للدعوة</h4>
                <?php 
                $qrFile = "qrcodes/{$guest['code']}.png";
                $qrHtmlFile = "qrcodes/{$guest['code']}.html";
                
                if (file_exists($qrFile)): ?>
                    <p>
                        <a href="<?php echo $qrFile; ?>" target="_blank" class="btn btn-info">
                            📱 عرض QR Code
                        </a>
                    </p>
                <?php elseif (file_exists($qrHtmlFile)): ?>
                    <p>
                        <a href="<?php echo $qrHtmlFile; ?>" target="_blank" class="btn btn-success">
                            🔗 عرض QR Code
                        </a>
                    </p>
                <?php else: ?>
                    <p>
                        <a href="qr_generator.php?code=<?php echo $guest['code']; ?>&url=<?php echo urlencode(SITE_URL . "/verify.php?code=" . $guest['code']); ?>" 
                           target="_blank" class="btn btn-warning">
                            ⚡ إنشاء QR Code
                        </a>
                    </p>
                <?php endif; ?>
            </div>

            <!-- نموذج تعديل الضيف -->
            <div class="form-container">
                <h2>📝 تحديث البيانات</h2>
                <form method="POST" action="">
                    <div class="form-row">
                        <div class="form-group required">
                            <label for="name">اسم الضيف</label>
                            <input type="text" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($guest['name']); ?>" 
                                   required maxlength="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">رقم الجوال</label>
                            <input type="tel" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($guest['phone'] ?? ''); ?>" 
                                   maxlength="20">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">ملاحظات</label>
                        <textarea id="notes" name="notes" rows="4" maxlength="500"><?php echo htmlspecialchars($guest['notes'] ?? ''); ?></textarea>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" class="btn btn-primary">
                            💾 حفظ التحديثات
                        </button>
                        <a href="supervisor_dashboard.php" class="btn btn-secondary">
                            ❌ إلغاء
                        </a>
                    </div>
                </form>
            </div>

            <!-- إجراءات إضافية -->
            <?php if (isAdmin() || $guest['created_by'] == $_SESSION['user_id']): ?>
                <div class="form-container">
                    <h2>⚙️ إجراءات إضافية</h2>
                    <div style="text-align: center;">
                        <?php if ($guest['scanned']): ?>
                            <a href="?id=<?php echo $guest['id']; ?>&reset_scan=1"
                               class="btn btn-warning"
                               onclick="return confirm('هل أنت متأكد من إعادة تعيين حالة المسح؟\nسيتمكن الضيف من الحضور مرة أخرى!')">
                                🔄 إعادة تعيين حالة المسح
                            </a>
                        <?php endif; ?>

                        <a href="regenerate_qr.php?guest_id=<?php echo $guest['id']; ?>"
                           class="btn btn-info"
                           onclick="return confirm('هل تريد إعادة توليد QR Code لهذا الضيف؟')">
                            🔄 إعادة توليد QR
                        </a>

                        <a href="print_invitations.php?guest_id=<?php echo $guest['id']; ?>"
                           target="_blank" class="btn btn-success">
                            🖨️ طباعة الدعوة
                        </a>

                        <a href="?id=<?php echo $guest['id']; ?>&delete=1"
                           class="btn btn-danger"
                           onclick="return confirm('هل أنت متأكد من حذف هذا الضيف؟\nسيتم حذف الدعوة وملف QR نهائياً!')">
                            🗑️ حذف الضيف
                        </a>
                    </div>
                </div>
            <?php endif; ?>
                </form>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: 40px;">
                <h3>❌ الضيف غير موجود</h3>
                <p>الضيف المطلوب غير موجود أو ليس لديك صلاحية للوصول إليه</p>
                <a href="supervisor_dashboard.php" class="btn btn-primary">العودة للوحة المشرف</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
