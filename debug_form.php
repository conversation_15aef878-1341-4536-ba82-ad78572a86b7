<?php
require_once 'config.php';

// التحقق من صلاحيات المدير
requireAdmin();

$message = '';
$debug_output = '';

// تشخيص النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $debug_output .= "<h3>🔍 تشخيص البيانات المرسلة:</h3>";
    $debug_output .= "<pre>" . print_r($_POST, true) . "</pre>";
    
    if (isset($_POST['create_user'])) {
        $debug_output .= "<p><strong>✅ تم العثور على create_user في POST</strong></p>";
        
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $full_name = sanitizeInput($_POST['full_name'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $phone = sanitizeInput($_POST['phone'] ?? '');
        $user_type = sanitizeInput($_POST['user_type'] ?? '');
        
        $debug_output .= "<h4>البيانات المنظفة:</h4>";
        $debug_output .= "<ul>";
        $debug_output .= "<li><strong>اسم المستخدم:</strong> '{$username}' (طول: " . strlen($username) . ")</li>";
        $debug_output .= "<li><strong>كلمة المرور:</strong> " . (empty($password) ? 'فارغة' : 'موجودة (طول: ' . strlen($password) . ')') . "</li>";
        $debug_output .= "<li><strong>الاسم الكامل:</strong> '{$full_name}' (طول: " . strlen($full_name) . ")</li>";
        $debug_output .= "<li><strong>البريد الإلكتروني:</strong> '{$email}' (طول: " . strlen($email) . ")</li>";
        $debug_output .= "<li><strong>رقم الجوال:</strong> '{$phone}' (طول: " . strlen($phone) . ")</li>";
        $debug_output .= "<li><strong>نوع المستخدم:</strong> '{$user_type}' (طول: " . strlen($user_type) . ")</li>";
        $debug_output .= "</ul>";
        
        // التحقق من صحة البيانات
        $errors = [];
        if (empty($username)) $errors[] = 'اسم المستخدم فارغ';
        if (empty($password)) $errors[] = 'كلمة المرور فارغة';
        if (empty($full_name)) $errors[] = 'الاسم الكامل فارغ';
        if (empty($user_type)) $errors[] = 'نوع المستخدم فارغ';
        if (!empty($password) && strlen($password) < 6) $errors[] = 'كلمة المرور قصيرة';
        if (!empty($user_type) && !in_array($user_type, ['admin', 'supervisor', 'employee'])) $errors[] = 'نوع المستخدم غير صحيح';
        
        if (!empty($errors)) {
            $debug_output .= "<h4>❌ أخطاء في البيانات:</h4>";
            $debug_output .= "<ul>";
            foreach ($errors as $error) {
                $debug_output .= "<li style='color: red;'>{$error}</li>";
            }
            $debug_output .= "</ul>";
            $message = showAlert('يوجد أخطاء في البيانات المدخلة', 'error');
        } else {
            $debug_output .= "<h4>✅ جميع البيانات صحيحة</h4>";
            
            // محاولة إنشاء المستخدم
            $debug_output .= "<h4>🔄 محاولة إنشاء المستخدم...</h4>";
            
            try {
                $result = createUser($username, $password, $full_name, $email, $phone, $user_type);
                
                $debug_output .= "<h4>📤 نتيجة createUser:</h4>";
                $debug_output .= "<pre>" . print_r($result, true) . "</pre>";
                
                if ($result['success']) {
                    $message = showAlert("✅ تم إنشاء المستخدم بنجاح! معرف المستخدم: {$result['user_id']}", 'success');
                    
                    // التحقق من وجود المستخدم في قاعدة البيانات
                    $pdo = getDBConnection();
                    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                    $stmt->execute([$result['user_id']]);
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($user) {
                        $debug_output .= "<h4>✅ المستخدم موجود في قاعدة البيانات:</h4>";
                        $debug_output .= "<pre>" . print_r($user, true) . "</pre>";
                    } else {
                        $debug_output .= "<h4>❌ المستخدم غير موجود في قاعدة البيانات!</h4>";
                    }
                } else {
                    $message = showAlert("❌ فشل في إنشاء المستخدم: " . $result['message'], 'error');
                }
            } catch (Exception $e) {
                $debug_output .= "<h4>💥 خطأ في التنفيذ:</h4>";
                $debug_output .= "<p style='color: red;'>" . $e->getMessage() . "</p>";
                $message = showAlert("خطأ في النظام: " . $e->getMessage(), 'error');
            }
        }
    } else {
        $debug_output .= "<p><strong>❌ لم يتم العثور على create_user في POST</strong></p>";
        $message = showAlert('لم يتم إرسال النموذج بشكل صحيح', 'error');
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>تشخيص النموذج</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <!-- شريط الحالة -->
        <div class="status-bar">
            <div class="status-left">
                <span><?php echo date('H:i'); ?></span>
            </div>
            <div class="status-right">
                <div class="signal-bars">
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                </div>
                <span>📶</span>
                <span>🔋</span>
                <span>100%</span>
            </div>
        </div>

        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <div class="header-back">
                    <a href="create_user.php" class="back-btn">←</a>
                </div>
                <h1>🔧 تشخيص النموذج</h1>
                <div class="header-actions">
                    <a href="create_user.php" class="header-btn">العودة</a>
                </div>
            </div>
        </header>

        <div class="page-content">
            <?php echo $message; ?>

            <!-- نموذج تشخيص مبسط -->
            <div class="form-container">
                <div class="form-header">
                    <div class="form-icon">🧪</div>
                    <h2 class="form-title">نموذج تشخيص مبسط</h2>
                    <p class="form-subtitle">لاختبار إرسال البيانات</p>
                </div>
                
                <form method="POST" class="form-body">
                    <div class="form-group">
                        <label class="form-label">اسم المستخدم <span class="required">*</span></label>
                        <input type="text" name="username" class="form-input" 
                               value="test_<?php echo time(); ?>" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">كلمة المرور <span class="required">*</span></label>
                        <input type="password" name="password" class="form-input" 
                               value="123456" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">الاسم الكامل <span class="required">*</span></label>
                        <input type="text" name="full_name" class="form-input" 
                               value="مستخدم تجريبي" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">نوع المستخدم <span class="required">*</span></label>
                        <select name="user_type" class="form-input" required>
                            <option value="">اختر نوع المستخدم</option>
                            <option value="admin">👑 مدير</option>
                            <option value="supervisor" selected>👨‍💼 مشرف</option>
                            <option value="employee">📱 موظف</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="email" class="form-input" 
                               value="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label class="form-label">رقم الجوال</label>
                        <input type="tel" name="phone" class="form-input" 
                               value="0501234567">
                    </div>

                    <div class="form-actions">
                        <button type="submit" name="create_user" class="btn btn-primary btn-xl">
                            🧪 اختبار إنشاء المستخدم
                        </button>
                    </div>
                </form>
            </div>

            <!-- معلومات التشخيص -->
            <?php if (!empty($debug_output)): ?>
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📋 معلومات التشخيص</h3>
                </div>
                <div class="card-body">
                    <?php echo $debug_output; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- معلومات النظام -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ℹ️ معلومات النظام</h3>
                </div>
                <div class="card-body">
                    <ul style="list-style: none; padding: 0;">
                        <li style="padding: 5px 0;"><strong>طريقة الطلب:</strong> <?php echo $_SERVER['REQUEST_METHOD']; ?></li>
                        <li style="padding: 5px 0;"><strong>المستخدم الحالي:</strong> <?php echo $_SESSION['full_name'] ?? 'غير محدد'; ?></li>
                        <li style="padding: 5px 0;"><strong>نوع المستخدم:</strong> <?php echo $_SESSION['user_type'] ?? 'غير محدد'; ?></li>
                        <li style="padding: 5px 0;"><strong>معرف الجلسة:</strong> <?php echo session_id(); ?></li>
                        <li style="padding: 5px 0;"><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></li>
                    </ul>
                </div>
            </div>

            <!-- اختبار قاعدة البيانات -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🗄️ اختبار قاعدة البيانات</h3>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        $pdo = getDBConnection();
                        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
                        $total = $stmt->fetchColumn();
                        echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات يعمل. إجمالي المستخدمين: {$total}</p>";
                        
                        // اختبار إدراج بسيط
                        $test_query = "SELECT 1 as test";
                        $stmt = $pdo->query($test_query);
                        $result = $stmt->fetch();
                        echo "<p style='color: green;'>✅ اختبار الاستعلام يعمل: " . $result['test'] . "</p>";
                        
                    } catch (Exception $e) {
                        echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
                    }
                    ?>
                </div>
            </div>
        </div>

        <!-- شريط التنقل السفلي -->
        <div class="tab-bar">
            <div class="tab-items">
                <a href="admin_dashboard.php" class="tab-item">
                    <span class="tab-icon">🏠</span>
                    <span class="tab-label">الرئيسية</span>
                </a>
                <a href="debug_form.php" class="tab-item active">
                    <span class="tab-icon">🔧</span>
                    <span class="tab-label">تشخيص</span>
                </a>
                <a href="create_user.php" class="tab-item">
                    <span class="tab-icon">➕</span>
                    <span class="tab-label">إنشاء</span>
                </a>
                <a href="test_create_user.php" class="tab-item">
                    <span class="tab-icon">🧪</span>
                    <span class="tab-label">اختبار</span>
                </a>
                <a href="logout.php" class="tab-item">
                    <span class="tab-icon">🚪</span>
                    <span class="tab-label">خروج</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        // تشخيص JavaScript
        console.log('تم تحميل صفحة التشخيص');
        
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const submitBtn = document.querySelector('button[type="submit"]');
            
            console.log('النموذج:', form);
            console.log('زر الإرسال:', submitBtn);
            
            if (form) {
                form.addEventListener('submit', function(e) {
                    console.log('تم إرسال النموذج');
                    console.log('البيانات:', new FormData(form));
                    
                    // عرض البيانات
                    const formData = new FormData(form);
                    for (let [key, value] of formData.entries()) {
                        console.log(key + ': ' + value);
                    }
                });
            }
        });
    </script>
</body>
</html>
