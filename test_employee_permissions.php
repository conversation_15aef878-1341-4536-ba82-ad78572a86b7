<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صلاحيات الموظف</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .step-box {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .success-box {
            background: #d4edda;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
            color: #155724;
        }
        
        .warning-box {
            background: #fff3cd;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
            color: #856404;
        }
        
        .error-box {
            background: #f8d7da;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
            color: #721c24;
        }
        
        .test-scenario {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
        }
        
        .comparison-table td {
            padding: 15px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }
        
        .old-behavior {
            background: #ffebee;
            color: #c62828;
        }
        
        .new-behavior {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .permission-flow {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border: 2px solid #007bff;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .flow-step.denied {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .flow-arrow {
            font-size: 20px;
            margin: 0 10px;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔐 اختبار صلاحيات الموظف</h1>
        <p>هذه الصفحة توضح نظام ربط صلاحيات الموظف بالمشرف الذي أنشأه</p>
        
        <div class="success-box">
            <h3>🎯 النظام الجديد:</h3>
            <p><strong>"ربط صلاحية الموظف على المشرف الذي أنشأه ولا يمكن أن يقوم بمسح الدعوات المصدرة من مشرف آخر"</strong></p>
        </div>
    </div>
    
    <div class="test-container">
        <h2>📊 مقارنة النظام القديم والجديد</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>الجانب</th>
                    <th class="old-behavior">النظام القديم ❌</th>
                    <th class="new-behavior">النظام الجديد ✅</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>صلاحيات الموظف</strong></td>
                    <td class="old-behavior">يمكن مسح جميع الدعوات</td>
                    <td class="new-behavior">يمسح دعوات مشرفه فقط</td>
                </tr>
                <tr>
                    <td><strong>الإحصائيات</strong></td>
                    <td class="old-behavior">إحصائيات عامة لجميع الدعوات</td>
                    <td class="new-behavior">إحصائيات دعوات المشرف فقط</td>
                </tr>
                <tr>
                    <td><strong>آخر العمليات</strong></td>
                    <td class="old-behavior">جميع عمليات المسح</td>
                    <td class="new-behavior">عمليات مسح دعوات المشرف فقط</td>
                </tr>
                <tr>
                    <td><strong>رسائل الخطأ</strong></td>
                    <td class="old-behavior">كود غير موجود</td>
                    <td class="new-behavior">دعوة من مشرف آخر</td>
                </tr>
                <tr>
                    <td><strong>الأمان</strong></td>
                    <td class="old-behavior">أقل أماناً</td>
                    <td class="new-behavior">أمان عالي مع عزل البيانات</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-container">
        <h2>🔄 كيف يعمل النظام الجديد</h2>
        
        <div class="permission-flow">
            <h4>📋 تدفق التحقق من الصلاحيات:</h4>
            
            <div class="flow-step">
                <span>1️⃣</span>
                <span class="flow-arrow">→</span>
                <span>الموظف يمسح QR Code</span>
            </div>
            
            <div class="flow-step">
                <span>2️⃣</span>
                <span class="flow-arrow">→</span>
                <span>النظام يستخرج رقم الإجازة</span>
            </div>
            
            <div class="flow-step">
                <span>3️⃣</span>
                <span class="flow-arrow">→</span>
                <span>البحث عن الدعوة مع التحقق من المشرف</span>
            </div>
            
            <div class="flow-step">
                <span>4️⃣</span>
                <span class="flow-arrow">→</span>
                <span>مقارنة مشرف الدعوة مع مشرف الموظف</span>
            </div>
            
            <div class="flow-step">
                <span>5️⃣</span>
                <span class="flow-arrow">→</span>
                <span><strong>إذا تطابق:</strong> السماح بالمسح ✅</span>
            </div>
            
            <div class="flow-step denied">
                <span>5️⃣</span>
                <span class="flow-arrow">→</span>
                <span><strong>إذا لم يتطابق:</strong> رفض مع رسالة واضحة ❌</span>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🧪 سيناريوهات الاختبار</h2>
        
        <div class="test-scenario">
            <h4>✅ سيناريو 1: دعوة من نفس المشرف</h4>
            <ol>
                <li><strong>المشرف أ</strong> ينشئ موظف ب</li>
                <li><strong>المشرف أ</strong> ينشئ دعوة للضيف ج</li>
                <li><strong>الموظف ب</strong> يمسح QR Code للضيف ج</li>
                <li><strong>النتيجة المتوقعة:</strong> ✅ نجح التسجيل</li>
                <li><strong>الرسالة:</strong> "تم تسجيل الحضور بنجاح"</li>
                <li><strong>معلومات إضافية:</strong> "الدعوة من: المشرف أ"</li>
            </ol>
        </div>
        
        <div class="test-scenario">
            <h4>❌ سيناريو 2: دعوة من مشرف آخر</h4>
            <ol>
                <li><strong>المشرف أ</strong> ينشئ موظف ب</li>
                <li><strong>المشرف د</strong> ينشئ دعوة للضيف هـ</li>
                <li><strong>الموظف ب</strong> يمسح QR Code للضيف هـ</li>
                <li><strong>النتيجة المتوقعة:</strong> ❌ رفض المسح</li>
                <li><strong>الرسالة:</strong> "هذه الدعوة مصدرة من مشرف آخر ولا يمكنك مسحها"</li>
                <li><strong>معلومات إضافية:</strong> "اسم الضيف: هـ، الدعوة مصدرة من: المشرف د"</li>
            </ol>
        </div>
        
        <div class="test-scenario">
            <h4>⚠️ سيناريو 3: دعوة مستخدمة مسبقاً من نفس المشرف</h4>
            <ol>
                <li><strong>المشرف أ</strong> ينشئ موظف ب</li>
                <li><strong>المشرف أ</strong> ينشئ دعوة للضيف ج</li>
                <li><strong>الضيف ج</strong> حضر من قبل</li>
                <li><strong>الموظف ب</strong> يمسح QR Code للضيف ج مرة أخرى</li>
                <li><strong>النتيجة المتوقعة:</strong> ⚠️ تحذير</li>
                <li><strong>الرسالة:</strong> "تم استخدام هذا الكود من قبل"</li>
                <li><strong>معلومات إضافية:</strong> "الدعوة من: المشرف أ، تم الحضور في: [التاريخ]"</li>
            </ol>
        </div>
    </div>
    
    <div class="test-container">
        <h2>📊 الإحصائيات المحدودة</h2>
        
        <div class="success-box">
            <h4>✅ الإحصائيات الآن تظهر فقط:</h4>
            <ul>
                <li><strong>ما مسحه الموظف اليوم:</strong> من دعوات مشرفه فقط</li>
                <li><strong>إجمالي ما مسحه الموظف:</strong> من دعوات مشرفه فقط</li>
                <li><strong>إجمالي الدعوات:</strong> دعوات المشرف فقط</li>
                <li><strong>إجمالي الحضور:</strong> حضور دعوات المشرف فقط</li>
            </ul>
        </div>
        
        <div class="warning-box">
            <h4>⚠️ لا تظهر الإحصائيات:</h4>
            <ul>
                <li>دعوات المشرفين الآخرين</li>
                <li>حضور دعوات المشرفين الآخرين</li>
                <li>عمليات مسح دعوات المشرفين الآخرين</li>
            </ul>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🔧 التحسينات التقنية</h2>
        
        <div class="step-box">
            <h4>🔍 استعلام البحث المحسن:</h4>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto;">
SELECT g.*, u.full_name as creator_name 
FROM guests g 
LEFT JOIN users u ON g.created_by = u.id 
WHERE g.code = ? AND g.created_by = ?
            </pre>
            <p><strong>الفائدة:</strong> يبحث عن الدعوة فقط إذا كانت من نفس المشرف</p>
        </div>
        
        <div class="step-box">
            <h4>🛡️ التحقق من الصلاحيات:</h4>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto;">
$employeeCreatedBy = $_SESSION['created_by'];
if ($employeeCreatedBy) {
    // البحث مع قيد المشرف
} else {
    // البحث العام (للحالات الخاصة)
}
            </pre>
            <p><strong>الفائدة:</strong> حماية شاملة مع مرونة للحالات الخاصة</p>
        </div>
        
        <div class="step-box">
            <h4>📋 رسائل خطأ مفصلة:</h4>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto;">
if ($guestExists) {
    // الضيف موجود لكن من مشرف آخر
    $scanResult = [
        'status' => 'error',
        'message' => 'هذه الدعوة مصدرة من مشرف آخر ولا يمكنك مسحها',
        'guest_info' => [
            'name' => $guestExists['name'],
            'creator' => $guestExists['creator_name']
        ]
    ];
}
            </pre>
            <p><strong>الفائدة:</strong> رسائل واضحة تساعد في فهم سبب الرفض</p>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🔗 اختبر بنفسك</h2>
        <div style="text-align: center;">
            <a href="employee_dashboard.php" class="btn btn-primary">📱 لوحة تحكم الموظف</a>
            <a href="supervisor_dashboard.php" class="btn btn-success">👨‍💼 لوحة تحكم المشرف</a>
            <a href="admin_dashboard.php" class="btn btn-warning">👑 لوحة تحكم الإدارة</a>
        </div>
        
        <div class="step-box" style="margin-top: 20px;">
            <h4>📝 خطوات الاختبار:</h4>
            <ol>
                <li><strong>أنشئ مشرفين مختلفين</strong> من لوحة الإدارة</li>
                <li><strong>أنشئ موظفين</strong> تحت كل مشرف</li>
                <li><strong>أنشئ دعوات</strong> من كل مشرف</li>
                <li><strong>جرب مسح الدعوات</strong> بموظفين مختلفين</li>
                <li><strong>لاحظ الرسائل والإحصائيات</strong> المختلفة</li>
            </ol>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🎯 النتيجة المتوقعة</h2>
        
        <div class="success-box">
            <h4>✅ بعد التحديثات:</h4>
            <ul>
                <li><strong>عزل كامل للبيانات:</strong> كل موظف يرى دعوات مشرفه فقط</li>
                <li><strong>أمان عالي:</strong> لا يمكن الوصول لدعوات المشرفين الآخرين</li>
                <li><strong>رسائل واضحة:</strong> توضح سبب رفض المسح</li>
                <li><strong>إحصائيات دقيقة:</strong> مقتصرة على نطاق عمل الموظف</li>
                <li><strong>تجربة مستخدم محسنة:</strong> معلومات مفيدة عن مصدر الدعوة</li>
            </ul>
        </div>
        
        <div class="warning-box">
            <h4>⚠️ ملاحظات مهمة:</h4>
            <ul>
                <li>إذا لم يكن للموظف مشرف محدد، سيعمل النظام بالطريقة العامة</li>
                <li>المشرفون يمكنهم رؤية جميع دعواتهم وعمليات المسح عليها</li>
                <li>الإدارة تحتفظ بالوصول الكامل لجميع البيانات</li>
            </ul>
        </div>
    </div>
</body>
</html>
