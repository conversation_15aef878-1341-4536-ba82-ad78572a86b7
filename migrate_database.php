<?php
/**
 * ملف ترحيل قاعدة البيانات من النظام القديم إلى الجديد
 * يجب تشغيله مرة واحدة فقط بعد تحديث النظام
 */

require_once 'config.php';

echo "<h1>🔄 ترحيل قاعدة البيانات</h1>";
echo "<hr>";

try {
    $pdo = getDBConnection();
    
    echo "<h2>1️⃣ التحقق من الجداول الموجودة</h2>";
    
    // التحقق من وجود جدول users الجديد
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "❌ جدول users غير موجود. يرجى تشغيل ملف database.sql أولاً<br>";
        exit();
    } else {
        echo "✅ جدول users موجود<br>";
    }
    
    // التحقق من وجود جدول admins القديم
    $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
    if ($stmt->rowCount() > 0) {
        echo "⚠️ جدول admins القديم موجود - سيتم ترحيل البيانات<br>";
        
        // ترحيل البيانات من admins إلى users
        $stmt = $pdo->query("SELECT * FROM admins");
        $oldAdmins = $stmt->fetchAll();
        
        foreach ($oldAdmins as $admin) {
            // التحقق من عدم وجود المستخدم مسبقاً
            $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
            $checkStmt->execute([$admin['username']]);
            
            if ($checkStmt->fetchColumn() == 0) {
                $insertStmt = $pdo->prepare("
                    INSERT INTO users (username, password, full_name, email, user_type, status, created_at) 
                    VALUES (?, ?, ?, ?, 'admin', 'active', ?)
                ");
                $insertStmt->execute([
                    $admin['username'],
                    $admin['password'],
                    'مدير النظام',
                    '<EMAIL>',
                    $admin['created_at']
                ]);
                echo "✅ تم ترحيل المدير: {$admin['username']}<br>";
            } else {
                echo "⚠️ المدير {$admin['username']} موجود مسبقاً<br>";
            }
        }
        
        // حذف جدول admins القديم (اختياري)
        echo "<br><strong>هل تريد حذف جدول admins القديم؟</strong><br>";
        echo "<a href='?delete_old_table=admins' onclick='return confirm(\"هل أنت متأكد؟\")'>🗑️ حذف جدول admins القديم</a><br>";
    } else {
        echo "✅ لا يوجد جدول admins قديم للترحيل<br>";
    }
    
    echo "<hr>";
    echo "<h2>2️⃣ التحقق من أعمدة جدول guests</h2>";
    
    // التحقق من وجود الأعمدة الجديدة في جدول guests
    $stmt = $pdo->query("DESCRIBE guests");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = ['created_by', 'scanned_by', 'updated_at'];
    $missingColumns = [];
    
    foreach ($requiredColumns as $column) {
        if (!in_array($column, $columns)) {
            $missingColumns[] = $column;
        }
    }
    
    if (!empty($missingColumns)) {
        echo "⚠️ أعمدة مفقودة في جدول guests: " . implode(', ', $missingColumns) . "<br>";
        echo "سيتم إضافتها الآن...<br>";
        
        // إضافة الأعمدة المفقودة
        if (in_array('created_by', $missingColumns)) {
            $pdo->exec("ALTER TABLE guests ADD COLUMN created_by int(11) DEFAULT NULL");
            $pdo->exec("ALTER TABLE guests ADD FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL");
            echo "✅ تم إضافة عمود created_by<br>";
        }
        
        if (in_array('scanned_by', $missingColumns)) {
            $pdo->exec("ALTER TABLE guests ADD COLUMN scanned_by int(11) DEFAULT NULL");
            $pdo->exec("ALTER TABLE guests ADD FOREIGN KEY (scanned_by) REFERENCES users(id) ON DELETE SET NULL");
            echo "✅ تم إضافة عمود scanned_by<br>";
        }
        
        if (in_array('updated_at', $missingColumns)) {
            $pdo->exec("ALTER TABLE guests ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            echo "✅ تم إضافة عمود updated_at<br>";
        }
        
    } else {
        echo "✅ جميع الأعمدة المطلوبة موجودة في جدول guests<br>";
    }
    
    echo "<hr>";
    echo "<h2>3️⃣ التحقق من الجداول الإضافية</h2>";
    
    // التحقق من جدول activity_log
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_log'");
    if ($stmt->rowCount() == 0) {
        echo "⚠️ جدول activity_log غير موجود - سيتم إنشاؤه<br>";
        $pdo->exec("
            CREATE TABLE activity_log (
                id int(11) NOT NULL AUTO_INCREMENT,
                user_id int(11) DEFAULT NULL,
                action varchar(100) NOT NULL,
                target_type varchar(50) DEFAULT NULL,
                target_id int(11) DEFAULT NULL,
                details text DEFAULT NULL,
                ip_address varchar(45) DEFAULT NULL,
                user_agent text DEFAULT NULL,
                created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY user_id (user_id),
                KEY action (action),
                KEY created_at (created_at),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "✅ تم إنشاء جدول activity_log<br>";
    } else {
        echo "✅ جدول activity_log موجود<br>";
    }
    
    // التحقق من جدول permissions
    $stmt = $pdo->query("SHOW TABLES LIKE 'permissions'");
    if ($stmt->rowCount() == 0) {
        echo "⚠️ جدول permissions غير موجود - سيتم إنشاؤه<br>";
        $pdo->exec("
            CREATE TABLE permissions (
                id int(11) NOT NULL AUTO_INCREMENT,
                user_id int(11) NOT NULL,
                permission varchar(50) NOT NULL,
                granted_by int(11) DEFAULT NULL,
                created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY user_id (user_id),
                KEY granted_by (granted_by),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "✅ تم إنشاء جدول permissions<br>";
    } else {
        echo "✅ جدول permissions موجود<br>";
    }
    
    echo "<hr>";
    echo "<h2>4️⃣ إحصائيات النظام</h2>";
    
    $totalUsers = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $totalGuests = $pdo->query("SELECT COUNT(*) FROM guests")->fetchColumn();
    $totalScanned = $pdo->query("SELECT COUNT(*) FROM guests WHERE scanned = 1")->fetchColumn();
    
    echo "👥 إجمالي المستخدمين: <strong>{$totalUsers}</strong><br>";
    echo "🎫 إجمالي الدعوات: <strong>{$totalGuests}</strong><br>";
    echo "✅ إجمالي الحضور: <strong>{$totalScanned}</strong><br>";
    
    echo "<hr>";
    echo "<h2>✅ تم الانتهاء من الترحيل بنجاح!</h2>";
    echo "<p>يمكنك الآن استخدام النظام الجديد:</p>";
    echo "<ul>";
    echo "<li><a href='login.php'>🔐 تسجيل الدخول</a></li>";
    echo "<li><a href='admin_dashboard.php'>👑 لوحة تحكم المدير</a></li>";
    echo "<li><a href='supervisor_dashboard.php'>👨‍💼 لوحة تحكم المشرف</a></li>";
    echo "<li><a href='employee_dashboard.php'>📱 لوحة تحكم الموظف</a></li>";
    echo "</ul>";
    
    // معالجة حذف الجداول القديمة
    if (isset($_GET['delete_old_table'])) {
        $table = $_GET['delete_old_table'];
        if ($table === 'admins') {
            $pdo->exec("DROP TABLE IF EXISTS admins");
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "✅ تم حذف جدول admins القديم";
            echo "</div>";
        }
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ خطأ عام: " . $e->getMessage();
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h1, h2 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
    padding: 5px 10px;
    background: #e3f2fd;
    border-radius: 3px;
    margin: 2px;
    display: inline-block;
}

a:hover {
    background: #bbdefb;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
</style>
