<?php
require_once 'config.php';

// التحقق من صلاحيات المدير
requireAdmin();

$pdo = getDBConnection();
$message = '';
$debug_info = '';

// اختبار إنشاء مستخدم
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['test_create'])) {
    $test_username = 'test_user_' . time();
    $test_password = 'test123456';
    $test_full_name = 'مستخدم تجريبي';
    $test_email = '<EMAIL>';
    $test_phone = '0501234567';
    $test_user_type = 'employee';
    
    $debug_info .= "<h3>🔍 معلومات الاختبار:</h3>";
    $debug_info .= "<ul>";
    $debug_info .= "<li><strong>اسم المستخدم:</strong> {$test_username}</li>";
    $debug_info .= "<li><strong>كلمة المرور:</strong> {$test_password}</li>";
    $debug_info .= "<li><strong>الاسم الكامل:</strong> {$test_full_name}</li>";
    $debug_info .= "<li><strong>البريد الإلكتروني:</strong> {$test_email}</li>";
    $debug_info .= "<li><strong>رقم الجوال:</strong> {$test_phone}</li>";
    $debug_info .= "<li><strong>نوع المستخدم:</strong> {$test_user_type}</li>";
    $debug_info .= "</ul>";
    
    // محاولة إنشاء المستخدم
    $result = createUser($test_username, $test_password, $test_full_name, $test_email, $test_phone, $test_user_type);
    
    if ($result['success']) {
        $message = showAlert("✅ تم إنشاء المستخدم التجريبي بنجاح! معرف المستخدم: {$result['user_id']}", 'success');
        
        // التحقق من وجود المستخدم في قاعدة البيانات
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$result['user_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            $debug_info .= "<h3>✅ المستخدم موجود في قاعدة البيانات:</h3>";
            $debug_info .= "<ul>";
            $debug_info .= "<li><strong>المعرف:</strong> {$user['id']}</li>";
            $debug_info .= "<li><strong>اسم المستخدم:</strong> {$user['username']}</li>";
            $debug_info .= "<li><strong>الاسم الكامل:</strong> {$user['full_name']}</li>";
            $debug_info .= "<li><strong>البريد الإلكتروني:</strong> {$user['email']}</li>";
            $debug_info .= "<li><strong>رقم الجوال:</strong> {$user['phone']}</li>";
            $debug_info .= "<li><strong>نوع المستخدم:</strong> {$user['user_type']}</li>";
            $debug_info .= "<li><strong>الحالة:</strong> {$user['status']}</li>";
            $debug_info .= "<li><strong>تاريخ الإنشاء:</strong> {$user['created_at']}</li>";
            $debug_info .= "</ul>";
        } else {
            $debug_info .= "<h3>❌ المستخدم غير موجود في قاعدة البيانات!</h3>";
        }
    } else {
        $message = showAlert("❌ فشل في إنشاء المستخدم: " . $result['message'], 'error');
    }
}

// اختبار الاتصال بقاعدة البيانات
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['test_db'])) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
        $total = $stmt->fetchColumn();
        $message = showAlert("✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح. إجمالي المستخدمين: {$total}", 'success');
        
        // عرض جميع المستخدمين
        $stmt = $pdo->query("SELECT id, username, full_name, user_type, status, created_at FROM users ORDER BY created_at DESC");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $debug_info .= "<h3>👥 جميع المستخدمين في قاعدة البيانات:</h3>";
        $debug_info .= "<table style='width: 100%; border-collapse: collapse; margin-top: 10px;'>";
        $debug_info .= "<tr style='background: #f0f0f0;'>";
        $debug_info .= "<th style='border: 1px solid #ddd; padding: 8px;'>المعرف</th>";
        $debug_info .= "<th style='border: 1px solid #ddd; padding: 8px;'>اسم المستخدم</th>";
        $debug_info .= "<th style='border: 1px solid #ddd; padding: 8px;'>الاسم الكامل</th>";
        $debug_info .= "<th style='border: 1px solid #ddd; padding: 8px;'>النوع</th>";
        $debug_info .= "<th style='border: 1px solid #ddd; padding: 8px;'>الحالة</th>";
        $debug_info .= "<th style='border: 1px solid #ddd; padding: 8px;'>تاريخ الإنشاء</th>";
        $debug_info .= "</tr>";
        
        foreach ($users as $user) {
            $debug_info .= "<tr>";
            $debug_info .= "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['id']}</td>";
            $debug_info .= "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['username']}</td>";
            $debug_info .= "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['full_name']}</td>";
            $debug_info .= "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['user_type']}</td>";
            $debug_info .= "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['status']}</td>";
            $debug_info .= "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['created_at']}</td>";
            $debug_info .= "</tr>";
        }
        $debug_info .= "</table>";
        
    } catch (PDOException $e) {
        $message = showAlert("❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage(), 'error');
    }
}

// حذف المستخدمين التجريبيين
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['cleanup'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM users WHERE username LIKE 'test_user_%'");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        $message = showAlert("🗑️ تم حذف {$deleted} مستخدم تجريبي", 'success');
    } catch (PDOException $e) {
        $message = showAlert("❌ خطأ في حذف المستخدمين التجريبيين: " . $e->getMessage(), 'error');
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>اختبار إنشاء المستخدمين</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <!-- شريط الحالة -->
        <div class="status-bar">
            <div class="status-left">
                <span><?php echo date('H:i'); ?></span>
            </div>
            <div class="status-right">
                <div class="signal-bars">
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                </div>
                <span>📶</span>
                <span>🔋</span>
                <span>100%</span>
            </div>
        </div>

        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <div class="header-back">
                    <a href="admin_dashboard.php" class="back-btn">←</a>
                </div>
                <h1>🔧 اختبار إنشاء المستخدمين</h1>
                <div class="header-actions">
                    <a href="create_user.php" class="header-btn">إنشاء</a>
                </div>
            </div>
        </header>

        <div class="page-content">
            <?php echo $message; ?>

            <!-- أزرار الاختبار -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🧪 أدوات التشخيص</h3>
                </div>
                <div class="card-body">
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                        <form method="POST">
                            <button type="submit" name="test_create" class="btn btn-primary btn-xl">
                                🧪 اختبار إنشاء مستخدم تجريبي
                            </button>
                        </form>
                        
                        <form method="POST">
                            <button type="submit" name="test_db" class="btn btn-info btn-xl">
                                🗄️ اختبار قاعدة البيانات وعرض المستخدمين
                            </button>
                        </form>
                        
                        <form method="POST">
                            <button type="submit" name="cleanup" class="btn btn-warning btn-xl">
                                🗑️ حذف المستخدمين التجريبيين
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- معلومات التشخيص -->
            <?php if (!empty($debug_info)): ?>
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📋 معلومات التشخيص</h3>
                </div>
                <div class="card-body">
                    <?php echo $debug_info; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- معلومات النظام -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ℹ️ معلومات النظام</h3>
                </div>
                <div class="card-body">
                    <div class="app-list">
                        <div class="list-item">
                            <div class="list-icon info">🐘</div>
                            <div class="list-content">
                                <div class="list-title">إصدار PHP</div>
                                <div class="list-subtitle"><?php echo PHP_VERSION; ?></div>
                            </div>
                        </div>
                        <div class="list-item">
                            <div class="list-icon success">🗄️</div>
                            <div class="list-content">
                                <div class="list-title">قاعدة البيانات</div>
                                <div class="list-subtitle">
                                    <?php 
                                    try {
                                        $version = $pdo->query('SELECT VERSION()')->fetchColumn();
                                        echo "MySQL " . $version;
                                    } catch (Exception $e) {
                                        echo "غير متاح";
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <div class="list-item">
                            <div class="list-icon warning">👤</div>
                            <div class="list-content">
                                <div class="list-title">المستخدم الحالي</div>
                                <div class="list-subtitle"><?php echo $_SESSION['full_name'] ?? 'غير محدد'; ?> (<?php echo $_SESSION['user_type'] ?? 'غير محدد'; ?>)</div>
                            </div>
                        </div>
                        <div class="list-item">
                            <div class="list-icon primary">🕒</div>
                            <div class="list-content">
                                <div class="list-title">الوقت الحالي</div>
                                <div class="list-subtitle"><?php echo date('Y-m-d H:i:s'); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تعليمات الاستخدام -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📖 تعليمات الاستخدام</h3>
                </div>
                <div class="card-body">
                    <ol style="padding-right: 20px; line-height: 1.8;">
                        <li><strong>اختبار إنشاء مستخدم تجريبي:</strong> ينشئ مستخدم جديد بمعرف فريد ويتحقق من وجوده في قاعدة البيانات</li>
                        <li><strong>اختبار قاعدة البيانات:</strong> يتحقق من الاتصال ويعرض جميع المستخدمين الموجودين</li>
                        <li><strong>حذف المستخدمين التجريبيين:</strong> ينظف قاعدة البيانات من المستخدمين التجريبيين</li>
                        <li><strong>إذا فشل الاختبار:</strong> تحقق من إعدادات قاعدة البيانات في ملف config.php</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- شريط التنقل السفلي -->
        <div class="tab-bar">
            <div class="tab-items">
                <a href="admin_dashboard.php" class="tab-item">
                    <span class="tab-icon">🏠</span>
                    <span class="tab-label">الرئيسية</span>
                </a>
                <a href="test_create_user.php" class="tab-item active">
                    <span class="tab-icon">🔧</span>
                    <span class="tab-label">اختبار</span>
                </a>
                <a href="create_user.php" class="tab-item">
                    <span class="tab-icon">➕</span>
                    <span class="tab-label">إنشاء</span>
                </a>
                <a href="admin_dashboard.php" class="tab-item">
                    <span class="tab-icon">👥</span>
                    <span class="tab-label">المستخدمين</span>
                </a>
                <a href="logout.php" class="tab-item">
                    <span class="tab-icon">🚪</span>
                    <span class="tab-label">خروج</span>
                </a>
            </div>
        </div>
    </div>
</body>
</html>
