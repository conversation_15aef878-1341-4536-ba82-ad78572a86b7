<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
requireSupervisor();

$pdo = getDBConnection();
$message = '';

// معالجة حذف مجموعة من الدعوات
if (isset($_POST['delete_batch'])) {
    $batchPrefix = sanitizeInput($_POST['batch_prefix'] ?? '');
    $batchDate = sanitizeInput($_POST['batch_date'] ?? '');
    
    if (!empty($batchPrefix) && !empty($batchDate)) {
        try {
            // جلب الدعوات المطابقة
            $stmt = $pdo->prepare("SELECT id, code FROM guests WHERE name LIKE ? AND DATE(created_at) = ?");
            $stmt->execute(["%{$batchPrefix}%", $batchDate]);
            $guestsToDelete = $stmt->fetchAll();
            
            if (!empty($guestsToDelete)) {
                // حذف ملفات QR
                foreach ($guestsToDelete as $guest) {
                    $qrFiles = [
                        "qrcodes/{$guest['code']}.png",
                        "qrcodes/{$guest['code']}.html",
                        "qrcodes/{$guest['code']}.txt"
                    ];
                    
                    foreach ($qrFiles as $file) {
                        if (file_exists($file)) {
                            unlink($file);
                        }
                    }
                }
                
                // حذف من قاعدة البيانات
                $stmt = $pdo->prepare("DELETE FROM guests WHERE name LIKE ? AND DATE(created_at) = ?");
                $stmt->execute(["%{$batchPrefix}%", $batchDate]);
                
                $deletedCount = $stmt->rowCount();
                $message = showAlert("تم حذف {$deletedCount} دعوة بنجاح", 'success');
            } else {
                $message = showAlert('لم يتم العثور على دعوات مطابقة للمعايير المحددة', 'warning');
            }
            
        } catch (PDOException $e) {
            $message = showAlert('خطأ في حذف الدعوات: ' . $e->getMessage(), 'error');
        }
    }
}

// جلب إحصائيات الدعوات المجمعة
try {
    $sql = "
        SELECT 
            SUBSTRING_INDEX(name, ' رقم ', 1) as prefix,
            DATE(created_at) as batch_date,
            COUNT(*) as total_count,
            SUM(scanned) as scanned_count,
            MIN(created_at) as first_created,
            MAX(created_at) as last_created
        FROM guests 
        WHERE name LIKE '%رقم%'
        GROUP BY SUBSTRING_INDEX(name, ' رقم ', 1), DATE(created_at)
        ORDER BY first_created DESC
    ";
    
    $stmt = $pdo->query($sql);
    $batches = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $batches = [];
    $message = showAlert('خطأ في جلب البيانات: ' . $e->getMessage(), 'error');
}

// جلب الدعوات الفردية (غير المجمعة)
try {
    $stmt = $pdo->query("
        SELECT COUNT(*) as individual_count 
        FROM guests 
        WHERE name NOT LIKE '%رقم%'
    ");
    $individualCount = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $individualCount = 0;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الدعوات المجمعة - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .batch-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-right: 5px solid #007bff;
        }
        
        .batch-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .batch-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }
        
        .batch-date {
            color: #666;
            font-size: 14px;
        }
        
        .batch-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .batch-stat {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .batch-stat .number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .batch-stat .label {
            font-size: 12px;
            color: #666;
        }
        
        .batch-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .progress-ring {
            width: 60px;
            height: 60px;
            margin: 0 auto;
        }
        
        .progress-ring circle {
            fill: transparent;
            stroke: #e9ecef;
            stroke-width: 4;
        }
        
        .progress-ring .progress {
            stroke: #28a745;
            stroke-linecap: round;
            transition: stroke-dasharray 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .batch-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .batch-actions {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>📦 إدارة الدعوات المجمعة</h1>
                <div class="header-actions">
                    <a href="dashboard.php" class="btn btn-secondary">🔙 لوحة التحكم</a>
                    <a href="bulk_invitations.php" class="btn btn-primary">➕ إنشاء دعوات جديدة</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <!-- إحصائيات عامة -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📦</div>
                <div class="stat-info">
                    <h3><?php echo count($batches); ?></h3>
                    <p>مجموعة دعوات</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👤</div>
                <div class="stat-info">
                    <h3><?php echo $individualCount; ?></h3>
                    <p>دعوة فردية</p>
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon">📊</div>
                <div class="stat-info">
                    <h3><?php echo array_sum(array_column($batches, 'total_count')) + $individualCount; ?></h3>
                    <p>إجمالي الدعوات</p>
                </div>
            </div>
        </div>

        <!-- قائمة المجموعات -->
        <?php if (empty($batches)): ?>
            <div class="form-container">
                <div class="no-data">
                    📦 لا توجد مجموعات دعوات حتى الآن
                    <br><br>
                    <a href="bulk_invitations.php" class="btn btn-primary">إنشاء مجموعة دعوات جديدة</a>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($batches as $batch): ?>
                <div class="batch-card">
                    <div class="batch-header">
                        <div>
                            <h3 class="batch-title">📋 <?php echo htmlspecialchars($batch['prefix']); ?></h3>
                            <div class="batch-date">
                                📅 تم الإنشاء: <?php echo date('Y-m-d', strtotime($batch['batch_date'])); ?>
                                (<?php echo date('H:i', strtotime($batch['first_created'])); ?> - <?php echo date('H:i', strtotime($batch['last_created'])); ?>)
                            </div>
                        </div>
                        
                        <!-- دائرة التقدم -->
                        <div class="progress-ring">
                            <?php 
                            $percentage = $batch['total_count'] > 0 ? ($batch['scanned_count'] / $batch['total_count']) * 100 : 0;
                            $circumference = 2 * 3.14159 * 26; // 2πr where r=26
                            $strokeDasharray = $circumference;
                            $strokeDashoffset = $circumference - ($percentage / 100) * $circumference;
                            ?>
                            <svg width="60" height="60">
                                <circle cx="30" cy="30" r="26" stroke="#e9ecef" stroke-width="4" fill="transparent"/>
                                <circle cx="30" cy="30" r="26" stroke="#28a745" stroke-width="4" fill="transparent"
                                        stroke-dasharray="<?php echo $strokeDasharray; ?>"
                                        stroke-dashoffset="<?php echo $strokeDashoffset; ?>"
                                        stroke-linecap="round" class="progress"/>
                                <text x="30" y="35" text-anchor="middle" font-size="12" fill="#333">
                                    <?php echo round($percentage); ?>%
                                </text>
                            </svg>
                        </div>
                    </div>
                    
                    <!-- إحصائيات المجموعة -->
                    <div class="batch-stats">
                        <div class="batch-stat">
                            <div class="number"><?php echo $batch['total_count']; ?></div>
                            <div class="label">إجمالي الدعوات</div>
                        </div>
                        <div class="batch-stat">
                            <div class="number" style="color: #28a745;"><?php echo $batch['scanned_count']; ?></div>
                            <div class="label">تم الحضور</div>
                        </div>
                        <div class="batch-stat">
                            <div class="number" style="color: #ffc107;"><?php echo $batch['total_count'] - $batch['scanned_count']; ?></div>
                            <div class="label">لم يحضر</div>
                        </div>
                        <div class="batch-stat">
                            <div class="number" style="color: #17a2b8;"><?php echo round($percentage, 1); ?>%</div>
                            <div class="label">معدل الحضور</div>
                        </div>
                    </div>
                    
                    <!-- إجراءات المجموعة -->
                    <div class="batch-actions">
                        <a href="dashboard.php?search=<?php echo urlencode($batch['prefix']); ?>" 
                           class="btn btn-info">
                            👁️ عرض الدعوات
                        </a>
                        
                        <a href="export.php?batch_prefix=<?php echo urlencode($batch['prefix']); ?>&batch_date=<?php echo $batch['batch_date']; ?>" 
                           class="btn btn-success">
                            📥 تصدير المجموعة
                        </a>
                        
                        <a href="regenerate_qr.php?batch_prefix=<?php echo urlencode($batch['prefix']); ?>&batch_date=<?php echo $batch['batch_date']; ?>"
                           class="btn btn-warning">
                            🔄 إعادة توليد QR
                        </a>

                        <a href="print_invitations.php?batch_prefix=<?php echo urlencode($batch['prefix']); ?>&batch_date=<?php echo $batch['batch_date']; ?>"
                           target="_blank" class="btn btn-secondary">
                            🖨️ طباعة الدعوات
                        </a>
                        
                        <button onclick="deleteBatch('<?php echo htmlspecialchars($batch['prefix']); ?>', '<?php echo $batch['batch_date']; ?>', <?php echo $batch['total_count']; ?>)" 
                                class="btn btn-danger">
                            🗑️ حذف المجموعة
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- نموذج حذف مخفي -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="batch_prefix" id="deleteBatchPrefix">
        <input type="hidden" name="batch_date" id="deleteBatchDate">
        <input type="hidden" name="delete_batch" value="1">
    </form>

    <script>
        function deleteBatch(prefix, date, count) {
            const message = `هل أنت متأكد من حذف مجموعة "${prefix}"؟\n\nسيتم حذف ${count} دعوة نهائياً.\n\nهذا الإجراء لا يمكن التراجع عنه!`;
            
            if (confirm(message)) {
                document.getElementById('deleteBatchPrefix').value = prefix;
                document.getElementById('deleteBatchDate').value = date;
                document.getElementById('deleteForm').submit();
            }
        }
        
        // تحديث دوائر التقدم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const progressCircles = document.querySelectorAll('.progress');
            progressCircles.forEach(circle => {
                const offset = circle.getAttribute('stroke-dashoffset');
                circle.style.strokeDashoffset = circle.getAttribute('stroke-dasharray');
                setTimeout(() => {
                    circle.style.strokeDashoffset = offset;
                }, 100);
            });
        });
    </script>
</body>
</html>
