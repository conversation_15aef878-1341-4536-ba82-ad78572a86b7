<?php
require_once 'config.php';

// التحقق من تسجيل الدخول (مشرف أو مدير)
requireSupervisor();

$pdo = getDBConnection();
$message = '';
$employee = null;

// التحقق من معرف الموظف
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: add_employee.php');
    exit();
}

$employeeId = (int)$_GET['id'];

// جلب بيانات الموظف
try {
    if (isAdmin()) {
        // المدير يمكنه تعديل أي موظف
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND user_type = 'employee'");
        $stmt->execute([$employeeId]);
    } else {
        // المشرف يمكنه تعديل موظفيه فقط
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND user_type = 'employee' AND created_by = ?");
        $stmt->execute([$employeeId, $_SESSION['user_id']]);
    }
    
    $employee = $stmt->fetch();
    
    if (!$employee) {
        $message = showAlert('الموظف غير موجود أو ليس لديك صلاحية لتعديله', 'error');
    }
} catch (PDOException $e) {
    $message = showAlert('خطأ في جلب بيانات الموظف: ' . $e->getMessage(), 'error');
}

// معالجة تحديث بيانات الموظف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $employee) {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($username) || empty($full_name)) {
        $message = showAlert('اسم المستخدم والاسم الكامل مطلوبان', 'error');
    } elseif (!empty($password) && strlen($password) < 6) {
        $message = showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
    } else {
        try {
            // التحقق من عدم وجود اسم المستخدم مع مستخدم آخر
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
            $stmt->execute([$username, $employeeId]);
            if ($stmt->fetch()) {
                $message = showAlert('اسم المستخدم موجود مع مستخدم آخر، اختر اسم آخر', 'error');
            } else {
                // تحديث بيانات الموظف
                if (!empty($password)) {
                    // تحديث مع كلمة المرور
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("
                        UPDATE users 
                        SET username = ?, password = ?, full_name = ?, email = ?, phone = ?, updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $success = $stmt->execute([$username, $hashedPassword, $full_name, $email, $phone, $employeeId]);
                } else {
                    // تحديث بدون كلمة المرور
                    $stmt = $pdo->prepare("
                        UPDATE users 
                        SET username = ?, full_name = ?, email = ?, phone = ?, updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $success = $stmt->execute([$username, $full_name, $email, $phone, $employeeId]);
                }
                
                if ($success) {
                    // تحديث بيانات الموظف المحلية
                    $employee['username'] = $username;
                    $employee['full_name'] = $full_name;
                    $employee['email'] = $email;
                    $employee['phone'] = $phone;
                    
                    // تسجيل النشاط
                    try {
                        $passwordNote = !empty($password) ? ' (مع تحديث كلمة المرور)' : '';
                        logActivity('update_employee', 'user', $employeeId, "تم تحديث بيانات الموظف: {$full_name} ({$username}){$passwordNote}");
                    } catch (Exception $e) {
                        // تجاهل خطأ التسجيل
                    }
                    
                    $message = showAlert("تم تحديث بيانات الموظف بنجاح!", 'success');
                } else {
                    $message = showAlert('حدث خطأ أثناء تحديث البيانات، حاول مرة أخرى', 'error');
                }
            }
        } catch (PDOException $e) {
            $message = showAlert('خطأ في قاعدة البيانات: ' . $e->getMessage(), 'error');
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل بيانات الموظف</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .form-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        .form-group.required label::after {
            content: " *";
            color: #dc3545;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .password-hint {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .employee-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>✏️ تعديل بيانات الموظف</h1>
                <div class="header-actions">
                    <span>مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                    <a href="add_employee.php" class="btn btn-secondary">← العودة لقائمة الموظفين</a>
                    <a href="supervisor_dashboard.php" class="btn btn-secondary">← لوحة المشرف</a>
                    <a href="logout.php" class="btn btn-secondary">تسجيل الخروج</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <?php if ($employee): ?>
            <!-- معلومات الموظف الحالية -->
            <div class="employee-info">
                <h3>📋 المعلومات الحالية</h3>
                <p><strong>الاسم:</strong> <?php echo htmlspecialchars($employee['full_name']); ?></p>
                <p><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($employee['username']); ?></p>
                <p><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d H:i', strtotime($employee['created_at'])); ?></p>
                <?php if (!empty($employee['updated_at'])): ?>
                    <p><strong>آخر تحديث:</strong> <?php echo date('Y-m-d H:i', strtotime($employee['updated_at'])); ?></p>
                <?php endif; ?>
            </div>

            <!-- نموذج تعديل الموظف -->
            <div class="form-container">
                <h2>📝 تحديث البيانات</h2>
                <form method="POST" action="">
                    <div class="form-row">
                        <div class="form-group required">
                            <label for="username">اسم المستخدم</label>
                            <input type="text" id="username" name="username" 
                                   value="<?php echo htmlspecialchars($employee['username']); ?>" 
                                   required maxlength="50">
                        </div>
                        
                        <div class="form-group">
                            <label for="password">كلمة المرور الجديدة</label>
                            <input type="password" id="password" name="password" minlength="6">
                            <div class="password-hint">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</div>
                        </div>
                    </div>
                    
                    <div class="form-group required">
                        <label for="full_name">الاسم الكامل</label>
                        <input type="text" id="full_name" name="full_name" 
                               value="<?php echo htmlspecialchars($employee['full_name']); ?>" 
                               required maxlength="100">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">البريد الإلكتروني</label>
                            <input type="email" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($employee['email'] ?? ''); ?>" 
                                   maxlength="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">رقم الجوال</label>
                            <input type="tel" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($employee['phone'] ?? ''); ?>" 
                                   maxlength="20">
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" class="btn btn-primary">
                            💾 حفظ التحديثات
                        </button>
                        <a href="add_employee.php" class="btn btn-secondary">
                            ❌ إلغاء
                        </a>
                        <a href="add_employee.php?delete_employee=<?php echo $employee['id']; ?>" 
                           class="btn btn-danger"
                           onclick="return confirm('هل أنت متأكد من حذف هذا الموظف؟\nسيتم حذف الحساب نهائياً!')">
                            🗑️ حذف الحساب
                        </a>
                    </div>
                </form>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: 40px;">
                <h3>❌ الموظف غير موجود</h3>
                <p>الموظف المطلوب غير موجود أو ليس لديك صلاحية للوصول إليه</p>
                <a href="add_employee.php" class="btn btn-primary">العودة لقائمة الموظفين</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
