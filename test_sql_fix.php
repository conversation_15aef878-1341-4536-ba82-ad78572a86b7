<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
requireLogin();

$pdo = getDBConnection();

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار إصلاح SQL</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; }
        .test-box { background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 15px 0; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; color: #155724; }
        .error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }
        .warning { border-left-color: #ffc107; background: #fff3cd; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>";

echo "<h1>🔧 اختبار إصلاح مشكلة SQL</h1>";

// التأكد من وجود معرف المشرف
if (!isset($_SESSION['created_by'])) {
    try {
        $stmt = $pdo->prepare("SELECT created_by FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
        $_SESSION['created_by'] = $user['created_by'] ?? null;
        
        echo "<div class='test-box success'>
                <h3>✅ تم جلب معرف المشرف</h3>
                <p><strong>معرف المشرف:</strong> " . ($_SESSION['created_by'] ?: 'غير محدد') . "</p>
              </div>";
    } catch (PDOException $e) {
        echo "<div class='test-box error'>
                <h3>❌ خطأ في جلب معرف المشرف</h3>
                <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
              </div>";
        $_SESSION['created_by'] = null;
    }
}

$employeeCreatedBy = isset($_SESSION['created_by']) ? $_SESSION['created_by'] : null;

echo "<div class='test-box'>
        <h3>📋 معلومات الموظف</h3>
        <p><strong>معرف الموظف:</strong> {$_SESSION['user_id']}</p>
        <p><strong>اسم الموظف:</strong> {$_SESSION['full_name']}</p>
        <p><strong>معرف المشرف:</strong> " . ($employeeCreatedBy ?: 'غير محدد') . "</p>
      </div>";

// اختبار الاستعلامات المصححة
echo "<h2>🧪 اختبار الاستعلامات المصححة</h2>";

// اختبار 1: إحصائيات اليوم
try {
    if ($employeeCreatedBy) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM guests 
            WHERE scanned_by = ? AND DATE(scanned_at) = CURDATE() AND guests.created_by = ?
        ");
        $stmt->execute([$_SESSION['user_id'], $employeeCreatedBy]);
        $todayCount = $stmt->fetchColumn();
        
        echo "<div class='test-box success'>
                <h4>✅ اختبار 1: إحصائيات اليوم</h4>
                <p><strong>عدد المسح اليوم:</strong> $todayCount</p>
                <pre>SELECT COUNT(*) FROM guests 
WHERE scanned_by = ? AND DATE(scanned_at) = CURDATE() AND guests.created_by = ?</pre>
              </div>";
    } else {
        echo "<div class='test-box warning'>
                <h4>⚠️ اختبار 1: لا يوجد مشرف محدد</h4>
                <p>سيتم استخدام الاستعلام العام</p>
              </div>";
    }
} catch (PDOException $e) {
    echo "<div class='test-box error'>
            <h4>❌ اختبار 1: خطأ في إحصائيات اليوم</h4>
            <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

// اختبار 2: إجمالي المسح
try {
    if ($employeeCreatedBy) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM guests 
            WHERE scanned_by = ? AND guests.created_by = ?
        ");
        $stmt->execute([$_SESSION['user_id'], $employeeCreatedBy]);
        $totalCount = $stmt->fetchColumn();
        
        echo "<div class='test-box success'>
                <h4>✅ اختبار 2: إجمالي المسح</h4>
                <p><strong>إجمالي المسح:</strong> $totalCount</p>
                <pre>SELECT COUNT(*) FROM guests 
WHERE scanned_by = ? AND guests.created_by = ?</pre>
              </div>";
    }
} catch (PDOException $e) {
    echo "<div class='test-box error'>
            <h4>❌ اختبار 2: خطأ في إجمالي المسح</h4>
            <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

// اختبار 3: إحصائيات المشرف
try {
    if ($employeeCreatedBy) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE guests.created_by = ?");
        $stmt->execute([$employeeCreatedBy]);
        $supervisorGuests = $stmt->fetchColumn();
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE scanned = 1 AND guests.created_by = ?");
        $stmt->execute([$employeeCreatedBy]);
        $supervisorScanned = $stmt->fetchColumn();
        
        echo "<div class='test-box success'>
                <h4>✅ اختبار 3: إحصائيات المشرف</h4>
                <p><strong>دعوات المشرف:</strong> $supervisorGuests</p>
                <p><strong>حضور دعوات المشرف:</strong> $supervisorScanned</p>
                <pre>SELECT COUNT(*) FROM guests WHERE guests.created_by = ?
SELECT COUNT(*) FROM guests WHERE scanned = 1 AND guests.created_by = ?</pre>
              </div>";
    }
} catch (PDOException $e) {
    echo "<div class='test-box error'>
            <h4>❌ اختبار 3: خطأ في إحصائيات المشرف</h4>
            <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

// اختبار 4: آخر عمليات المسح
try {
    if ($employeeCreatedBy) {
        $stmt = $pdo->prepare("
            SELECT g.*, u.full_name as scanned_by_name, creator.full_name as creator_name
            FROM guests g 
            LEFT JOIN users u ON g.scanned_by = u.id 
            LEFT JOIN users creator ON g.created_by = creator.id
            WHERE g.scanned_by = ? AND g.created_by = ?
            ORDER BY g.scanned_at DESC 
            LIMIT 5
        ");
        $stmt->execute([$_SESSION['user_id'], $employeeCreatedBy]);
        $recentScans = $stmt->fetchAll();
        
        echo "<div class='test-box success'>
                <h4>✅ اختبار 4: آخر عمليات المسح</h4>
                <p><strong>عدد العمليات:</strong> " . count($recentScans) . "</p>";
        
        if (!empty($recentScans)) {
            echo "<ul>";
            foreach ($recentScans as $scan) {
                echo "<li><strong>{$scan['name']}</strong> - {$scan['scanned_at']} - من: {$scan['creator_name']}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>لا توجد عمليات مسح</p>";
        }
        
        echo "<pre>SELECT g.*, u.full_name as scanned_by_name, creator.full_name as creator_name
FROM guests g 
LEFT JOIN users u ON g.scanned_by = u.id 
LEFT JOIN users creator ON g.created_by = creator.id
WHERE g.scanned_by = ? AND g.created_by = ?
ORDER BY g.scanned_at DESC LIMIT 5</pre>
              </div>";
    }
} catch (PDOException $e) {
    echo "<div class='test-box error'>
            <h4>❌ اختبار 4: خطأ في آخر عمليات المسح</h4>
            <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

// اختبار 5: البحث عن دعوة مع قيد المشرف
echo "<h2>🔍 اختبار البحث عن الدعوات</h2>";

try {
    if ($employeeCreatedBy) {
        // جلب دعوة من نفس المشرف للاختبار
        $stmt = $pdo->prepare("
            SELECT g.*, u.full_name as creator_name 
            FROM guests g 
            LEFT JOIN users u ON g.created_by = u.id 
            WHERE g.created_by = ? 
            LIMIT 1
        ");
        $stmt->execute([$employeeCreatedBy]);
        $testGuest = $stmt->fetch();
        
        if ($testGuest) {
            echo "<div class='test-box success'>
                    <h4>✅ اختبار 5: البحث عن دعوة من نفس المشرف</h4>
                    <p><strong>اسم الضيف:</strong> {$testGuest['name']}</p>
                    <p><strong>كود الدعوة:</strong> {$testGuest['code']}</p>
                    <p><strong>المشرف:</strong> {$testGuest['creator_name']}</p>
                    <p><strong>حالة المسح:</strong> " . ($testGuest['scanned'] ? 'تم المسح' : 'لم يتم المسح') . "</p>
                  </div>";
        } else {
            echo "<div class='test-box warning'>
                    <h4>⚠️ اختبار 5: لا توجد دعوات من هذا المشرف</h4>
                    <p>لا توجد دعوات للاختبار</p>
                  </div>";
        }
        
        // جلب دعوة من مشرف آخر للاختبار
        $stmt = $pdo->prepare("
            SELECT g.*, u.full_name as creator_name 
            FROM guests g 
            LEFT JOIN users u ON g.created_by = u.id 
            WHERE g.created_by != ? OR g.created_by IS NULL
            LIMIT 1
        ");
        $stmt->execute([$employeeCreatedBy]);
        $otherGuest = $stmt->fetch();
        
        if ($otherGuest) {
            echo "<div class='test-box warning'>
                    <h4>⚠️ اختبار 6: دعوة من مشرف آخر (يجب رفضها)</h4>
                    <p><strong>اسم الضيف:</strong> {$otherGuest['name']}</p>
                    <p><strong>كود الدعوة:</strong> {$otherGuest['code']}</p>
                    <p><strong>المشرف:</strong> " . ($otherGuest['creator_name'] ?: 'غير محدد') . "</p>
                    <p><strong>ملاحظة:</strong> هذه الدعوة يجب أن يتم رفضها عند محاولة مسحها</p>
                  </div>";
        }
    }
} catch (PDOException $e) {
    echo "<div class='test-box error'>
            <h4>❌ خطأ في اختبار البحث</h4>
            <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "<div class='test-box'>
        <h3>🎯 النتيجة</h3>
        <p>إذا ظهرت جميع الاختبارات بعلامة ✅ فإن مشكلة SQL تم حلها بنجاح!</p>
        <p><a href='employee_dashboard.php' style='color: #007bff;'>← العودة للوحة تحكم الموظف</a></p>
      </div>";

echo "</body></html>";
?>
