# 📷 دليل استخدام الكاميرا وماسح QR

## 🎯 نظرة عامة

تم إضافة ماسح QR Code متقدم لقسم الموظف يدعم:
- 📱 **مسح QR Code** بالكاميرا مباشرة
- 🔄 **تبديل بين الكاميرات** (أمامية/خلفية)
- ⚡ **مسح تلقائي** وإرسال فوري
- 🎨 **واجهة جميلة** مع إطار مسح متحرك

---

## 🚀 كيفية الاستخدام

### للموظف:
1. **اذهب إلى لوحة تحكم الموظف:**
   ```
   http://localhost/اسم_المجلد/employee_dashboard.php
   ```

2. **اضغط "تشغيل الكاميرا"**
   - سيطلب المتصفح إذن الوصول للكاميرا
   - اضغط "السماح" أو "Allow"

3. **وجه الكاميرا نحو كود QR**
   - ستظهر كاميرا مع إطار مسح أخضر
   - ضع كود QR داخل الإطار
   - سيتم المسح تلقائياً

4. **تسجيل الحضور**
   - عند مسح الكود بنجاح، سيتم إرسال النموذج تلقائياً
   - ستظهر رسالة تأكيد الحضور

---

## 🔧 المميزات التقنية

### ماسح QR متقدم
- **مكتبة jsQR** لمسح أكواد QR
- **دعم جميع أنواع QR Code** 
- **مسح سريع ودقيق**
- **معالجة أخطاء متقدمة**

### إدارة الكاميرا
- **اكتشاف تلقائي** للكاميرات المتاحة
- **تفضيل الكاميرا الخلفية** (أفضل للمسح)
- **تبديل سهل** بين الكاميرات
- **إيقاف تلقائي** عند إغلاق الصفحة

### واجهة المستخدم
- **إطار مسح متحرك** يوضح منطقة المسح
- **رسائل حالة واضحة** لكل خطوة
- **أزرار تحكم بديهية**
- **تصميم متجاوب** للهواتف والأجهزة اللوحية

---

## 🛠️ استكشاف الأخطاء

### المشاكل الشائعة:

#### 1. "المتصفح لا يدعم الكاميرا"
**الأسباب:**
- متصفح قديم
- عدم دعم WebRTC

**الحلول:**
- استخدم متصفح حديث (Chrome, Firefox, Safari, Edge)
- تأكد من تحديث المتصفح

#### 2. "فشل في تشغيل الكاميرا"
**الأسباب:**
- عدم السماح بالوصول للكاميرا
- كاميرا مستخدمة في تطبيق آخر
- مشاكل في الأذونات

**الحلول:**
- اضغط "السماح" عند طلب الإذن
- أغلق التطبيقات الأخرى التي تستخدم الكاميرا
- أعد تحميل الصفحة وحاول مرة أخرى

#### 3. "لا يتم مسح الكود"
**الأسباب:**
- كود QR غير واضح أو تالف
- إضاءة ضعيفة
- كاميرا غير مركزة

**الحلول:**
- تأكد من وضوح كود QR
- حسن الإضاءة
- اقترب أو ابتعد عن الكود
- جرب زاوية مختلفة

#### 4. "الكاميرا لا تعمل على HTTPS"
**الأسباب:**
- المتصفحات الحديثة تتطلب HTTPS للكاميرا

**الحلول:**
- استخدم `https://localhost` بدلاً من `http://localhost`
- أو استخدم `127.0.0.1` بدلاً من `localhost`

---

## 🧪 اختبار النظام

### صفحة اختبار الكاميرا:
```
http://localhost/اسم_المجلد/test_camera.php
```

**ما تختبره:**
- ✅ دعم المتصفح للكاميرا
- ✅ قائمة الكاميرات المتاحة
- ✅ تشغيل/إيقاف الكاميرا
- ✅ مسح أكواد QR تجريبية
- ✅ تبديل بين الكاميرات

---

## 📱 المتصفحات المدعومة

### ✅ مدعوم بالكامل:
- **Chrome 53+** (الأفضل)
- **Firefox 36+**
- **Safari 11+**
- **Edge 12+**

### ⚠️ دعم محدود:
- **Internet Explorer** (غير مدعوم)
- **متصفحات قديمة**

### 📱 الهواتف الذكية:
- **Android Chrome 53+**
- **iOS Safari 11+**
- **Samsung Internet 6.2+**

---

## 🔒 الأمان والخصوصية

### حماية البيانات:
- ✅ **لا يتم حفظ الصور** - المسح يتم في الذاكرة فقط
- ✅ **لا يتم إرسال بيانات الكاميرا** للخادم
- ✅ **إيقاف تلقائي** للكاميرا عند إغلاق الصفحة
- ✅ **طلب إذن صريح** من المستخدم

### أفضل الممارسات:
- استخدم HTTPS في البيئة الإنتاجية
- تأكد من تحديث المتصفح
- لا تشارك الشاشة أثناء المسح

---

## 🎨 التخصيص

### تعديل إعدادات الكاميرا:
في ملف `assets/qr-scanner.js`:
```javascript
const constraints = {
    video: {
        facingMode: 'environment', // أو 'user' للكاميرا الأمامية
        width: { ideal: 640 },
        height: { ideal: 480 }
    }
};
```

### تعديل سرعة المسح:
```javascript
this.scanInterval = setInterval(() => {
    this.scanFrame();
}, 100); // كل 100ms - قلل الرقم للمسح الأسرع
```

### تعديل حجم إطار المسح:
في CSS:
```css
.qr-scan-frame {
    width: 250px;  /* عرض الإطار */
    height: 250px; /* ارتفاع الإطار */
}
```

---

## 📊 الإحصائيات والمراقبة

### في لوحة تحكم الموظف:
- **عدد ما مسحه اليوم** - إحصائية يومية
- **إجمالي ما مسحه** - إحصائية تراكمية  
- **آخر عمليات المسح** - قائمة بآخر 10 عمليات
- **وقت كل عملية مسح** - تسجيل دقيق للوقت

### في قاعدة البيانات:
- حقل `scanned_by` - من قام بالمسح
- حقل `scanned_at` - وقت المسح
- جدول `activity_log` - سجل مفصل للعمليات

---

## 🎉 النتيجة النهائية

**الآن لدى الموظفين:**
- ✅ **ماسح QR احترافي** بالكاميرا
- ✅ **واجهة سهلة الاستخدام**
- ✅ **مسح سريع ودقيق**
- ✅ **دعم جميع الأجهزة**
- ✅ **تسجيل حضور فوري**

**مثالي للاستخدام في:**
- 🏢 **المؤتمرات والفعاليات**
- 🏪 **نقاط الدخول والاستقبال**
- 🎫 **التحقق من التذاكر**
- 📱 **أي مكان يحتاج مسح QR سريع**
