<?php
require_once 'config.php';
require_once 'qr_generator.php';

// التحقق من تسجيل الدخول
requireSupervisor();

$pdo = getDBConnection();
$message = '';
$results = [];

// معالجة إعادة توليد أكواد QR
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // جلب جميع الضيوف
        $stmt = $pdo->query("SELECT * FROM guests ORDER BY created_at DESC");
        $guests = $stmt->fetchAll();
        
        $success = 0;
        $failed = 0;
        
        foreach ($guests as $guest) {
            $code = $guest['code'];
            $verifyUrl = SITE_URL . "/verify.php?code=" . $code;
            
            // محاولة توليد كود QR
            $qrPath = generateAdvancedQRCode($code, $verifyUrl);
            
            if ($qrPath) {
                $results[] = [
                    'name' => $guest['name'],
                    'code' => $code,
                    'status' => 'success',
                    'path' => $qrPath
                ];
                $success++;
            } else {
                $results[] = [
                    'name' => $guest['name'],
                    'code' => $code,
                    'status' => 'failed',
                    'path' => null
                ];
                $failed++;
            }
        }
        
        $message = showAlert("تم إعادة توليد {$success} كود QR بنجاح، فشل في {$failed} كود", 
                           $failed == 0 ? 'success' : 'warning');
        
    } catch (PDOException $e) {
        $message = showAlert('خطأ في جلب البيانات: ' . $e->getMessage(), 'error');
    }
}

// جلب إحصائيات أكواد QR
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM guests");
    $totalGuests = $stmt->fetchColumn();
    
    $existingQR = 0;
    $missingQR = 0;
    
    $stmt = $pdo->query("SELECT code FROM guests");
    $codes = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($codes as $code) {
        if (file_exists("qrcodes/{$code}.png") || file_exists("qrcodes/{$code}.html")) {
            $existingQR++;
        } else {
            $missingQR++;
        }
    }
    
} catch (PDOException $e) {
    $totalGuests = $existingQR = $missingQR = 0;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة توليد أكواد QR - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>🔄 إعادة توليد أكواد QR</h1>
                <div class="header-actions">
                    <a href="dashboard.php" class="btn btn-secondary">🔙 العودة للوحة التحكم</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <!-- الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-info">
                    <h3><?php echo $totalGuests; ?></h3>
                    <p>إجمالي الضيوف</p>
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon">✅</div>
                <div class="stat-info">
                    <h3><?php echo $existingQR; ?></h3>
                    <p>أكواد QR موجودة</p>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">⚠️</div>
                <div class="stat-info">
                    <h3><?php echo $missingQR; ?></h3>
                    <p>أكواد QR مفقودة</p>
                </div>
            </div>
        </div>

        <!-- نموذج إعادة التوليد -->
        <div class="form-container">
            <h3>إعادة توليد جميع أكواد QR</h3>
            <p>هذه العملية ستحاول إعادة توليد أكواد QR لجميع الضيوف المسجلين في النظام.</p>
            
            <?php if ($missingQR > 0): ?>
                <div class="alert alert-warning">
                    ⚠️ يوجد <?php echo $missingQR; ?> كود QR مفقود
                </div>
            <?php endif; ?>
            
            <form method="POST" onsubmit="return confirm('هل أنت متأكد من إعادة توليد جميع أكواد QR؟');">
                <button type="submit" class="btn btn-primary">
                    🔄 إعادة توليد جميع أكواد QR
                </button>
            </form>
        </div>

        <!-- نتائج العملية -->
        <?php if (!empty($results)): ?>
            <div class="table-container">
                <h3>نتائج إعادة التوليد</h3>
                <table class="guests-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>اسم الضيف</th>
                            <th>كود الدعوة</th>
                            <th>الحالة</th>
                            <th>المسار</th>
                            <th>الإجراء</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($results as $index => $result): ?>
                            <tr class="<?php echo $result['status'] === 'success' ? 'scanned' : ''; ?>">
                                <td><?php echo $index + 1; ?></td>
                                <td><?php echo htmlspecialchars($result['name']); ?></td>
                                <td><?php echo htmlspecialchars($result['code']); ?></td>
                                <td>
                                    <?php if ($result['status'] === 'success'): ?>
                                        <span class="status-badge success">✅ نجح</span>
                                    <?php else: ?>
                                        <span class="status-badge pending">❌ فشل</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($result['path']): ?>
                                        <code><?php echo htmlspecialchars($result['path']); ?></code>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($result['status'] === 'success' && $result['path']): ?>
                                        <?php if (strpos($result['path'], '.png') !== false): ?>
                                            <a href="<?php echo $result['path']; ?>" target="_blank" class="btn btn-sm btn-info">
                                                📱 عرض
                                            </a>
                                        <?php else: ?>
                                            <a href="<?php echo $result['path']; ?>" target="_blank" class="btn btn-sm btn-success">
                                                🔗 عرض
                                            </a>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <a href="qr_generator.php?code=<?php echo $result['code']; ?>&url=<?php echo urlencode(SITE_URL . "/verify.php?code=" . $result['code']); ?>" 
                                           target="_blank" class="btn btn-sm btn-warning">
                                            ⚡ إنشاء
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <!-- معلومات إضافية -->
        <div class="form-container">
            <h3>ℹ️ معلومات مهمة</h3>
            <ul style="text-align: right; line-height: 2;">
                <li><strong>أكواد QR بصيغة PNG:</strong> تعمل بشكل أفضل وتحتاج اتصال إنترنت للتوليد</li>
                <li><strong>أكواد QR بصيغة HTML:</strong> تعمل محلياً وتحتوي على رابط مباشر</li>
                <li><strong>في حالة فشل التوليد:</strong> يمكن للضيوف استخدام الرابط مباشرة</li>
                <li><strong>الروابط تعمل دائماً:</strong> حتى بدون أكواد QR</li>
            </ul>
        </div>
    </div>
</body>
</html>
