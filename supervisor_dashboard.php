<?php
require_once 'config.php';

// التحقق من صلاحية المشرف
requireSupervisor();

$pdo = getDBConnection();
$message = '';

// معالجة حذف ضيف
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $guestId = (int)$_GET['delete'];
    
    try {
        // التحقق من أن الضيف تم إنشاؤه بواسطة هذا المشرف أو أنه مدير
        $whereClause = isAdmin() ? "id = ?" : "id = ? AND created_by = ?";
        $params = isAdmin() ? [$guestId] : [$guestId, $_SESSION['user_id']];
        
        $stmt = $pdo->prepare("SELECT code FROM guests WHERE {$whereClause}");
        $stmt->execute($params);
        $guest = $stmt->fetch();
        
        if ($guest) {
            // حذف ملف QR إذا كان موجوداً
            $qrFiles = [
                "qrcodes/{$guest['code']}.png",
                "qrcodes/{$guest['code']}.html",
                "qrcodes/{$guest['code']}.txt"
            ];
            
            foreach ($qrFiles as $qrFile) {
                if (file_exists($qrFile)) {
                    unlink($qrFile);
                }
            }
            
            // حذف الضيف من قاعدة البيانات
            $stmt = $pdo->prepare("DELETE FROM guests WHERE {$whereClause}");
            $stmt->execute($params);
            
            logActivity('delete_guest', 'guest', $guestId, "تم حذف الضيف");
            $message = showAlert('تم حذف الضيف بنجاح', 'success');
        } else {
            $message = showAlert('غير مسموح لك بحذف هذا الضيف', 'error');
        }
    } catch (PDOException $e) {
        $message = showAlert('خطأ في حذف الضيف: ' . $e->getMessage(), 'error');
    }
}

// جلب إحصائيات المشرف
try {
    if (isAdmin()) {
        // المدير يرى جميع الإحصائيات
        $totalGuests = $pdo->query("SELECT COUNT(*) FROM guests")->fetchColumn();
        $scannedGuests = $pdo->query("SELECT COUNT(*) FROM guests WHERE scanned = 1")->fetchColumn();
        $myGuests = $pdo->query("SELECT COUNT(*) FROM guests")->fetchColumn();
    } else {
        // المشرف يرى إحصائياته فقط
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE guests.created_by = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $myGuests = $stmt->fetchColumn();

        $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE guests.created_by = ? AND scanned = 1");
        $stmt->execute([$_SESSION['user_id']]);
        $scannedGuests = $stmt->fetchColumn();
        
        $totalGuests = $myGuests;
    }
    
    $pendingGuests = $totalGuests - $scannedGuests;
} catch (PDOException $e) {
    $totalGuests = $scannedGuests = $pendingGuests = $myGuests = 0;
}

// جلب قائمة الضيوف
$searchTerm = sanitizeInput($_GET['search'] ?? '');
$whereClause = '';
$params = [];

if (isAdmin()) {
    // المدير يرى جميع الضيوف
    if (!empty($searchTerm)) {
        $whereClause = "WHERE name LIKE ? OR phone LIKE ?";
        $params = ["%{$searchTerm}%", "%{$searchTerm}%"];
    }
} else {
    // المشرف يرى ضيوفه فقط
    if (!empty($searchTerm)) {
        $whereClause = "WHERE g.created_by = ? AND (g.name LIKE ? OR g.phone LIKE ?)";
        $params = [$_SESSION['user_id'], "%{$searchTerm}%", "%{$searchTerm}%"];
    } else {
        $whereClause = "WHERE g.created_by = ?";
        $params = [$_SESSION['user_id']];
    }
}

try {
    $sql = "SELECT g.id, g.name, g.phone, g.notes, g.code, g.scanned, g.scanned_at,
                   g.created_by, g.created_at, g.updated_at, u.full_name as created_by_name
            FROM guests g
            LEFT JOIN users u ON g.created_by = u.id
            {$whereClause}
            ORDER BY g.created_at DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $guests = $stmt->fetchAll();
} catch (PDOException $e) {
    $guests = [];
    $message = showAlert('خطأ في جلب البيانات: ' . $e->getMessage(), 'error');
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المشرف - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>👨‍💼 لوحة تحكم المشرف</h1>
                <div class="header-actions">
                    <span>مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                    <?php if (isAdmin()): ?>
                        <a href="admin_dashboard.php" class="btn btn-warning">👑 إدارة المستخدمين</a>
                    <?php endif; ?>
                    <a href="test_supervisor_sql_fix.php" class="btn btn-info">🔧 اختبار SQL</a>
                    <a href="logout.php" class="btn btn-secondary">تسجيل الخروج</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <!-- الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-info">
                    <h3><?php echo $totalGuests; ?></h3>
                    <p><?php echo isAdmin() ? 'إجمالي المدعوين' : 'دعواتي'; ?></p>
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon">✅</div>
                <div class="stat-info">
                    <h3><?php echo $scannedGuests; ?></h3>
                    <p>تم الحضور</p>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">⏳</div>
                <div class="stat-info">
                    <h3><?php echo $pendingGuests; ?></h3>
                    <p>لم يحضر بعد</p>
                </div>
            </div>
            <?php if (!isAdmin()): ?>
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-info">
                        <h3><?php echo $totalGuests > 0 ? round(($scannedGuests / $totalGuests) * 100, 1) : 0; ?>%</h3>
                        <p>معدل الحضور</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- أدوات التحكم -->
        <div class="controls">
            <div class="controls-left">
                <a href="add_guest.php" class="btn btn-primary">
                    ➕ إضافة ضيف جديد
                </a>
                <a href="bulk_invitations.php" class="btn btn-success">
                    📦 إنشاء دعوات متعددة
                </a>
                <a href="manage_bulk.php" class="btn btn-info">
                    📊 إدارة المجموعات
                </a>
                <a href="export.php" class="btn btn-info">
                    📥 تصدير البيانات
                </a>
                <a href="regenerate_qr.php" class="btn btn-warning">
                    🔄 إعادة توليد QR
                </a>
                <a href="print_invitations.php" target="_blank" class="btn btn-secondary">
                    🖨️ طباعة الدعوات
                </a>
            </div>
            <div class="controls-right">
                <form method="GET" class="search-form">
                    <input type="text" name="search" placeholder="البحث بالاسم أو رقم الجوال..." 
                           value="<?php echo htmlspecialchars($searchTerm); ?>">
                    <button type="submit" class="btn btn-secondary">🔍 بحث</button>
                    <?php if (!empty($searchTerm)): ?>
                        <a href="supervisor_dashboard.php" class="btn btn-light">✖️ إلغاء</a>
                    <?php endif; ?>
                </form>
            </div>
        </div>

        <!-- جدول الضيوف -->
        <div class="table-container">
            <table class="guests-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>رقم الجوال</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                        <th>تاريخ الحضور</th>
                        <?php if (isAdmin()): ?>
                            <th>أنشأه</th>
                        <?php endif; ?>
                        <th>كود QR</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($guests)): ?>
                        <tr>
                            <td colspan="<?php echo isAdmin() ? '9' : '8'; ?>" class="no-data">
                                <?php echo empty($searchTerm) ? 'لا توجد دعوات مضافة بعد' : 'لا توجد نتائج للبحث'; ?>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($guests as $index => $guest): ?>
                            <tr class="<?php echo $guest['scanned'] ? 'scanned' : ''; ?>">
                                <td><?php echo $index + 1; ?></td>
                                <td class="guest-name"><?php echo htmlspecialchars($guest['name']); ?></td>
                                <td><?php echo htmlspecialchars($guest['phone'] ?? '-'); ?></td>
                                <td>
                                    <?php if ($guest['scanned']): ?>
                                        <span class="status-badge success">✅ حضر</span>
                                    <?php else: ?>
                                        <span class="status-badge pending">⏳ لم يحضر</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($guest['created_at'])); ?></td>
                                <td>
                                    <?php echo $guest['scanned_at'] ? date('Y-m-d H:i', strtotime($guest['scanned_at'])) : '-'; ?>
                                </td>
                                <?php if (isAdmin()): ?>
                                    <td><?php echo htmlspecialchars($guest['created_by_name'] ?? 'غير محدد'); ?></td>
                                <?php endif; ?>
                                <td>
                                    <?php 
                                    $qrFile = "qrcodes/{$guest['code']}.png";
                                    $qrHtmlFile = "qrcodes/{$guest['code']}.html";
                                    
                                    if (file_exists($qrFile)): ?>
                                        <a href="<?php echo $qrFile; ?>" target="_blank" class="btn btn-sm btn-info">
                                            📱 عرض QR
                                        </a>
                                    <?php elseif (file_exists($qrHtmlFile)): ?>
                                        <a href="<?php echo $qrHtmlFile; ?>" target="_blank" class="btn btn-sm btn-success">
                                            🔗 عرض QR
                                        </a>
                                    <?php else: ?>
                                        <a href="qr_generator.php?code=<?php echo $guest['code']; ?>&url=<?php echo urlencode(SITE_URL . "/verify.php?code=" . $guest['code']); ?>" 
                                           target="_blank" class="btn btn-sm btn-warning">
                                            ⚡ إنشاء QR
                                        </a>
                                    <?php endif; ?>
                                </td>
                                <td class="actions">
                                    <?php if (isAdmin() || $guest['created_by'] == $_SESSION['user_id']): ?>
                                        <a href="edit_guest.php?id=<?php echo $guest['id']; ?>" 
                                           class="btn btn-sm btn-warning">✏️ تعديل</a>
                                        <a href="?delete=<?php echo $guest['id']; ?>" 
                                           class="btn btn-sm btn-danger"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا الضيف؟')">🗑️ حذف</a>
                                    <?php else: ?>
                                        <span class="btn btn-sm btn-light">🔒 محمي</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
