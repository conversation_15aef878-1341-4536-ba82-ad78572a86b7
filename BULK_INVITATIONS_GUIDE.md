# 📦 دليل الدعوات المتعددة - الميزة الجديدة

## 🎯 ما هي الدعوات المتعددة؟

ميزة جديدة تتيح لك إنشاء عدد كبير من الدعوات بسرعة بدون الحاجة لإدخال أسماء محددة. مثالية للفعاليات الكبيرة والمؤتمرات.

## ✨ المميزات الجديدة

### 📦 إنشاء دعوات متعددة (`bulk_invitations.php`)
- **إنشاء حتى 1000 دعوة** في عملية واحدة
- **ترقيم تلقائي** للأسماء (ضيف رقم 1، ضيف رقم 2...)
- **بادئة قابلة للتخصيص** (ضيف، مدعو، حاضر...)
- **ملاحظات عامة** تطبق على جميع الدعوات
- **توليد أكواد QR اختياري** (لتوفير الوقت)
- **معاينة فورية** للأسماء المتوقعة
- **نتائج مفصلة** مع إحصائيات

### 📊 إدارة المجموعات (`manage_bulk.php`)
- **عرض جميع مجموعات الدعوات** مع الإحصائيات
- **دوائر تقدم ملونة** تظهر معدل الحضور
- **إحصائيات مفصلة** لكل مجموعة
- **إدارة شاملة** (عرض، تصدير، طباعة، حذف)
- **حذف مجموعات كاملة** بأمان

### 🖨️ طباعة الدعوات (`print_invitations.php`)
- **تصميم احترافي** للطباعة
- **تخطيط بطاقات** مناسب للقص
- **معلومات كاملة** (الاسم، الكود، الرابط)
- **مكان مخصص لكود QR**
- **طباعة مجموعات محددة** أو جميع الدعوات

## 🚀 كيفية الاستخدام

### 1️⃣ إنشاء دعوات متعددة

```
لوحة التحكم → 📦 إنشاء دعوات متعددة
```

**الخطوات:**
1. أدخل **عدد الدعوات** (1-1000)
2. اختر **بادئة الاسم** (مثل: ضيف، مدعو)
3. أضف **ملاحظات عامة** (اختياري)
4. اختر **توليد أكواد QR** (اختياري - يستغرق وقتاً أطول)
5. اضغط **"إنشاء الدعوات"**

**مثال النتيجة:**
- ضيف رقم 1 - كود: INV_ABC123_4567
- ضيف رقم 2 - كود: INV_DEF456_7890
- ضيف رقم 3 - كود: INV_GHI789_0123
- ...

### 2️⃣ إدارة المجموعات

```
لوحة التحكم → 📊 إدارة المجموعات
```

**ما ستراه:**
- **قائمة جميع المجموعات** مع التواريخ
- **إحصائيات مفصلة** (العدد الكلي، الحاضرين، المعدل)
- **دائرة تقدم ملونة** تظهر معدل الحضور
- **أزرار إدارة** لكل مجموعة

**الإجراءات المتاحة:**
- 👁️ **عرض الدعوات** - يفتح لوحة التحكم مع فلترة
- 📥 **تصدير المجموعة** - تحميل بيانات المجموعة
- 🔄 **إعادة توليد QR** - للأكواد المفقودة
- 🖨️ **طباعة الدعوات** - طباعة بطاقات الدعوة
- 🗑️ **حذف المجموعة** - حذف جميع دعوات المجموعة

### 3️⃣ طباعة الدعوات

```
إدارة المجموعات → 🖨️ طباعة الدعوات
أو
لوحة التحكم → 🖨️ طباعة الدعوات
```

**المميزات:**
- **تصميم بطاقات** جاهز للقص
- **معلومات كاملة** على كل بطاقة
- **مكان مخصص** لكود QR
- **رابط التحقق** مطبوع
- **تاريخ الإنشاء** والملاحظات

## 📋 أمثلة عملية

### مثال 1: مؤتمر كبير
```
العدد: 500
البادئة: مشارك
الملاحظات: مؤتمر التقنية 2024
توليد QR: نعم
```
**النتيجة:** مشارك رقم 1، مشارك رقم 2... حتى مشارك رقم 500

### مثال 2: حفل زفاف
```
العدد: 150
البادئة: ضيف
الملاحظات: حفل زفاف أحمد وفاطمة
توليد QR: لا (لتوفير الوقت)
```
**النتيجة:** ضيف رقم 1، ضيف رقم 2... حتى ضيف رقم 150

### مثال 3: ورشة عمل
```
العدد: 50
البادئة: متدرب
الملاحظات: ورشة البرمجة المتقدمة
توليد QR: نعم
```
**النتيجة:** متدرب رقم 1، متدرب رقم 2... حتى متدرب رقم 50

## ⚡ نصائح للاستخدام الأمثل

### 🚀 لتوفير الوقت:
- **لا تفعل توليد QR** عند الإنشاء إذا كان العدد كبير
- استخدم **"إعادة توليد QR"** لاحقاً عند الحاجة
- **اطبع الدعوات** أولاً ثم ولد الأكواد

### 📊 للتنظيم الأفضل:
- استخدم **بادئات واضحة** (مثل: VIP، عام، طلاب)
- أضف **ملاحظات مفيدة** تميز كل مجموعة
- **اطبع بتواريخ مختلفة** لتسهيل التتبع

### 🔒 للأمان:
- **احتفظ بنسخة احتياطية** قبل حذف المجموعات
- **تأكد من البيانات** قبل الطباعة النهائية
- **اختبر عينة صغيرة** قبل الإنشاء الكبير

## 🛠️ استكشاف الأخطاء

### مشكلة: فشل في إنشاء بعض الدعوات
**الحل:** تحقق من مساحة قاعدة البيانات وأعد المحاولة

### مشكلة: أكواد QR لا تظهر
**الحل:** استخدم "إعادة توليد QR" من إدارة المجموعات

### مشكلة: الطباعة غير منسقة
**الحل:** تأكد من إعدادات الطابعة (A4، عمودي)

## 📈 الإحصائيات والتقارير

### في إدارة المجموعات ستجد:
- **إجمالي الدعوات** في كل مجموعة
- **عدد الحاضرين** الفعلي
- **عدد المتبقين** (لم يحضروا)
- **معدل الحضور** بالنسبة المئوية
- **تاريخ الإنشاء** والفترة الزمنية

### في التصدير ستحصل على:
- **ملف CSV** لجميع بيانات المجموعة
- **ملف JSON** للاستخدام البرمجي
- **فلترة حسب المجموعة** أو التاريخ

---

## 🎉 الخلاصة

الآن يمكنك:
- ✅ **إنشاء مئات الدعوات** في دقائق
- ✅ **إدارة مجموعات منفصلة** بسهولة
- ✅ **طباعة دعوات احترافية** جاهزة للاستخدام
- ✅ **تتبع معدلات الحضور** لكل مجموعة
- ✅ **تصدير وطباعة** بيانات محددة

**هذه الميزة تجعل النظام مناسباً للفعاليات الكبيرة والمؤسسات!** 🚀
