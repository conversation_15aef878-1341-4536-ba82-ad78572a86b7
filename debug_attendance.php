<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
if (!isUserLoggedIn()) {
    header('Location: login.php');
    exit();
}

$pdo = getDBConnection();

echo "<h1>🔍 تشخيص مشكلة تحضير الإجازة</h1>";
echo "<hr>";

// فحص البيانات المرسلة
echo "<h2>1️⃣ فحص البيانات المرسلة</h2>";

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>📨 البيانات المستلمة:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    if (isset($_POST['scan_code'])) {
        echo "<p><strong>✅ تم استلام طلب تحضير الإجازة</strong></p>";
        
        $rawCode = sanitizeInput($_POST['code'] ?? '');
        echo "<p><strong>الكود الخام:</strong> <code>" . htmlspecialchars($rawCode) . "</code></p>";
        
        if (!empty($rawCode)) {
            // استخراج الكود
            function extractCodeFromInput($input) {
                $input = trim($input);
                
                if (strpos($input, 'verify.php?code=') !== false) {
                    $parsed = parse_url($input);
                    if (isset($parsed['query'])) {
                        parse_str($parsed['query'], $params);
                        if (isset($params['code'])) {
                            return $params['code'];
                        }
                    }
                }
                
                if (preg_match('/code=([^&\s]+)/', $input, $matches)) {
                    return $matches[1];
                }
                
                return $input;
            }
            
            $code = extractCodeFromInput($rawCode);
            echo "<p><strong>الكود المستخرج:</strong> <code>" . htmlspecialchars($code) . "</code></p>";
            
            // البحث عن الضيف
            echo "<h3>🔍 البحث في قاعدة البيانات:</h3>";
            
            try {
                $stmt = $pdo->prepare("SELECT * FROM guests WHERE code = ?");
                $stmt->execute([$code]);
                $guest = $stmt->fetch();
                
                if ($guest) {
                    echo "<p><strong>✅ تم العثور على الضيف:</strong></p>";
                    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                    echo "<tr><th>الحقل</th><th>القيمة</th></tr>";
                    foreach ($guest as $key => $value) {
                        echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
                    }
                    echo "</table>";
                    
                    // فحص حالة الحضور
                    if ($guest['scanned'] == 1) {
                        echo "<p style='color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px;'>";
                        echo "<strong>⚠️ تحذير:</strong> هذا الضيف حضر من قبل في: " . $guest['scanned_at'];
                        echo "</p>";
                    } else {
                        echo "<p style='color: #155724; background: #d4edda; padding: 10px; border-radius: 5px;'>";
                        echo "<strong>✅ جاهز للتحضير:</strong> الضيف لم يحضر بعد";
                        echo "</p>";
                        
                        // محاولة تحضير الإجازة
                        echo "<h3>🎯 محاولة تحضير الإجازة:</h3>";
                        
                        $stmt = $pdo->prepare("UPDATE guests SET scanned = 1, scanned_at = NOW(), scanned_by = ? WHERE id = ?");
                        $updateResult = $stmt->execute([$_SESSION['user_id'], $guest['id']]);
                        
                        if ($updateResult) {
                            $affectedRows = $stmt->rowCount();
                            echo "<p style='color: #155724; background: #d4edda; padding: 10px; border-radius: 5px;'>";
                            echo "<strong>✅ نجح التحديث!</strong> تم تحديث $affectedRows صف";
                            echo "</p>";
                            
                            // التحقق من التحديث
                            $stmt = $pdo->prepare("SELECT * FROM guests WHERE id = ?");
                            $stmt->execute([$guest['id']]);
                            $updatedGuest = $stmt->fetch();
                            
                            echo "<p><strong>📊 حالة الضيف بعد التحديث:</strong></p>";
                            echo "<ul>";
                            echo "<li><strong>scanned:</strong> " . $updatedGuest['scanned'] . "</li>";
                            echo "<li><strong>scanned_at:</strong> " . $updatedGuest['scanned_at'] . "</li>";
                            echo "<li><strong>scanned_by:</strong> " . $updatedGuest['scanned_by'] . "</li>";
                            echo "</ul>";
                            
                            // تسجيل النشاط
                            try {
                                logActivity('scan_guest', 'guest', $guest['id'], "تم تسجيل حضور الضيف: {$guest['name']}");
                                echo "<p style='color: #155724;'>✅ تم تسجيل النشاط في السجل</p>";
                            } catch (Exception $e) {
                                echo "<p style='color: #721c24;'>❌ فشل في تسجيل النشاط: " . $e->getMessage() . "</p>";
                            }
                            
                        } else {
                            echo "<p style='color: #721c24; background: #f8d7da; padding: 10px; border-radius: 5px;'>";
                            echo "<strong>❌ فشل التحديث!</strong>";
                            echo "</p>";
                            
                            // معلومات إضافية عن الخطأ
                            $errorInfo = $stmt->errorInfo();
                            if ($errorInfo[0] !== '00000') {
                                echo "<p><strong>تفاصيل الخطأ:</strong></p>";
                                echo "<ul>";
                                echo "<li><strong>SQLSTATE:</strong> " . $errorInfo[0] . "</li>";
                                echo "<li><strong>Driver Error Code:</strong> " . $errorInfo[1] . "</li>";
                                echo "<li><strong>Driver Error Message:</strong> " . $errorInfo[2] . "</li>";
                                echo "</ul>";
                            }
                        }
                    }
                    
                } else {
                    echo "<p style='color: #721c24; background: #f8d7da; padding: 10px; border-radius: 5px;'>";
                    echo "<strong>❌ لم يتم العثور على الضيف</strong>";
                    echo "</p>";
                    
                    // البحث عن أكواد مشابهة
                    echo "<h3>🔍 البحث عن أكواد مشابهة:</h3>";
                    $stmt = $pdo->prepare("SELECT code, name FROM guests WHERE code LIKE ? LIMIT 5");
                    $stmt->execute(['%' . substr($code, 0, 10) . '%']);
                    $similarCodes = $stmt->fetchAll();
                    
                    if ($similarCodes) {
                        echo "<p><strong>أكواد مشابهة موجودة:</strong></p>";
                        echo "<ul>";
                        foreach ($similarCodes as $similar) {
                            echo "<li><code>" . htmlspecialchars($similar['code']) . "</code> - " . htmlspecialchars($similar['name']) . "</li>";
                        }
                        echo "</ul>";
                    } else {
                        echo "<p>لا توجد أكواد مشابهة</p>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<p style='color: #721c24; background: #f8d7da; padding: 10px; border-radius: 5px;'>";
                echo "<strong>❌ خطأ في قاعدة البيانات:</strong> " . $e->getMessage();
                echo "</p>";
            }
            
        } else {
            echo "<p style='color: #721c24;'><strong>❌ لم يتم إرسال كود</strong></p>";
        }
    } else {
        echo "<p style='color: #721c24;'><strong>❌ لم يتم استلام طلب تحضير</strong></p>";
    }
    echo "</div>";
} else {
    echo "<p>لم يتم إرسال أي بيانات بعد. استخدم النموذج أدناه للاختبار.</p>";
}

// فحص حالة قاعدة البيانات
echo "<h2>2️⃣ فحص حالة قاعدة البيانات</h2>";

try {
    // فحص الاتصال
    $stmt = $pdo->query("SELECT 1");
    echo "<p style='color: #155724;'>✅ الاتصال بقاعدة البيانات يعمل</p>";
    
    // فحص جدول guests
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM guests");
    $total = $stmt->fetchColumn();
    echo "<p><strong>إجمالي الدعوات:</strong> $total</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as scanned FROM guests WHERE scanned = 1");
    $scanned = $stmt->fetchColumn();
    echo "<p><strong>الحضور المسجل:</strong> $scanned</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as pending FROM guests WHERE scanned = 0");
    $pending = $stmt->fetchColumn();
    echo "<p><strong>في انتظار الحضور:</strong> $pending</p>";
    
    // عرض آخر 5 دعوات
    echo "<h3>📋 آخر 5 دعوات:</h3>";
    $stmt = $pdo->query("SELECT id, name, code, scanned, scanned_at FROM guests ORDER BY id DESC LIMIT 5");
    $recentGuests = $stmt->fetchAll();
    
    if ($recentGuests) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الكود</th><th>حضر؟</th><th>وقت الحضور</th></tr>";
        foreach ($recentGuests as $guest) {
            $status = $guest['scanned'] ? '✅ نعم' : '⏳ لا';
            echo "<tr>";
            echo "<td>" . $guest['id'] . "</td>";
            echo "<td>" . htmlspecialchars($guest['name']) . "</td>";
            echo "<td><code>" . htmlspecialchars($guest['code']) . "</code></td>";
            echo "<td>$status</td>";
            echo "<td>" . ($guest['scanned_at'] ?: '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: #721c24;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// فحص صلاحيات المستخدم
echo "<h2>3️⃣ فحص صلاحيات المستخدم</h2>";

echo "<p><strong>معرف المستخدم:</strong> " . $_SESSION['user_id'] . "</p>";
echo "<p><strong>اسم المستخدم:</strong> " . $_SESSION['username'] . "</p>";
echo "<p><strong>نوع المستخدم:</strong> " . $_SESSION['user_type'] . "</p>";

if ($_SESSION['user_type'] !== 'employee') {
    echo "<p style='color: #721c24;'>⚠️ تحذير: المستخدم ليس موظف</p>";
}

// نموذج اختبار
echo "<h2>4️⃣ نموذج اختبار</h2>";

echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
echo "<h3>اختبر تحضير إجازة:</h3>";
echo "<input type='text' name='code' placeholder='أدخل كود الإجازة' style='width: 300px; padding: 10px; margin: 10px 0;' required>";
echo "<br>";
echo "<button type='submit' name='scan_code' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🎯 اختبار التحضير</button>";
echo "</form>";

// روابط مفيدة
echo "<h2>5️⃣ روابط مفيدة</h2>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='employee_dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📱 لوحة تحكم الموظف</a>";
echo "<a href='test_scan_system.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔧 اختبار النظام</a>";
echo "<a href='setup_database.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🛠️ إعداد قاعدة البيانات</a>";
echo "</div>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    direction: rtl;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

th {
    background: #007bff;
    color: white;
    padding: 10px;
}

td {
    padding: 8px;
    border-bottom: 1px solid #eee;
}

code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    color: #e83e8c;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    border-left: 4px solid #007bff;
}

a {
    display: inline-block;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

a:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
</style>
