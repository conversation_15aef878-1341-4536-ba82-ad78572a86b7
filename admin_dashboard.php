<?php
require_once 'config.php';

// التحقق من صلاحية المدير
requireAdmin();

$pdo = getDBConnection();
$message = '';

// معالجة إنشاء مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_user'])) {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $full_name = sanitizeInput($_POST['full_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $user_type = sanitizeInput($_POST['user_type'] ?? '');
    
    if (empty($username) || empty($password) || empty($full_name) || empty($user_type)) {
        $message = showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
    } elseif (!validatePassword($password)) {
        $message = showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
    } else {
        $result = createUser($username, $password, $full_name, $email, $phone, $user_type);
        
        if ($result['success']) {
            $message = showAlert("تم إنشاء المستخدم {$full_name} بنجاح", 'success');
        } else {
            $message = showAlert($result['message'], 'error');
        }
    }
}

// معالجة تعطيل/تفعيل مستخدم
if (isset($_GET['toggle_user'])) {
    $user_id = (int)$_GET['toggle_user'];
    $action = $_GET['action'] ?? '';
    
    if ($action === 'activate' || $action === 'deactivate') {
        $status = $action === 'activate' ? 'active' : 'inactive';
        
        try {
            $stmt = $pdo->prepare("UPDATE users SET status = ? WHERE id = ? AND id != ?");
            $stmt->execute([$status, $user_id, $_SESSION['user_id']]); // لا يمكن تعطيل نفسه
            
            $action_text = $action === 'activate' ? 'تفعيل' : 'تعطيل';
            logActivity($action . '_user', 'user', $user_id, "تم {$action_text} المستخدم");
            $message = showAlert("تم {$action_text} المستخدم بنجاح", 'success');
        } catch (PDOException $e) {
            $message = showAlert('خطأ في تحديث حالة المستخدم', 'error');
        }
    }
}

// جلب الإحصائيات
try {
    $stats = [
        'total_users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'active_users' => $pdo->query("SELECT COUNT(*) FROM users WHERE status = 'active'")->fetchColumn(),
        'supervisors' => $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'supervisor'")->fetchColumn(),
        'employees' => $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'employee'")->fetchColumn(),
        'total_guests' => $pdo->query("SELECT COUNT(*) FROM guests")->fetchColumn(),
        'scanned_guests' => $pdo->query("SELECT COUNT(*) FROM guests WHERE scanned = 1")->fetchColumn(),
    ];
} catch (PDOException $e) {
    $stats = array_fill_keys(['total_users', 'active_users', 'supervisors', 'employees', 'total_guests', 'scanned_guests'], 0);
}

// جلب قائمة المستخدمين
try {
    $stmt = $pdo->query("
        SELECT u.*, creator.full_name as created_by_name 
        FROM users u 
        LEFT JOIN users creator ON u.created_by = creator.id 
        ORDER BY u.created_at DESC
    ");
    $users = $stmt->fetchAll();
} catch (PDOException $e) {
    $users = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>👑 لوحة تحكم المدير</h1>
                <div class="header-actions">
                    <span>مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                    <a href="dashboard.php" class="btn btn-info">📋 إدارة الدعوات</a>
                    <a href="logout.php" class="btn btn-secondary">تسجيل الخروج</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <!-- الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-info">
                    <h3><?php echo $stats['total_users']; ?></h3>
                    <p>إجمالي المستخدمين</p>
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon">✅</div>
                <div class="stat-info">
                    <h3><?php echo $stats['active_users']; ?></h3>
                    <p>المستخدمين النشطين</p>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">👨‍💼</div>
                <div class="stat-info">
                    <h3><?php echo $stats['supervisors']; ?></h3>
                    <p>المشرفين</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📱</div>
                <div class="stat-info">
                    <h3><?php echo $stats['employees']; ?></h3>
                    <p>الموظفين</p>
                </div>
            </div>
        </div>

        <!-- إحصائيات الدعوات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">🎫</div>
                <div class="stat-info">
                    <h3><?php echo $stats['total_guests']; ?></h3>
                    <p>إجمالي الدعوات</p>
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon">✅</div>
                <div class="stat-info">
                    <h3><?php echo $stats['scanned_guests']; ?></h3>
                    <p>تم الحضور</p>
                </div>
            </div>
        </div>

        <!-- نموذج إنشاء مستخدم جديد -->
        <div class="form-container">
            <h3>➕ إنشاء مستخدم جديد</h3>
            
            <form method="POST">
                <div class="form-row">
                    <div class="form-group">
                        <label for="username">اسم المستخدم: <span class="required">*</span></label>
                        <input type="text" id="username" name="username" required 
                               placeholder="اسم المستخدم للدخول">
                    </div>
                    
                    <div class="form-group">
                        <label for="password">كلمة المرور: <span class="required">*</span></label>
                        <input type="password" id="password" name="password" required 
                               placeholder="6 أحرف على الأقل">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="full_name">الاسم الكامل: <span class="required">*</span></label>
                        <input type="text" id="full_name" name="full_name" required 
                               placeholder="الاسم الكامل للمستخدم">
                    </div>
                    
                    <div class="form-group">
                        <label for="user_type">نوع المستخدم: <span class="required">*</span></label>
                        <select id="user_type" name="user_type" required>
                            <option value="">اختر نوع المستخدم</option>
                            <option value="supervisor">👨‍💼 مشرف</option>
                            <option value="employee">📱 موظف</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني:</label>
                        <input type="email" id="email" name="email" 
                               placeholder="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">رقم الجوال:</label>
                        <input type="tel" id="phone" name="phone" 
                               placeholder="05xxxxxxxx">
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" name="create_user" class="btn btn-primary">
                        ✅ إنشاء المستخدم
                    </button>
                </div>
            </form>
        </div>

        <!-- قائمة المستخدمين -->
        <div class="table-container">
            <h3>👥 إدارة المستخدمين</h3>
            
            <table class="guests-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم الكامل</th>
                        <th>اسم المستخدم</th>
                        <th>النوع</th>
                        <th>الحالة</th>
                        <th>آخر دخول</th>
                        <th>أنشأه</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $index => $user): ?>
                        <tr class="<?php echo $user['status'] === 'active' ? '' : 'inactive'; ?>">
                            <td><?php echo $index + 1; ?></td>
                            <td class="guest-name"><?php echo htmlspecialchars($user['full_name']); ?></td>
                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                            <td>
                                <?php
                                $types = [
                                    'admin' => '👑 مدير',
                                    'supervisor' => '👨‍💼 مشرف',
                                    'employee' => '📱 موظف'
                                ];
                                echo $types[$user['user_type']] ?? $user['user_type'];
                                ?>
                            </td>
                            <td>
                                <?php if ($user['status'] === 'active'): ?>
                                    <span class="status-badge success">✅ نشط</span>
                                <?php else: ?>
                                    <span class="status-badge pending">⏸️ معطل</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php echo $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : 'لم يدخل بعد'; ?>
                            </td>
                            <td><?php echo htmlspecialchars($user['created_by_name'] ?? 'النظام'); ?></td>
                            <td class="actions">
                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                    <?php if ($user['status'] === 'active'): ?>
                                        <a href="?toggle_user=<?php echo $user['id']; ?>&action=deactivate" 
                                           class="btn btn-sm btn-warning"
                                           onclick="return confirm('هل تريد تعطيل هذا المستخدم؟')">
                                            ⏸️ تعطيل
                                        </a>
                                    <?php else: ?>
                                        <a href="?toggle_user=<?php echo $user['id']; ?>&action=activate" 
                                           class="btn btn-sm btn-success">
                                            ✅ تفعيل
                                        </a>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="btn btn-sm btn-light">👤 أنت</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
