<?php
require_once 'config.php';

// التحقق من تسجيل دخول المسؤول
checkAdminAuth();

$pdo = getDBConnection();
$message = '';
$success = false;

// معالجة إضافة ضيف جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitizeInput($_POST['name'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $notes = sanitizeInput($_POST['notes'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($name)) {
        $message = showAlert('يرجى إدخال اسم الضيف', 'error');
    } else {
        try {
            // توليد كود فريد
            $code = generateUniqueCode();
            
            // التأكد من أن الكود فريد
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE code = ?");
            $stmt->execute([$code]);
            
            while ($stmt->fetchColumn() > 0) {
                $code = generateUniqueCode();
                $stmt->execute([$code]);
            }
            
            // إدراج الضيف في قاعدة البيانات
            $stmt = $pdo->prepare("INSERT INTO guests (name, phone, notes, code) VALUES (?, ?, ?, ?)");
            $stmt->execute([$name, $phone, $notes, $code]);
            
            // توليد كود QR
            $verifyUrl = SITE_URL . "/verify.php?code=" . $code;

            // تضمين مولد QR المحسن
            require_once 'qr_generator.php';
            $qrCodePath = generateAdvancedQRCode($code, $verifyUrl);
            
            if ($qrCodePath) {
                $message = showAlert("تم إضافة الضيف بنجاح! كود QR: {$code}", 'success');
                $success = true;
                
                // إعادة تعيين المتغيرات
                $name = $phone = $notes = '';
            } else {
                $message = showAlert('تم إضافة الضيف ولكن فشل في إنشاء كود QR', 'warning');
            }
            
        } catch (PDOException $e) {
            $message = showAlert('خطأ في إضافة الضيف: ' . $e->getMessage(), 'error');
        }
    }
}

/**
 * توليد كود QR باستخدام طرق متعددة
 */
function generateQRCode($code, $url) {
    try {
        // الطريقة الأولى: استخدام QR Server API
        $qrUrl = "https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=" . urlencode($url);

        // تحميل الصورة
        $qrImage = @file_get_contents($qrUrl);

        if ($qrImage !== false) {
            $filePath = "qrcodes/{$code}.png";
            file_put_contents($filePath, $qrImage);
            return $filePath;
        }

        // الطريقة الثانية: إنشاء QR Code بسيط باستخدام HTML/CSS
        return generateSimpleQRCode($code, $url);

    } catch (Exception $e) {
        // في حالة فشل كل الطرق، إنشاء ملف نصي بالكود
        return generateTextCode($code, $url);
    }
}

/**
 * إنشاء QR Code بديل باستخدام خدمة أخرى
 */
function generateSimpleQRCode($code, $url) {
    try {
        // استخدام خدمة QR Code مجانية أخرى
        $qrUrl = "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=" . urlencode($code);

        $qrImage = @file_get_contents($qrUrl);

        if ($qrImage !== false) {
            $filePath = "qrcodes/{$code}.png";
            file_put_contents($filePath, $qrImage);
            return $filePath;
        }

        return generateTextCode($code, $url);
    } catch (Exception $e) {
        return generateTextCode($code, $url);
    }
}

/**
 * إنشاء ملف نصي بالكود كبديل
 */
function generateTextCode($code, $url) {
    try {
        $content = "كود الدعوة: {$code}\n";
        $content .= "رابط التحقق: {$url}\n";
        $content .= "تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n";

        $filePath = "qrcodes/{$code}.txt";
        file_put_contents($filePath, $content);
        return $filePath;
    } catch (Exception $e) {
        return false;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة ضيف جديد - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>➕ إضافة ضيف جديد</h1>
                <div class="header-actions">
                    <a href="dashboard.php" class="btn btn-secondary">🔙 العودة للوحة التحكم</a>
                    <a href="logout.php" class="btn btn-light">تسجيل الخروج</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <div class="form-container">
            <form method="POST" class="guest-form">
                <div class="form-group">
                    <label for="name">اسم الضيف: <span class="required">*</span></label>
                    <input type="text" id="name" name="name" required 
                           value="<?php echo htmlspecialchars($name ?? ''); ?>"
                           placeholder="أدخل اسم الضيف الكامل">
                </div>
                
                <div class="form-group">
                    <label for="phone">رقم الجوال:</label>
                    <input type="tel" id="phone" name="phone" 
                           value="<?php echo htmlspecialchars($phone ?? ''); ?>"
                           placeholder="مثال: 0501234567">
                </div>
                
                <div class="form-group">
                    <label for="notes">ملاحظات:</label>
                    <textarea id="notes" name="notes" rows="4" 
                              placeholder="أي ملاحظات إضافية عن الضيف..."><?php echo htmlspecialchars($notes ?? ''); ?></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        ✅ إضافة الضيف وتوليد QR
                    </button>
                    <a href="dashboard.php" class="btn btn-secondary">
                        ❌ إلغاء
                    </a>
                </div>
            </form>
        </div>

        <?php if ($success): ?>
            <div class="success-section">
                <h3>🎉 تم إضافة الضيف بنجاح!</h3>
                <div class="qr-preview">
                    <p><strong>كود الدعوة:</strong> <?php echo $code; ?></p>
                    <p><strong>رابط التحقق:</strong></p>
                    <code><?php echo SITE_URL . "/verify.php?code=" . $code; ?></code>
                    
                    <?php
                    $qrFile = "qrcodes/{$code}.png";
                    $qrHtmlFile = "qrcodes/{$code}.html";

                    if (file_exists($qrFile)): ?>
                        <div class="qr-image">
                            <img src="<?php echo $qrFile; ?>" alt="QR Code" style="max-width: 200px;">
                            <br>
                            <a href="<?php echo $qrFile; ?>" download="<?php echo $name; ?>_QR.png"
                               class="btn btn-info">📥 تحميل كود QR</a>
                        </div>
                    <?php elseif (file_exists($qrHtmlFile)): ?>
                        <div class="qr-image">
                            <p>📱 كود QR متوفر كصفحة ويب:</p>
                            <a href="<?php echo $qrHtmlFile; ?>" target="_blank" class="btn btn-info">
                                🔗 عرض كود QR
                            </a>
                            <br><br>
                            <a href="qr_generator.php?code=<?php echo $code; ?>&url=<?php echo urlencode(SITE_URL . "/verify.php?code=" . $code); ?>"
                               target="_blank" class="btn btn-success">
                                📱 عرض كود QR محسن
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="qr-image">
                            <p>⚠️ لم يتم إنشاء كود QR، يمكنك استخدام الرابط مباشرة</p>
                            <a href="qr_generator.php?code=<?php echo $code; ?>&url=<?php echo urlencode(SITE_URL . "/verify.php?code=" . $code); ?>"
                               target="_blank" class="btn btn-primary">
                                📱 إنشاء كود QR الآن
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="next-actions">
                    <a href="add_guest.php" class="btn btn-primary">➕ إضافة ضيف آخر</a>
                    <a href="dashboard.php" class="btn btn-secondary">📋 عرض جميع الضيوف</a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // تنسيق رقم الجوال تلقائياً
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 0 && !value.startsWith('0')) {
                value = '0' + value;
            }
            e.target.value = value;
        });
    </script>
</body>
</html>
