<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
requireSupervisor();

$pdo = getDBConnection();

// جلب جميع الضيوف
try {
    $stmt = $pdo->query("SELECT * FROM guests ORDER BY created_at DESC");
    $guests = $stmt->fetchAll();
} catch (PDOException $e) {
    die("خطأ في جلب البيانات: " . $e->getMessage());
}

// تحديد نوع التصدير
$exportType = $_GET['type'] ?? 'csv';

if ($exportType === 'csv') {
    // تصدير CSV
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="guests_' . date('Y-m-d_H-i-s') . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // إضافة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // عناوين الأعمدة
    fputcsv($output, [
        'الرقم',
        'الاسم',
        'رقم الجوال',
        'الملاحظات',
        'كود الدعوة',
        'حالة الحضور',
        'تاريخ الإضافة',
        'تاريخ الحضور'
    ]);
    
    // البيانات
    foreach ($guests as $index => $guest) {
        fputcsv($output, [
            $index + 1,
            $guest['name'],
            $guest['phone'] ?? '',
            $guest['notes'] ?? '',
            $guest['code'],
            $guest['scanned'] ? 'حضر' : 'لم يحضر',
            date('Y-m-d H:i', strtotime($guest['created_at'])),
            $guest['scanned_at'] ? date('Y-m-d H:i', strtotime($guest['scanned_at'])) : ''
        ]);
    }
    
    fclose($output);
    exit();
    
} elseif ($exportType === 'json') {
    // تصدير JSON
    header('Content-Type: application/json; charset=utf-8');
    header('Content-Disposition: attachment; filename="guests_' . date('Y-m-d_H-i-s') . '.json"');
    
    $exportData = [
        'export_date' => date('Y-m-d H:i:s'),
        'total_guests' => count($guests),
        'scanned_guests' => count(array_filter($guests, function($g) { return $g['scanned']; })),
        'guests' => $guests
    ];
    
    echo json_encode($exportData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit();
    
} else {
    // صفحة اختيار نوع التصدير
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تصدير البيانات - نظام إدارة الدعوات</title>
        <link rel="stylesheet" href="assets/style.css">
    </head>
    <body>
        <div class="container">
            <header class="header">
                <div class="header-content">
                    <h1>📥 تصدير البيانات</h1>
                    <div class="header-actions">
                        <a href="dashboard.php" class="btn btn-secondary">🔙 العودة</a>
                    </div>
                </div>
            </header>

            <div class="form-container">
                <h3>اختر نوع التصدير:</h3>
                <p>إجمالي الضيوف: <strong><?php echo count($guests); ?></strong></p>
                <p>الحاضرين: <strong><?php echo count(array_filter($guests, function($g) { return $g['scanned']; })); ?></strong></p>
                
                <div class="export-options" style="margin-top: 30px;">
                    <a href="?type=csv" class="btn btn-primary" style="margin: 10px;">
                        📊 تصدير Excel/CSV
                    </a>
                    <a href="?type=json" class="btn btn-info" style="margin: 10px;">
                        📄 تصدير JSON
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
