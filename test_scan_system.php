<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
if (!isUserLoggedIn()) {
    header('Location: login.php');
    exit();
}

$pdo = getDBConnection();
$testResults = [];

echo "<h1>🧪 اختبار نظام المسح وقاعدة البيانات</h1>";
echo "<hr>";

// اختبار 1: التحقق من وجود الجداول
echo "<h2>1️⃣ فحص الجداول المطلوبة</h2>";

$requiredTables = ['guests', 'users', 'activity_log'];
foreach ($requiredTables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ جدول $table موجود<br>";
        } else {
            echo "❌ جدول $table غير موجود<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في فحص جدول $table: " . $e->getMessage() . "<br>";
    }
}

// اختبار 2: فحص بنية جدول guests
echo "<h2>2️⃣ فحص بنية جدول guests</h2>";

try {
    $stmt = $pdo->query("DESCRIBE guests");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $requiredColumns = ['id', 'name', 'code', 'scanned', 'scanned_at', 'scanned_by', 'created_by'];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>الحالة</th></tr>";
    
    $existingColumns = array_column($columns, 'Field');
    
    foreach ($requiredColumns as $col) {
        $exists = in_array($col, $existingColumns);
        $status = $exists ? "✅ موجود" : "❌ مفقود";
        echo "<tr><td>$col</td><td>-</td><td>$status</td></tr>";
    }
    
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ خطأ في فحص بنية الجدول: " . $e->getMessage() . "<br>";
}

// اختبار 3: إنشاء دعوة تجريبية
echo "<h2>3️⃣ إنشاء دعوة تجريبية</h2>";

$testCode = 'INV_TEST_' . time() . '_' . rand(1000, 9999);

try {
    $stmt = $pdo->prepare("INSERT INTO guests (name, code, created_by) VALUES (?, ?, ?)");
    $result = $stmt->execute(['ضيف تجريبي', $testCode, $_SESSION['user_id']]);
    
    if ($result) {
        $testGuestId = $pdo->lastInsertId();
        echo "✅ تم إنشاء دعوة تجريبية بنجاح<br>";
        echo "🎫 الكود: <code>$testCode</code><br>";
        echo "🆔 معرف الضيف: $testGuestId<br>";
        
        // اختبار 4: محاولة مسح الدعوة التجريبية
        echo "<h2>4️⃣ اختبار مسح الدعوة التجريبية</h2>";
        
        // محاولة المسح الأولى
        $stmt = $pdo->prepare("UPDATE guests SET scanned = 1, scanned_at = NOW(), scanned_by = ? WHERE code = ?");
        $scanResult = $stmt->execute([$_SESSION['user_id'], $testCode]);
        
        if ($scanResult) {
            echo "✅ تم مسح الدعوة التجريبية بنجاح<br>";
            
            // التحقق من التحديث
            $stmt = $pdo->prepare("SELECT * FROM guests WHERE code = ?");
            $stmt->execute([$testCode]);
            $guest = $stmt->fetch();
            
            if ($guest && $guest['scanned'] == 1) {
                echo "✅ تم تحديث حالة المسح في قاعدة البيانات<br>";
                echo "📅 وقت المسح: " . $guest['scanned_at'] . "<br>";
                echo "👤 مسح بواسطة: " . $guest['scanned_by'] . "<br>";
            } else {
                echo "❌ لم يتم تحديث حالة المسح<br>";
            }
            
            // محاولة المسح الثانية (يجب أن تفشل)
            echo "<h2>5️⃣ اختبار منع المسح المتكرر</h2>";
            
            $stmt = $pdo->prepare("SELECT * FROM guests WHERE code = ?");
            $stmt->execute([$testCode]);
            $guest = $stmt->fetch();
            
            if ($guest['scanned'] == 1) {
                echo "✅ النظام يكتشف أن الكود تم مسحه من قبل<br>";
                echo "⚠️ رسالة التحذير: تم استخدام هذا الكود من قبل<br>";
            } else {
                echo "❌ النظام لا يكتشف المسح السابق<br>";
            }
            
        } else {
            echo "❌ فشل في مسح الدعوة التجريبية<br>";
        }
        
    } else {
        echo "❌ فشل في إنشاء الدعوة التجريبية<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الدعوة التجريبية: " . $e->getMessage() . "<br>";
}

// اختبار 6: فحص سجل الأنشطة
echo "<h2>6️⃣ اختبار سجل الأنشطة</h2>";

try {
    // محاولة تسجيل نشاط تجريبي
    logActivity('test_scan', 'guest', $testGuestId ?? 0, 'اختبار تسجيل النشاط');
    
    // التحقق من التسجيل
    $stmt = $pdo->prepare("SELECT * FROM activity_log WHERE action = 'test_scan' ORDER BY created_at DESC LIMIT 1");
    $stmt->execute();
    $activity = $stmt->fetch();
    
    if ($activity) {
        echo "✅ تم تسجيل النشاط بنجاح<br>";
        echo "📝 النشاط: " . $activity['action'] . "<br>";
        echo "📅 الوقت: " . $activity['created_at'] . "<br>";
        echo "👤 المستخدم: " . $activity['user_id'] . "<br>";
    } else {
        echo "❌ لم يتم تسجيل النشاط<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار سجل الأنشطة: " . $e->getMessage() . "<br>";
}

// اختبار 7: إحصائيات الموظف
echo "<h2>7️⃣ اختبار إحصائيات الموظف</h2>";

try {
    // إجمالي ما مسحه هذا الموظف اليوم
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE scanned_by = ? AND DATE(scanned_at) = CURDATE()");
    $stmt->execute([$_SESSION['user_id']]);
    $todayCount = $stmt->fetchColumn();
    
    // إجمالي ما مسحه هذا الموظف
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE scanned_by = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $totalCount = $stmt->fetchColumn();
    
    echo "✅ إحصائيات الموظف تعمل بشكل صحيح<br>";
    echo "📊 مسح اليوم: $todayCount<br>";
    echo "📊 إجمالي المسح: $totalCount<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في إحصائيات الموظف: " . $e->getMessage() . "<br>";
}

// تنظيف البيانات التجريبية
echo "<h2>8️⃣ تنظيف البيانات التجريبية</h2>";

try {
    if (isset($testCode)) {
        $stmt = $pdo->prepare("DELETE FROM guests WHERE code = ?");
        $stmt->execute([$testCode]);
        echo "✅ تم حذف الدعوة التجريبية<br>";
    }
    
    $stmt = $pdo->prepare("DELETE FROM activity_log WHERE action = 'test_scan'");
    $stmt->execute();
    echo "✅ تم حذف سجل الأنشطة التجريبي<br>";
    
} catch (Exception $e) {
    echo "⚠️ تحذير: لم يتم تنظيف البيانات التجريبية: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h2>🎯 ملخص النتائج</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>✅ إذا كانت جميع الاختبارات ناجحة:</h3>";
echo "<p>النظام يعمل بشكل صحيح ويجب أن يقوم بتحديث قاعدة البيانات عند المسح.</p>";

echo "<h3>❌ إذا فشل أي اختبار:</h3>";
echo "<ul>";
echo "<li>تحقق من إعدادات قاعدة البيانات في config.php</li>";
echo "<li>تأكد من تشغيل خدمة MySQL</li>";
echo "<li>شغل setup_database.php لإعادة إنشاء الجداول</li>";
echo "<li>تحقق من صلاحيات المستخدم في قاعدة البيانات</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='employee_dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📱 لوحة تحكم الموظف</a>";
echo "<a href='setup_database.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔧 إعداد قاعدة البيانات</a>";
echo "<a href='test_system.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 اختبار النظام العام</a>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    direction: rtl;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    border-radius: 5px;
    overflow: hidden;
}

th {
    background: #007bff;
    color: white;
    padding: 10px;
}

td {
    padding: 8px;
    border-bottom: 1px solid #eee;
}

code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    color: #e83e8c;
}

a {
    display: inline-block;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

a:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
</style>
