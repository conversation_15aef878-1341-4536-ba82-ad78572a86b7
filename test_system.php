<?php
/**
 * ملف اختبار النظام
 * يتحقق من جميع المكونات الأساسية
 */

echo "<h1>🧪 اختبار نظام إدارة الدعوات</h1>";
echo "<hr>";

// 1. اختبار ملف الإعدادات
echo "<h2>1️⃣ اختبار ملف الإعدادات</h2>";
if (file_exists('config.php')) {
    echo "✅ ملف config.php موجود<br>";
    require_once 'config.php';
    
    // اختبار الثوابت
    if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER')) {
        echo "✅ إعدادات قاعدة البيانات محددة<br>";
    } else {
        echo "❌ إعدادات قاعدة البيانات غير مكتملة<br>";
    }
    
    // اختبار الاتصال بقاعدة البيانات
    try {
        $pdo = getDBConnection();
        echo "✅ الاتصال بقاعدة البيانات نجح<br>";
        
        // اختبار وجود الجداول
        $tables = ['guests'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->rowCount() > 0) {
                echo "✅ جدول {$table} موجود<br>";
            } else {
                echo "❌ جدول {$table} غير موجود<br>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ ملف config.php غير موجود<br>";
}

echo "<hr>";

// 2. اختبار الملفات الأساسية
echo "<h2>2️⃣ اختبار الملفات الأساسية</h2>";
$requiredFiles = [
    'index.php' => 'الصفحة الرئيسية',
    'login.php' => 'صفحة تسجيل الدخول',
    'dashboard.php' => 'لوحة التحكم',
    'add_guest.php' => 'إضافة ضيف',
    'verify.php' => 'التحقق من الكود',
    'logout.php' => 'تسجيل الخروج',
    'export.php' => 'تصدير البيانات'
];

foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "✅ {$description} ({$file}) موجود<br>";
    } else {
        echo "❌ {$description} ({$file}) غير موجود<br>";
    }
}

echo "<hr>";

// 3. اختبار المجلدات
echo "<h2>3️⃣ اختبار المجلدات</h2>";
$requiredDirs = [
    'qrcodes' => 'مجلد أكواد QR',
    'assets' => 'مجلد الملفات المساعدة'
];

foreach ($requiredDirs as $dir => $description) {
    if (is_dir($dir)) {
        echo "✅ {$description} ({$dir}) موجود<br>";
        
        // اختبار صلاحيات الكتابة
        if (is_writable($dir)) {
            echo "✅ {$description} قابل للكتابة<br>";
        } else {
            echo "⚠️ {$description} غير قابل للكتابة<br>";
        }
    } else {
        echo "❌ {$description} ({$dir}) غير موجود<br>";
    }
}

echo "<hr>";

// 4. اختبار ملفات التصميم
echo "<h2>4️⃣ اختبار ملفات التصميم</h2>";
if (file_exists('assets/style.css')) {
    echo "✅ ملف التصميم موجود<br>";
    $cssSize = filesize('assets/style.css');
    echo "📏 حجم ملف CSS: " . number_format($cssSize) . " بايت<br>";
} else {
    echo "❌ ملف التصميم غير موجود<br>";
}

echo "<hr>";

// 5. اختبار وظائف PHP المطلوبة
echo "<h2>5️⃣ اختبار وظائف PHP</h2>";
$requiredExtensions = [
    'pdo' => 'PDO للاتصال بقاعدة البيانات',
    'pdo_mysql' => 'PDO MySQL Driver',
    'session' => 'إدارة الجلسات',
    'json' => 'معالجة JSON'
];

foreach ($requiredExtensions as $ext => $description) {
    if (extension_loaded($ext)) {
        echo "✅ {$description} ({$ext}) متوفر<br>";
    } else {
        echo "❌ {$description} ({$ext}) غير متوفر<br>";
    }
}

// اختبار وظائف مهمة
$requiredFunctions = [
    'file_get_contents' => 'قراءة الملفات',
    'file_put_contents' => 'كتابة الملفات',
    'uniqid' => 'توليد معرفات فريدة',
    'password_hash' => 'تشفير كلمات المرور'
];

foreach ($requiredFunctions as $func => $description) {
    if (function_exists($func)) {
        echo "✅ {$description} ({$func}) متوفر<br>";
    } else {
        echo "❌ {$description} ({$func}) غير متوفر<br>";
    }
}

echo "<hr>";

// 6. اختبار إعدادات PHP
echo "<h2>6️⃣ اختبار إعدادات PHP</h2>";
echo "📋 إصدار PHP: " . PHP_VERSION . "<br>";
echo "📋 الحد الأقصى لحجم الملف: " . ini_get('upload_max_filesize') . "<br>";
echo "📋 الحد الأقصى لوقت التنفيذ: " . ini_get('max_execution_time') . " ثانية<br>";
echo "📋 الحد الأقصى للذاكرة: " . ini_get('memory_limit') . "<br>";

echo "<hr>";

// 7. اختبار الاتصال بالإنترنت (لـ QR Code)
echo "<h2>7️⃣ اختبار الاتصال بالإنترنت</h2>";
$testUrl = "https://www.google.com";
$context = stream_context_create([
    'http' => [
        'timeout' => 5
    ]
]);

if (@file_get_contents($testUrl, false, $context)) {
    echo "✅ الاتصال بالإنترنت يعمل (مطلوب لتوليد أكواد QR)<br>";
} else {
    echo "⚠️ لا يوجد اتصال بالإنترنت (قد يؤثر على توليد أكواد QR)<br>";
}

echo "<hr>";

// 8. ملخص النتائج
echo "<h2>📊 ملخص النتائج</h2>";
echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🎯 حالة النظام:</h3>";

if (file_exists('config.php') && is_dir('qrcodes') && is_dir('assets') && file_exists('assets/style.css')) {
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>✅ النظام جاهز للاستخدام!</p>";
    echo "<p><strong>الخطوات التالية:</strong></p>";
    echo "<ol>";
    echo "<li>تأكد من إنشاء قاعدة البيانات باستخدام ملف database.sql</li>";
    echo "<li>اذهب إلى <a href='login.php'>صفحة تسجيل الدخول</a></li>";
    echo "<li>استخدم: اسم المستخدم = admin، كلمة المرور = admin123</li>";
    echo "</ol>";
} else {
    echo "<p style='color: red; font-size: 18px; font-weight: bold;'>❌ النظام يحتاج إلى إعداد إضافي</p>";
    echo "<p>يرجى مراجعة الأخطاء أعلاه وإصلاحها</p>";
}

echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>تم إنشاء هذا الاختبار تلقائياً • " . date('Y-m-d H:i:s') . "</p>";
?>
