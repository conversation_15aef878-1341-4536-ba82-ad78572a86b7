<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
if (!isUserLoggedIn()) {
    header('Location: login.php');
    exit();
}

/**
 * استخراج كود الدعوة من النص المدخل
 * يدعم الكود المباشر والروابط
 */
function extractCodeFromInput($input) {
    $input = trim($input);
    
    // إذا كان رابطاً، استخرج الكود من المعامل
    if (strpos($input, 'verify.php?code=') !== false) {
        // استخراج الكود من الرابط
        $parsed = parse_url($input);
        if (isset($parsed['query'])) {
            parse_str($parsed['query'], $params);
            if (isset($params['code'])) {
                return $params['code'];
            }
        }
    }
    
    // إذا كان رابطاً بصيغة أخرى، حاول استخراج الكود
    if (preg_match('/code=([^&\s]+)/', $input, $matches)) {
        return $matches[1];
    }
    
    // إذا كان كود مباشر، أرجعه كما هو
    return $input;
}

echo "<h1>🧪 اختبار استخراج الكود من الروابط</h1>";
echo "<hr>";

// أمثلة للاختبار
$testCases = [
    // أكواد مباشرة
    'INV_685CC13E3FBCE_4786',
    'INV_TEST_123456789_0000',
    
    // روابط كاملة
    'http://localhost/دعوات حظور/verify.php?code=INV_685CC13E3FBCE_4786',
    'https://example.com/verify.php?code=INV_TEST_123456789_0000',
    
    // روابط مع معاملات إضافية
    'http://localhost/verify.php?code=INV_685CC13E3FBCE_4786&ref=email',
    'https://example.com/verify.php?utm_source=qr&code=INV_TEST_123456789_0000&utm_medium=print',
    
    // روابط مقطوعة أو غير مكتملة
    'verify.php?code=INV_685CC13E3FBCE_4786',
    'code=INV_TEST_123456789_0000',
    
    // حالات خاطئة
    'INV_WRONG',
    'http://example.com/verify.php',
    'random text',
    ''
];

echo "<h2>📋 نتائج الاختبار</h2>";

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px; text-align: right;'>النص المدخل</th>";
echo "<th style='padding: 10px; text-align: right;'>الكود المستخرج</th>";
echo "<th style='padding: 10px; text-align: right;'>النتيجة</th>";
echo "</tr>";

foreach ($testCases as $input) {
    $extractedCode = extractCodeFromInput($input);
    
    // تحديد نوع النتيجة
    $isValid = !empty($extractedCode) && strpos($extractedCode, 'INV_') === 0;
    $resultClass = $isValid ? 'success' : 'error';
    $resultIcon = $isValid ? '✅' : '❌';
    
    $inputDisplay = htmlspecialchars($input);
    if (strlen($inputDisplay) > 60) {
        $inputDisplay = substr($inputDisplay, 0, 60) . '...';
    }
    
    echo "<tr>";
    echo "<td style='padding: 8px; font-family: monospace; font-size: 12px;'>" . ($input ? $inputDisplay : '<em>فارغ</em>') . "</td>";
    echo "<td style='padding: 8px; font-family: monospace; font-weight: bold;'>" . htmlspecialchars($extractedCode) . "</td>";
    echo "<td style='padding: 8px; text-align: center;'>";
    
    if ($isValid) {
        echo "<span style='color: #28a745;'>$resultIcon صحيح</span>";
    } else {
        echo "<span style='color: #dc3545;'>$resultIcon خاطئ</span>";
    }
    
    echo "</td>";
    echo "</tr>";
}

echo "</table>";

// اختبار تفاعلي
echo "<h2>🔧 اختبار تفاعلي</h2>";

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['test_input'])) {
    $testInput = $_POST['test_input'];
    $result = extractCodeFromInput($testInput);
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📊 نتيجة الاختبار:</h3>";
    echo "<p><strong>النص المدخل:</strong> <code>" . htmlspecialchars($testInput) . "</code></p>";
    echo "<p><strong>الكود المستخرج:</strong> <code>" . htmlspecialchars($result) . "</code></p>";
    
    if (!empty($result) && strpos($result, 'INV_') === 0) {
        echo "<p style='color: #28a745;'><strong>✅ النتيجة:</strong> كود صحيح</p>";
        
        // اختبار البحث في قاعدة البيانات
        try {
            $pdo = getDBConnection();
            $stmt = $pdo->prepare("SELECT name, scanned FROM guests WHERE code = ?");
            $stmt->execute([$result]);
            $guest = $stmt->fetch();
            
            if ($guest) {
                echo "<p style='color: #17a2b8;'><strong>🎫 الضيف:</strong> " . htmlspecialchars($guest['name']) . "</p>";
                echo "<p><strong>📊 الحالة:</strong> " . ($guest['scanned'] ? '✅ حضر' : '⏳ لم يحضر') . "</p>";
            } else {
                echo "<p style='color: #ffc107;'><strong>⚠️ تحذير:</strong> الكود غير موجود في قاعدة البيانات</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: #dc3545;'><strong>❌ خطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p style='color: #dc3545;'><strong>❌ النتيجة:</strong> كود غير صحيح</p>";
    }
    echo "</div>";
}

echo "<form method='POST' style='background: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🧪 اختبر بنفسك:</h3>";
echo "<p>أدخل كود دعوة أو رابط تحقق:</p>";
echo "<input type='text' name='test_input' style='width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin: 10px 0;' placeholder='مثال: INV_123456789_0000 أو http://localhost/verify.php?code=INV_123456789_0000'>";
echo "<br>";
echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🔍 اختبار</button>";
echo "</form>";

// أمثلة للنسخ
echo "<h2>📝 أمثلة للنسخ والاختبار</h2>";

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>✅ أكواد صحيحة:</h4>";
echo "<ul style='font-family: monospace;'>";
echo "<li>INV_685CC13E3FBCE_4786</li>";
echo "<li>INV_685CC2AC95E90_6360</li>";
echo "<li>INV_TEST_" . time() . "_1234</li>";
echo "</ul>";

echo "<h4>🔗 روابط صحيحة:</h4>";
echo "<ul style='font-family: monospace; font-size: 12px;'>";
echo "<li>http://localhost/دعوات حظور/verify.php?code=INV_685CC13E3FBCE_4786</li>";
echo "<li>verify.php?code=INV_685CC2AC95E90_6360</li>";
echo "<li>code=INV_TEST_" . time() . "_1234</li>";
echo "</ul>";

echo "<h4>❌ أمثلة خاطئة:</h4>";
echo "<ul style='font-family: monospace;'>";
echo "<li>INV_123 (قصير جداً)</li>";
echo "<li>ABC_685CC13E3FBCE_4786 (لا يبدأ بـ INV_)</li>";
echo "<li>http://example.com/verify.php (بدون كود)</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='employee_dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📱 لوحة تحكم الموظف</a>";
echo "<a href='test_scan_system.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔧 اختبار النظام</a>";
echo "<a href='test_camera.php' style='background: #ffc107; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📷 اختبار الكاميرا</a>";
echo "</div>";

echo "<hr>";
echo "<h2>💡 ملاحظات مهمة</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid #bee5eb;'>";
echo "<h4>🎯 كيف يعمل النظام الآن:</h4>";
echo "<ol>";
echo "<li><strong>مسح QR Code:</strong> ينتج رابط كامل مثل <code>http://localhost/verify.php?code=INV_123...</code></li>";
echo "<li><strong>استخراج الكود:</strong> النظام يستخرج <code>INV_123...</code> من الرابط تلقائياً</li>";
echo "<li><strong>البحث في قاعدة البيانات:</strong> يبحث عن الكود المستخرج</li>";
echo "<li><strong>تسجيل الحضور:</strong> يحدث قاعدة البيانات عند العثور على الضيف</li>";
echo "</ol>";

echo "<h4>✅ المميزات الجديدة:</h4>";
echo "<ul>";
echo "<li>دعم مسح QR Code الذي يحتوي على روابط</li>";
echo "<li>دعم الإدخال اليدوي للأكواد</li>";
echo "<li>دعم نسخ ولصق الروابط الكاملة</li>";
echo "<li>استخراج ذكي للكود من أي نوع إدخال</li>";
echo "<li>رسائل واضحة لكل نوع إدخال</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    direction: rtl;
}

h1, h2, h3, h4 {
    color: #333;
}

table {
    background: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

th {
    background: #007bff;
    color: white;
    padding: 10px;
}

td {
    padding: 8px;
    border-bottom: 1px solid #eee;
}

code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    color: #e83e8c;
}

a {
    display: inline-block;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

a:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

ul, ol {
    text-align: right;
}

li {
    margin: 5px 0;
}
</style>
