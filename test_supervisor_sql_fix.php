<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
requireLogin();

$pdo = getDBConnection();

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار إصلاح SQL - لوحة المشرف</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 900px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
        .test-box { background: white; border-radius: 10px; padding: 20px; margin: 15px 0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; color: #155724; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; color: #721c24; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; color: #856404; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 8px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: right; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>";

echo "<h1>🔧 اختبار إصلاح SQL - لوحة المشرف</h1>";

echo "<div class='test-box info'>
        <h3>📋 معلومات الجلسة</h3>
        <p><strong>معرف المستخدم:</strong> {$_SESSION['user_id']}</p>
        <p><strong>اسم المستخدم:</strong> {$_SESSION['full_name']}</p>
        <p><strong>نوع المستخدم:</strong> {$_SESSION['user_type']}</p>
        <p><strong>هل هو مدير:</strong> " . (isAdmin() ? 'نعم' : 'لا') . "</p>
      </div>";

echo "<h2>🧪 اختبار الاستعلامات المصححة</h2>";

// اختبار 1: الإحصائيات
echo "<div class='test-box'>
        <h4>📊 اختبار 1: الإحصائيات</h4>";

try {
    if (isAdmin()) {
        // المدير يرى جميع الإحصائيات
        $totalGuests = $pdo->query("SELECT COUNT(*) FROM guests")->fetchColumn();
        $scannedGuests = $pdo->query("SELECT COUNT(*) FROM guests WHERE scanned = 1")->fetchColumn();
        $myGuests = $pdo->query("SELECT COUNT(*) FROM guests")->fetchColumn();
        
        echo "<div class='success'>
                <h5>✅ إحصائيات المدير</h5>
                <ul>
                    <li><strong>إجمالي الدعوات:</strong> $totalGuests</li>
                    <li><strong>إجمالي الحضور:</strong> $scannedGuests</li>
                    <li><strong>دعواتي:</strong> $myGuests</li>
                </ul>
              </div>";
    } else {
        // المشرف يرى إحصائياته فقط
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE guests.created_by = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $myGuests = $stmt->fetchColumn();
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE guests.created_by = ? AND scanned = 1");
        $stmt->execute([$_SESSION['user_id']]);
        $scannedGuests = $stmt->fetchColumn();
        
        $totalGuests = $myGuests;
        
        echo "<div class='success'>
                <h5>✅ إحصائيات المشرف</h5>
                <ul>
                    <li><strong>دعواتي:</strong> $myGuests</li>
                    <li><strong>حضور دعواتي:</strong> $scannedGuests</li>
                    <li><strong>إجمالي الدعوات:</strong> $totalGuests</li>
                </ul>
              </div>";
    }
    
    echo "<pre>-- استعلامات الإحصائيات المصححة
SELECT COUNT(*) FROM guests WHERE guests.created_by = ?
SELECT COUNT(*) FROM guests WHERE guests.created_by = ? AND scanned = 1</pre>";
    
} catch (PDOException $e) {
    echo "<div class='error'>
            <h5>❌ فشل في الإحصائيات!</h5>
            <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "</div>";

// اختبار 2: جلب قائمة الضيوف
echo "<div class='test-box'>
        <h4>👥 اختبار 2: جلب قائمة الضيوف</h4>";

try {
    // محاكاة منطق supervisor_dashboard.php
    $searchTerm = '';
    
    if (isAdmin()) {
        // المدير يرى جميع الضيوف
        if (!empty($searchTerm)) {
            $whereClause = "WHERE g.name LIKE ? OR g.phone LIKE ?";
            $params = ["%{$searchTerm}%", "%{$searchTerm}%"];
        } else {
            $whereClause = "";
            $params = [];
        }
    } else {
        // المشرف يرى ضيوفه فقط
        if (!empty($searchTerm)) {
            $whereClause = "WHERE g.created_by = ? AND (g.name LIKE ? OR g.phone LIKE ?)";
            $params = [$_SESSION['user_id'], "%{$searchTerm}%", "%{$searchTerm}%"];
        } else {
            $whereClause = "WHERE g.created_by = ?";
            $params = [$_SESSION['user_id']];
        }
    }
    
    $sql = "SELECT g.id, g.name, g.phone, g.notes, g.code, g.scanned, g.scanned_at, 
                   g.created_by, g.created_at, g.updated_at, u.full_name as created_by_name 
            FROM guests g 
            LEFT JOIN users u ON g.created_by = u.id 
            {$whereClause} 
            ORDER BY g.created_at DESC
            LIMIT 10";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $guests = $stmt->fetchAll();
    
    echo "<div class='success'>
            <h5>✅ نجح جلب قائمة الضيوف!</h5>
            <p><strong>عدد النتائج:</strong> " . count($guests) . "</p>";
    
    if (!empty($guests)) {
        echo "<table>
                <tr>
                    <th>الاسم</th>
                    <th>الهاتف</th>
                    <th>الكود</th>
                    <th>الحالة</th>
                    <th>المنشئ</th>
                    <th>تاريخ الإنشاء</th>
                </tr>";
        
        foreach (array_slice($guests, 0, 5) as $guest) {
            $status = $guest['scanned'] ? '✅ حضر' : '⏳ لم يحضر';
            echo "<tr>
                    <td>{$guest['name']}</td>
                    <td>" . ($guest['phone'] ?: '-') . "</td>
                    <td>{$guest['code']}</td>
                    <td>$status</td>
                    <td>" . ($guest['created_by_name'] ?: 'غير محدد') . "</td>
                    <td>" . date('Y-m-d H:i', strtotime($guest['created_at'])) . "</td>
                  </tr>";
        }
        
        echo "</table>";
        
        if (count($guests) > 5) {
            echo "<p><em>عرض أول 5 نتائج من " . count($guests) . " نتيجة</em></p>";
        }
    } else {
        echo "<p>لا توجد دعوات</p>";
    }
    
    echo "</div>";
    
    echo "<pre>-- الاستعلام المصحح
SELECT g.id, g.name, g.phone, g.notes, g.code, g.scanned, g.scanned_at, 
       g.created_by, g.created_at, g.updated_at, u.full_name as created_by_name 
FROM guests g 
LEFT JOIN users u ON g.created_by = u.id 
{$whereClause} 
ORDER BY g.created_at DESC</pre>";
    
} catch (PDOException $e) {
    echo "<div class='error'>
            <h5>❌ فشل في جلب قائمة الضيوف!</h5>
            <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "</div>";

// اختبار 3: البحث في الضيوف
echo "<div class='test-box'>
        <h4>🔍 اختبار 3: البحث في الضيوف</h4>";

try {
    $searchTerm = 'test'; // مصطلح بحث تجريبي
    
    if (isAdmin()) {
        $whereClause = "WHERE g.name LIKE ? OR g.phone LIKE ?";
        $params = ["%{$searchTerm}%", "%{$searchTerm}%"];
    } else {
        $whereClause = "WHERE g.created_by = ? AND (g.name LIKE ? OR g.phone LIKE ?)";
        $params = [$_SESSION['user_id'], "%{$searchTerm}%", "%{$searchTerm}%"];
    }
    
    $sql = "SELECT g.id, g.name, g.phone, g.notes, g.code, g.scanned, g.scanned_at, 
                   g.created_by, g.created_at, g.updated_at, u.full_name as created_by_name 
            FROM guests g 
            LEFT JOIN users u ON g.created_by = u.id 
            {$whereClause} 
            ORDER BY g.created_at DESC
            LIMIT 5";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $searchResults = $stmt->fetchAll();
    
    echo "<div class='success'>
            <h5>✅ نجح البحث!</h5>
            <p><strong>مصطلح البحث:</strong> '$searchTerm'</p>
            <p><strong>عدد النتائج:</strong> " . count($searchResults) . "</p>";
    
    if (!empty($searchResults)) {
        echo "<ul>";
        foreach ($searchResults as $result) {
            echo "<li><strong>{$result['name']}</strong> - {$result['phone']} - من: " . ($result['created_by_name'] ?: 'غير محدد') . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>لا توجد نتائج للبحث</p>";
    }
    
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>
            <h5>❌ فشل في البحث!</h5>
            <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "</div>";

echo "<div class='test-box info'>
        <h3>🎯 النتيجة النهائية</h3>
        <p>إذا ظهرت جميع الاختبارات بعلامة ✅ فإن مشكلة SQL Ambiguity في لوحة المشرف تم حلها بنجاح!</p>
        <p>الآن يمكن للمشرف استخدام لوحة التحكم بدون أخطاء SQL.</p>
        <br>
        <a href='supervisor_dashboard.php' class='btn btn-primary'>👨‍💼 العودة للوحة تحكم المشرف</a>
        <a href='employee_dashboard.php' class='btn btn-success'>📱 لوحة تحكم الموظف</a>
      </div>";

echo "</body></html>";
?>
