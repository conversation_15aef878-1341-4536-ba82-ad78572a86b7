<?php
require_once 'config.php';

$error = '';
$success = '';

// إذا كان المسؤول مسجل دخول بالفعل، إعادة توجيه إلى لوحة التحكم
if (isAdminLoggedIn()) {
    header('Location: dashboard.php');
    exit();
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        // التحقق من بيانات المسؤول
        if ($username === ADMIN_USERNAME && $password === ADMIN_PASSWORD) {
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_username'] = $username;
            $_SESSION['login_time'] = time();
            
            header('Location: dashboard.php');
            exit();
        } else {
            $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <h1>🎫 نظام إدارة الدعوات</h1>
                <p>تسجيل دخول المسؤول</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    ⚠️ <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    ✅ <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" name="username" required 
                           value="<?php echo htmlspecialchars($username ?? ''); ?>"
                           placeholder="أدخل اسم المستخدم">
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="أدخل كلمة المرور">
                </div>
                
                <button type="submit" class="btn btn-primary">
                    🔐 تسجيل الدخول
                </button>
            </form>
            
            <div class="login-info">
                <p><strong>بيانات الدخول الافتراضية:</strong></p>
                <p>اسم المستخدم: <code>admin</code></p>
                <p>كلمة المرور: <code>admin123</code></p>
            </div>
        </div>
    </div>
</body>
</html>
