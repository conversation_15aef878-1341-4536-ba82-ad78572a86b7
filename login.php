<?php
require_once 'config.php';

$error = '';
$success = '';

// إذا كان المستخدم مسجل دخول بالفعل، إعادة توجيه حسب نوعه
if (isUserLoggedIn()) {
    $user_type = $_SESSION['user_type'];
    switch ($user_type) {
        case 'admin':
            header('Location: admin_dashboard.php');
            break;
        case 'supervisor':
            header('Location: supervisor_dashboard.php');
            break;
        case 'employee':
            header('Location: employee_dashboard.php');
            break;
        default:
            header('Location: dashboard.php');
    }
    exit();
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        $result = loginUser($username, $password);

        if ($result['success']) {
            $user_type = $result['user']['user_type'];

            // إعادة توجيه حسب نوع المستخدم
            switch ($user_type) {
                case 'admin':
                    header('Location: admin_dashboard.php');
                    break;
                case 'supervisor':
                    header('Location: supervisor_dashboard.php');
                    break;
                case 'employee':
                    header('Location: employee_dashboard.php');
                    break;
                default:
                    header('Location: dashboard.php');
            }
            exit();
        } else {
            $error = $result['message'];

            // إعادة توجيه لإعداد قاعدة البيانات إذا كان مطلوباً
            if (isset($result['redirect'])) {
                header('Location: ' . $result['redirect']);
                exit();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <h1>🎫 نظام إدارة الدعوات</h1>
                <p>تسجيل الدخول</p>
            </div>

            <!-- أنواع المستخدمين -->
            <div class="user-types">
                <div class="user-type-card">
                    <div class="user-icon">👑</div>
                    <h4>المدير</h4>
                    <p>إدارة كاملة للنظام</p>
                </div>
                <div class="user-type-card">
                    <div class="user-icon">👨‍💼</div>
                    <h4>المشرف</h4>
                    <p>إنشاء وإدارة الدعوات</p>
                </div>
                <div class="user-type-card">
                    <div class="user-icon">📱</div>
                    <h4>الموظف</h4>
                    <p>مسح أكواد الحضور</p>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    ⚠️ <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    ✅ <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" name="username" required 
                           value="<?php echo htmlspecialchars($username ?? ''); ?>"
                           placeholder="أدخل اسم المستخدم">
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="أدخل كلمة المرور">
                </div>
                
                <button type="submit" class="btn btn-primary">
                    🔐 تسجيل الدخول
                </button>
            </form>
            
            <div class="login-info">
                <p><strong>بيانات الدخول الافتراضية:</strong></p>
                <div class="default-accounts">
                    <div class="account-info">
                        <strong>👑 المدير:</strong><br>
                        المستخدم: <code>admin</code><br>
                        كلمة المرور: <code>admin123</code>
                    </div>
                </div>
                <p><small>ملاحظة: سيقوم المدير بإنشاء حسابات المشرفين والموظفين</small></p>

                <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
                    <strong>🔧 إعداد أول مرة؟</strong><br>
                    <small>إذا كانت هذه أول مرة تستخدم النظام، اضغط على الرابط أدناه لإعداد قاعدة البيانات:</small><br>
                    <a href="setup_database.php" style="color: #856404; font-weight: bold;">⚙️ إعداد قاعدة البيانات</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
