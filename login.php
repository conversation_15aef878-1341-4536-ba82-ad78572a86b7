<?php
require_once 'config.php';

$error = '';
$success = '';

// إذا كان المستخدم مسجل دخول بالفعل، إعادة توجيه حسب نوعه
if (isUserLoggedIn()) {
    $user_type = $_SESSION['user_type'];
    switch ($user_type) {
        case 'admin':
            header('Location: admin_dashboard.php');
            break;
        case 'supervisor':
            header('Location: supervisor_dashboard.php');
            break;
        case 'employee':
            header('Location: employee_dashboard.php');
            break;
        default:
            header('Location: dashboard.php');
    }
    exit();
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        $result = loginUser($username, $password);

        if ($result['success']) {
            $user_type = $result['user']['user_type'];

            // إعادة توجيه حسب نوع المستخدم
            switch ($user_type) {
                case 'admin':
                    header('Location: admin_dashboard.php');
                    break;
                case 'supervisor':
                    header('Location: supervisor_dashboard.php');
                    break;
                case 'employee':
                    header('Location: employee_dashboard.php');
                    break;
                default:
                    header('Location: dashboard.php');
            }
            exit();
        } else {
            $error = $result['message'];

            // إعادة توجيه لإعداد قاعدة البيانات إذا كان مطلوباً
            if (isset($result['redirect'])) {
                header('Location: ' . $result['redirect']);
                exit();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .contact-info {
            margin-top: 20px;
        }

        .whatsapp-contact {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 10px rgba(40, 167, 69, 0.1);
            transition: all 0.3s ease;
        }

        .whatsapp-contact:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
        }

        .whatsapp-contact h4 {
            margin: 0 0 10px 0;
            color: #155724;
            font-size: 18px;
        }

        .whatsapp-contact p {
            margin: 5px 0;
            color: #155724;
            font-size: 14px;
        }

        .whatsapp-btn {
            display: inline-block;
            margin-top: 15px;
            padding: 12px 25px;
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(37, 211, 102, 0.3);
        }

        .whatsapp-btn:hover {
            background: linear-gradient(135deg, #128c7e 0%, #25d366 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.4);
            color: white;
            text-decoration: none;
        }

        .whatsapp-btn:active {
            transform: translateY(0);
        }

        @media (max-width: 768px) {
            .whatsapp-contact {
                padding: 15px;
            }

            .whatsapp-btn {
                padding: 10px 20px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <h1>🎫 نظام إدارة الدعوات</h1>
                <p>تسجيل الدخول</p>
            </div>

            <!-- أنواع المستخدمين -->
            <div class="user-types">
                <div class="user-type-card">
                    <div class="user-icon">👑</div>
                    <h4>المدير</h4>
                    <p>إدارة كاملة للنظام</p>
                </div>
                <div class="user-type-card">
                    <div class="user-icon">👨‍💼</div>
                    <h4>المشرف</h4>
                    <p>إنشاء وإدارة الدعوات</p>
                </div>
                <div class="user-type-card">
                    <div class="user-icon">📱</div>
                    <h4>الموظف</h4>
                    <p>مسح أكواد الحضور</p>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    ⚠️ <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    ✅ <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" name="username" required 
                           value="<?php echo htmlspecialchars($username ?? ''); ?>"
                           placeholder="أدخل اسم المستخدم">
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="أدخل كلمة المرور">
                </div>
                
                <button type="submit" class="btn btn-primary">
                    🔐 تسجيل الدخول
                </button>
            </form>
            
            <div class="contact-info">
                <div class="whatsapp-contact">
                    <h4>📞 للتواصل مع مدير النظام</h4>
                    <p>للتواصل مع مدير نظام الدعوات الإلكترونية</p>
                    <p>تواصل معنا على الواتساب</p>
                    <a href="https://wa.me/967772419417"
                       target="_blank"
                       class="whatsapp-btn">
                        📱 +967772419417
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
