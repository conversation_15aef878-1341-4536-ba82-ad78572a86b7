<?php
/**
 * إعداد قاعدة البيانات التلقائي
 * ينشئ الجداول المطلوبة إذا لم تكن موجودة
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'invitation_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

echo "<h1>🔧 إعداد قاعدة البيانات</h1>";
echo "<hr>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>1️⃣ إنشاء قاعدة البيانات</h2>";
    
    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء/التحقق من قاعدة البيانات: " . DB_NAME . "<br>";
    
    // الاتصال بقاعدة البيانات المحددة
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>2️⃣ إنشاء جدول المستخدمين</h2>";
    
    // إنشاء جدول المستخدمين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `user_type` enum('admin','supervisor','employee') NOT NULL DEFAULT 'employee',
            `status` enum('active','inactive') NOT NULL DEFAULT 'active',
            `created_by` int(11) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `last_login` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`),
            UNIQUE KEY `email` (`email`),
            KEY `created_by` (`created_by`),
            KEY `user_type` (`user_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ تم إنشاء جدول users<br>";
    
    // إدراج المدير الافتراضي
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    
    if ($stmt->fetchColumn() == 0) {
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, full_name, email, user_type, status) 
            VALUES ('admin', ?, 'مدير النظام', '<EMAIL>', 'admin', 'active')
        ");
        $stmt->execute([$adminPassword]);
        echo "✅ تم إنشاء المدير الافتراضي (admin/admin123)<br>";
    } else {
        echo "ℹ️ المدير الافتراضي موجود مسبقاً<br>";
    }
    
    echo "<h2>3️⃣ إنشاء/تحديث جدول الضيوف</h2>";
    
    // إنشاء جدول الضيوف
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `guests` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `notes` text DEFAULT NULL,
            `code` varchar(50) NOT NULL,
            `scanned` tinyint(1) NOT NULL DEFAULT 0,
            `scanned_at` timestamp NULL DEFAULT NULL,
            `scanned_by` int(11) DEFAULT NULL,
            `created_by` int(11) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `code` (`code`),
            KEY `created_by` (`created_by`),
            KEY `scanned_by` (`scanned_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ تم إنشاء/تحديث جدول guests<br>";
    
    // التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
    $stmt = $pdo->query("DESCRIBE guests");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('created_by', $columns)) {
        $pdo->exec("ALTER TABLE guests ADD COLUMN created_by int(11) DEFAULT NULL");
        echo "✅ تم إضافة عمود created_by<br>";
    }
    
    if (!in_array('scanned_by', $columns)) {
        $pdo->exec("ALTER TABLE guests ADD COLUMN scanned_by int(11) DEFAULT NULL");
        echo "✅ تم إضافة عمود scanned_by<br>";
    }
    
    if (!in_array('updated_at', $columns)) {
        $pdo->exec("ALTER TABLE guests ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
        echo "✅ تم إضافة عمود updated_at<br>";
    }
    
    echo "<h2>4️⃣ إنشاء جدول سجل العمليات</h2>";
    
    // إنشاء جدول سجل العمليات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `activity_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL,
            `action` varchar(100) NOT NULL,
            `target_type` varchar(50) DEFAULT NULL,
            `target_id` int(11) DEFAULT NULL,
            `details` text DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `action` (`action`),
            KEY `created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ تم إنشاء جدول activity_log<br>";
    
    echo "<h2>5️⃣ إنشاء جدول الصلاحيات</h2>";
    
    // إنشاء جدول الصلاحيات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `permissions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `permission` varchar(50) NOT NULL,
            `granted_by` int(11) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `granted_by` (`granted_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ تم إنشاء جدول permissions<br>";
    
    echo "<h2>6️⃣ إضافة الفهارس والمفاتيح الخارجية</h2>";
    
    // إضافة الفهارس
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_name ON guests (name)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_phone ON guests (phone)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_scanned ON guests (scanned)");
        echo "✅ تم إضافة الفهارس<br>";
    } catch (PDOException $e) {
        echo "ℹ️ الفهارس موجودة مسبقاً<br>";
    }
    
    echo "<h2>7️⃣ إحصائيات النظام</h2>";
    
    $totalUsers = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $totalGuests = $pdo->query("SELECT COUNT(*) FROM guests")->fetchColumn();
    $totalScanned = $pdo->query("SELECT COUNT(*) FROM guests WHERE scanned = 1")->fetchColumn();
    
    echo "👥 إجمالي المستخدمين: <strong>{$totalUsers}</strong><br>";
    echo "🎫 إجمالي الدعوات: <strong>{$totalGuests}</strong><br>";
    echo "✅ إجمالي الحضور: <strong>{$totalScanned}</strong><br>";
    
    echo "<hr>";
    echo "<h2>🎉 تم إعداد قاعدة البيانات بنجاح!</h2>";
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>✅ النظام جاهز للاستخدام!</h3>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<ul>";
    echo "<li>اسم المستخدم: <code>admin</code></li>";
    echo "<li>كلمة المرور: <code>admin123</code></li>";
    echo "</ul>";
    echo "<p><strong>الروابط المهمة:</strong></p>";
    echo "<ul>";
    echo "<li><a href='login.php'>🔐 تسجيل الدخول</a></li>";
    echo "<li><a href='admin_dashboard.php'>👑 لوحة تحكم المدير</a></li>";
    echo "<li><a href='test_system.php'>🧪 اختبار النظام</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في إعداد قاعدة البيانات</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الحلول المقترحة:</strong></p>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل خدمة MySQL في XAMPP</li>";
    echo "<li>تأكد من صحة إعدادات قاعدة البيانات في أعلى هذا الملف</li>";
    echo "<li>تأكد من وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ عام</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
    direction: rtl;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
    padding: 5px 10px;
    background: #e3f2fd;
    border-radius: 3px;
    margin: 2px;
    display: inline-block;
}

a:hover {
    background: #bbdefb;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    color: #e83e8c;
}
</style>
