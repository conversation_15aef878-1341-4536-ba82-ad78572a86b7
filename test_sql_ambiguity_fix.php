<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
requireLogin();

$pdo = getDBConnection();

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار إصلاح SQL Ambiguity</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 900px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
        .test-box { background: white; border-radius: 10px; padding: 20px; margin: 15px 0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; color: #155724; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; color: #721c24; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; color: #856404; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 8px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
    </style>
</head>
<body>";

echo "<h1>🔧 اختبار إصلاح SQL Ambiguity</h1>";

// التأكد من وجود معرف المشرف
if (!isset($_SESSION['created_by'])) {
    try {
        $stmt = $pdo->prepare("SELECT created_by FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
        $_SESSION['created_by'] = $user['created_by'] ?? null;
        
        echo "<div class='test-box success'>
                <h3>✅ تم جلب معرف المشرف من قاعدة البيانات</h3>
                <p><strong>معرف المشرف:</strong> " . ($_SESSION['created_by'] ?: 'غير محدد') . "</p>
              </div>";
    } catch (PDOException $e) {
        echo "<div class='test-box error'>
                <h3>❌ خطأ في جلب معرف المشرف</h3>
                <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
              </div>";
        $_SESSION['created_by'] = null;
    }
}

$employeeCreatedBy = isset($_SESSION['created_by']) ? $_SESSION['created_by'] : null;

echo "<div class='test-box info'>
        <h3>📋 معلومات الجلسة</h3>
        <p><strong>معرف الموظف:</strong> {$_SESSION['user_id']}</p>
        <p><strong>اسم الموظف:</strong> {$_SESSION['full_name']}</p>
        <p><strong>نوع المستخدم:</strong> {$_SESSION['user_type']}</p>
        <p><strong>معرف المشرف:</strong> " . ($employeeCreatedBy ?: 'غير محدد') . "</p>
      </div>";

echo "<h2>🧪 اختبار الاستعلامات المصححة</h2>";

// اختبار 1: آخر عمليات المسح (الاستعلام المشكوك فيه)
echo "<div class='test-box'>
        <h4>🔍 اختبار 1: آخر عمليات المسح (مع JOIN مزدوج)</h4>";

try {
    if ($employeeCreatedBy) {
        $stmt = $pdo->prepare("
            SELECT g.id, g.name, g.phone, g.code, g.scanned, g.scanned_at, g.created_at,
                   u.full_name as scanned_by_name, creator.full_name as creator_name
            FROM guests g 
            LEFT JOIN users u ON g.scanned_by = u.id 
            LEFT JOIN users creator ON g.created_by = creator.id
            WHERE g.scanned_by = ? AND g.created_by = ?
            ORDER BY g.scanned_at DESC 
            LIMIT 5
        ");
        $stmt->execute([$_SESSION['user_id'], $employeeCreatedBy]);
        $recentScans = $stmt->fetchAll();
        
        echo "<div class='success'>
                <h5>✅ نجح الاستعلام!</h5>
                <p><strong>عدد النتائج:</strong> " . count($recentScans) . "</p>";
        
        if (!empty($recentScans)) {
            echo "<strong>النتائج:</strong><ul>";
            foreach ($recentScans as $scan) {
                echo "<li>{$scan['name']} - {$scan['scanned_at']} - من: " . ($scan['creator_name'] ?: 'غير محدد') . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>لا توجد عمليات مسح من هذا المشرف</p>";
        }
        
        echo "</div>";
    } else {
        echo "<div class='warning'>
                <h5>⚠️ لا يوجد مشرف محدد</h5>
                <p>سيتم اختبار الاستعلام العام</p>
              </div>";
        
        $stmt = $pdo->prepare("
            SELECT g.id, g.name, g.phone, g.code, g.scanned, g.scanned_at, g.created_at,
                   u.full_name as scanned_by_name, creator.full_name as creator_name
            FROM guests g 
            LEFT JOIN users u ON g.scanned_by = u.id 
            LEFT JOIN users creator ON g.created_by = creator.id
            WHERE g.scanned_by = ? 
            ORDER BY g.scanned_at DESC 
            LIMIT 5
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $recentScans = $stmt->fetchAll();
        
        echo "<div class='success'>
                <h5>✅ نجح الاستعلام العام!</h5>
                <p><strong>عدد النتائج:</strong> " . count($recentScans) . "</p>
              </div>";
    }
    
    echo "<pre>SELECT g.id, g.name, g.phone, g.code, g.scanned, g.scanned_at, g.created_at,
       u.full_name as scanned_by_name, creator.full_name as creator_name
FROM guests g 
LEFT JOIN users u ON g.scanned_by = u.id 
LEFT JOIN users creator ON g.created_by = creator.id
WHERE g.scanned_by = ? AND g.created_by = ?</pre>";
    
} catch (PDOException $e) {
    echo "<div class='error'>
            <h5>❌ فشل الاستعلام!</h5>
            <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "</div>";

// اختبار 2: الإحصائيات
echo "<div class='test-box'>
        <h4>📊 اختبار 2: الإحصائيات</h4>";

try {
    if ($employeeCreatedBy) {
        // إحصائيات اليوم
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM guests 
            WHERE scanned_by = ? AND DATE(scanned_at) = CURDATE() AND guests.created_by = ?
        ");
        $stmt->execute([$_SESSION['user_id'], $employeeCreatedBy]);
        $todayCount = $stmt->fetchColumn();
        
        // إجمالي المسح
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM guests 
            WHERE scanned_by = ? AND guests.created_by = ?
        ");
        $stmt->execute([$_SESSION['user_id'], $employeeCreatedBy]);
        $totalCount = $stmt->fetchColumn();
        
        // دعوات المشرف
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE guests.created_by = ?");
        $stmt->execute([$employeeCreatedBy]);
        $supervisorGuests = $stmt->fetchColumn();
        
        // حضور دعوات المشرف
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE scanned = 1 AND guests.created_by = ?");
        $stmt->execute([$employeeCreatedBy]);
        $supervisorScanned = $stmt->fetchColumn();
        
        echo "<div class='success'>
                <h5>✅ نجحت جميع استعلامات الإحصائيات!</h5>
                <ul>
                    <li><strong>مسح اليوم:</strong> $todayCount</li>
                    <li><strong>إجمالي المسح:</strong> $totalCount</li>
                    <li><strong>دعوات المشرف:</strong> $supervisorGuests</li>
                    <li><strong>حضور دعوات المشرف:</strong> $supervisorScanned</li>
                </ul>
              </div>";
    } else {
        echo "<div class='warning'>
                <h5>⚠️ لا يوجد مشرف محدد - استخدام الإحصائيات العامة</h5>
              </div>";
    }
} catch (PDOException $e) {
    echo "<div class='error'>
            <h5>❌ فشل في الإحصائيات!</h5>
            <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "</div>";

// اختبار 3: البحث عن الدعوات
echo "<div class='test-box'>
        <h4>🔍 اختبار 3: البحث عن الدعوات</h4>";

try {
    if ($employeeCreatedBy) {
        // البحث عن دعوة من نفس المشرف
        $stmt = $pdo->prepare("
            SELECT g.*, u.full_name as creator_name 
            FROM guests g 
            LEFT JOIN users u ON g.created_by = u.id 
            WHERE g.created_by = ? 
            LIMIT 1
        ");
        $stmt->execute([$employeeCreatedBy]);
        $testGuest = $stmt->fetch();
        
        if ($testGuest) {
            echo "<div class='success'>
                    <h5>✅ وجدت دعوة من نفس المشرف</h5>
                    <p><strong>اسم الضيف:</strong> {$testGuest['name']}</p>
                    <p><strong>كود الدعوة:</strong> {$testGuest['code']}</p>
                    <p><strong>المشرف:</strong> " . ($testGuest['creator_name'] ?: 'غير محدد') . "</p>
                  </div>";
        } else {
            echo "<div class='warning'>
                    <h5>⚠️ لا توجد دعوات من هذا المشرف</h5>
                  </div>";
        }
    }
} catch (PDOException $e) {
    echo "<div class='error'>
            <h5>❌ فشل في البحث!</h5>
            <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "</div>";

echo "<div class='test-box info'>
        <h3>🎯 النتيجة النهائية</h3>
        <p>إذا ظهرت جميع الاختبارات بعلامة ✅ فإن مشكلة SQL Ambiguity تم حلها بنجاح!</p>
        <p>الآن يمكن للموظف استخدام النظام بدون أخطاء SQL.</p>
        <br>
        <a href='employee_dashboard.php' class='btn btn-primary'>📱 العودة للوحة تحكم الموظف</a>
        <a href='test_employee_permissions.php' class='btn btn-primary'>🔐 اختبار الصلاحيات</a>
      </div>";

echo "</body></html>";
?>
