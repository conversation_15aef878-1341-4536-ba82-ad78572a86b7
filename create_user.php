<?php
require_once 'config.php';

// التحقق من صلاحيات المدير
requireAdmin();

$pdo = getDBConnection();
$message = '';

// معالجة إنشاء مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_user'])) {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $full_name = sanitizeInput($_POST['full_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $user_type = sanitizeInput($_POST['user_type'] ?? '');
    
    if (empty($username) || empty($password) || empty($full_name) || empty($user_type)) {
        $message = showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
    } elseif (!validatePassword($password)) {
        $message = showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
    } else {
        $result = createUser($username, $password, $full_name, $email, $phone, $user_type);
        
        if ($result['success']) {
            $message = showAlert("تم إنشاء المستخدم {$full_name} بنجاح", 'success');
            // إعادة تعيين النموذج
            $_POST = array();
        } else {
            $message = showAlert($result['message'], 'error');
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>إنشاء مستخدم جديد</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <!-- شريط الحالة -->
        <div class="status-bar">
            <div class="status-left">
                <span><?php echo date('H:i'); ?></span>
            </div>
            <div class="status-right">
                <div class="signal-bars">
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                </div>
                <span>📶</span>
                <span>🔋</span>
                <span>100%</span>
            </div>
        </div>

        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <div class="header-back">
                    <a href="admin_dashboard.php" class="back-btn">←</a>
                </div>
                <h1>➕ إنشاء مستخدم جديد</h1>
                <div class="header-actions">
                    <a href="admin_dashboard.php" class="header-btn">إلغاء</a>
                </div>
            </div>
        </header>

        <div class="page-content">
            <?php echo $message; ?>

            <!-- نموذج إنشاء المستخدم -->
            <div class="form-container">
                <div class="form-header">
                    <h2 class="form-title">بيانات المستخدم الجديد</h2>
                </div>
                
                <form method="POST" class="form-body">
                    <!-- معلومات تسجيل الدخول -->
                    <div class="form-group">
                        <label class="form-label">اسم المستخدم <span style="color: var(--danger-color);">*</span></label>
                        <input type="text" name="username" class="form-input" 
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                               placeholder="اسم المستخدم للدخول" required>
                        <span class="form-help">سيستخدم هذا الاسم لتسجيل الدخول</span>
                    </div>

                    <div class="form-group">
                        <label class="form-label">كلمة المرور <span style="color: var(--danger-color);">*</span></label>
                        <input type="password" name="password" class="form-input" 
                               placeholder="6 أحرف على الأقل" required minlength="6">
                        <span class="form-help">يجب أن تكون 6 أحرف على الأقل</span>
                    </div>

                    <!-- المعلومات الشخصية -->
                    <div class="form-group">
                        <label class="form-label">الاسم الكامل <span style="color: var(--danger-color);">*</span></label>
                        <input type="text" name="full_name" class="form-input" 
                               value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                               placeholder="الاسم الكامل للمستخدم" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">نوع المستخدم <span style="color: var(--danger-color);">*</span></label>
                        <select name="user_type" class="form-input form-select" required>
                            <option value="">اختر نوع المستخدم</option>
                            <option value="admin" <?php echo ($_POST['user_type'] ?? '') === 'admin' ? 'selected' : ''; ?>>
                                👑 مدير - صلاحيات كاملة
                            </option>
                            <option value="supervisor" <?php echo ($_POST['user_type'] ?? '') === 'supervisor' ? 'selected' : ''; ?>>
                                👨‍💼 مشرف - إدارة الدعوات والموظفين
                            </option>
                            <option value="employee" <?php echo ($_POST['user_type'] ?? '') === 'employee' ? 'selected' : ''; ?>>
                                📱 موظف - مسح الدعوات فقط
                            </option>
                        </select>
                    </div>

                    <!-- معلومات التواصل (اختيارية) -->
                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="email" class="form-input" 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               placeholder="<EMAIL>">
                        <span class="form-help">اختياري - للتواصل والإشعارات</span>
                    </div>

                    <div class="form-group">
                        <label class="form-label">رقم الجوال</label>
                        <input type="tel" name="phone" class="form-input" 
                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                               placeholder="05xxxxxxxx">
                        <span class="form-help">اختياري - للتواصل السريع</span>
                    </div>
                </form>

                <div class="form-actions">
                    <button type="submit" name="create_user" form="userForm" class="btn btn-success btn-xl">
                        ✅ إنشاء المستخدم
                    </button>
                    <a href="admin_dashboard.php" class="btn btn-flat">
                        ❌ إلغاء
                    </a>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ℹ️ معلومات مهمة</h3>
                </div>
                <div class="card-body">
                    <div class="app-list">
                        <div class="list-item">
                            <div class="list-icon info">👑</div>
                            <div class="list-content">
                                <div class="list-title">المدير</div>
                                <div class="list-subtitle">صلاحيات كاملة لإدارة النظام</div>
                            </div>
                        </div>
                        <div class="list-item">
                            <div class="list-icon warning">👨‍💼</div>
                            <div class="list-content">
                                <div class="list-title">المشرف</div>
                                <div class="list-subtitle">إدارة الدعوات وإنشاء حسابات الموظفين</div>
                            </div>
                        </div>
                        <div class="list-item">
                            <div class="list-icon success">📱</div>
                            <div class="list-content">
                                <div class="list-title">الموظف</div>
                                <div class="list-subtitle">مسح QR Code وتسجيل الحضور فقط</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <?php
            try {
                $stmt = $pdo->query("
                    SELECT 
                        user_type,
                        COUNT(*) as count 
                    FROM users 
                    WHERE status = 'active' 
                    GROUP BY user_type
                ");
                $userStats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            } catch (PDOException $e) {
                $userStats = [];
            }
            ?>

            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">👑</div>
                    <div class="stat-number"><?php echo $userStats['admin'] ?? 0; ?></div>
                    <div class="stat-label">مدراء</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">👨‍💼</div>
                    <div class="stat-number"><?php echo $userStats['supervisor'] ?? 0; ?></div>
                    <div class="stat-label">مشرفين</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-icon">📱</div>
                    <div class="stat-number"><?php echo $userStats['employee'] ?? 0; ?></div>
                    <div class="stat-label">موظفين</div>
                </div>
                <div class="stat-card info">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number"><?php echo array_sum($userStats); ?></div>
                    <div class="stat-label">إجمالي</div>
                </div>
            </div>
        </div>

        <!-- شريط التنقل السفلي -->
        <div class="tab-bar">
            <div class="tab-items">
                <a href="admin_dashboard.php" class="tab-item">
                    <span class="tab-icon">🏠</span>
                    <span class="tab-label">الرئيسية</span>
                </a>
                <a href="create_user.php" class="tab-item active">
                    <span class="tab-icon">➕</span>
                    <span class="tab-label">إضافة</span>
                </a>
                <a href="admin_dashboard.php" class="tab-item">
                    <span class="tab-icon">👥</span>
                    <span class="tab-label">المستخدمين</span>
                </a>
                <a href="supervisor_dashboard.php" class="tab-item">
                    <span class="tab-icon">📊</span>
                    <span class="tab-label">التقارير</span>
                </a>
                <a href="logout.php" class="tab-item">
                    <span class="tab-icon">🚪</span>
                    <span class="tab-label">خروج</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        // إضافة form id للنموذج
        document.querySelector('form').id = 'userForm';
        
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('userForm');
            const submitBtn = document.querySelector('button[type="submit"]');
            
            form.addEventListener('submit', function() {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '⏳ جاري الإنشاء...';
            });
            
            // تحقق من قوة كلمة المرور
            const passwordInput = document.querySelector('input[name="password"]');
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                if (password.length >= 6) {
                    this.classList.remove('error');
                    this.classList.add('success');
                } else if (password.length > 0) {
                    this.classList.add('error');
                    this.classList.remove('success');
                } else {
                    this.classList.remove('error', 'success');
                }
            });
        });
    </script>
</body>
</html>
