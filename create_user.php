<?php
require_once 'config.php';

// التحقق من صلاحيات المدير
requireAdmin();

$pdo = getDBConnection();
$message = '';

// معالجة إنشاء مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_user'])) {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $full_name = sanitizeInput($_POST['full_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $user_type = sanitizeInput($_POST['user_type'] ?? '');
    
    if (empty($username) || empty($password) || empty($full_name) || empty($user_type)) {
        $message = showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
    } elseif (!validatePassword($password)) {
        $message = showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
    } else {
        $result = createUser($username, $password, $full_name, $email, $phone, $user_type);
        
        if ($result['success']) {
            $message = showAlert("تم إنشاء المستخدم {$full_name} بنجاح", 'success');
            // إعادة تعيين النموذج
            $_POST = array();
        } else {
            $message = showAlert($result['message'], 'error');
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>إنشاء مستخدم جديد</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        /* شريط التقدم */
        .progress-container {
            margin: var(--spacing-md);
            background: var(--bg-secondary);
            border-radius: var(--radius-large);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-light);
        }

        .progress-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 2;
            background: var(--bg-secondary);
            padding: var(--spacing-sm);
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--text-tertiary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
            transition: all 0.3s ease;
        }

        .progress-step.active .step-number {
            background: var(--primary-color);
            transform: scale(1.1);
        }

        .progress-step.completed .step-number {
            background: var(--success-color);
        }

        .step-label {
            font-size: 12px;
            color: var(--text-secondary);
            text-align: center;
            font-weight: 500;
        }

        .progress-step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }

        .progress-line {
            flex: 1;
            height: 2px;
            background: var(--text-tertiary);
            margin: 0 var(--spacing-sm);
            position: relative;
            top: -15px;
        }

        .progress-line.completed {
            background: var(--success-color);
        }

        /* أقسام النموذج */
        .form-section {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .form-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: var(--radius-large);
            color: white;
        }

        .section-icon {
            font-size: 32px;
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            margin: 0;
            letter-spacing: -0.3px;
        }

        .section-subtitle {
            font-size: 14px;
            margin: var(--spacing-xs) 0 0 0;
            opacity: 0.9;
        }

        /* تحسينات الحقول */
        .form-label {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-primary);
        }

        .label-icon {
            font-size: 16px;
            width: 24px;
            height: 24px;
            background: var(--bg-tertiary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .required {
            color: var(--danger-color);
            font-weight: 700;
        }

        .optional {
            color: var(--text-secondary);
            font-size: 12px;
            background: var(--bg-tertiary);
            padding: 2px 8px;
            border-radius: 12px;
        }

        .form-help {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: var(--spacing-xs);
        }

        .help-icon {
            font-size: 14px;
        }

        /* كلمة المرور */
        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            left: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .password-container .form-input {
            padding-left: 50px;
        }

        .password-strength {
            margin-top: var(--spacing-sm);
        }

        .strength-bar {
            width: 100%;
            height: 4px;
            background: var(--text-tertiary);
            border-radius: 2px;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            width: 0%;
            background: var(--danger-color);
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-text {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: var(--spacing-xs);
            display: block;
        }

        /* اختيار نوع المستخدم */
        .user-type-selector {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .user-type-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-lg);
            border: 2px solid var(--text-tertiary);
            border-radius: var(--radius-medium);
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--bg-secondary);
        }

        .user-type-option:hover {
            border-color: var(--primary-color);
            background: rgba(0,122,255,0.05);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .user-type-option.selected {
            border-color: var(--primary-color);
            background: rgba(0,122,255,0.1);
        }

        .option-icon {
            font-size: 32px;
            width: 60px;
            height: 60px;
            background: var(--bg-tertiary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .option-content {
            flex: 1;
        }

        .option-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .option-desc {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.4;
        }

        .option-radio {
            width: 24px;
            height: 24px;
        }

        .option-radio input[type="radio"] {
            width: 100%;
            height: 100%;
            margin: 0;
        }

        /* أزرار الأقسام */
        .section-actions {
            display: flex;
            gap: var(--spacing-md);
            justify-content: space-between;
            margin-top: var(--spacing-xl);
            padding: var(--spacing-lg);
            border-top: 1px solid var(--text-tertiary);
        }

        .section-actions .btn {
            flex: 1;
            max-width: 200px;
        }

        /* تحسينات للجوال */
        @media (max-width: 768px) {
            .progress-bar {
                flex-direction: column;
                gap: var(--spacing-md);
            }

            .progress-line {
                width: 2px;
                height: 30px;
                margin: 0;
                top: 0;
            }

            .section-header {
                flex-direction: column;
                text-align: center;
                gap: var(--spacing-sm);
            }

            .user-type-selector {
                gap: var(--spacing-sm);
            }

            .user-type-option {
                padding: var(--spacing-md);
            }

            .option-icon {
                width: 50px;
                height: 50px;
                font-size: 24px;
            }

            .section-actions {
                flex-direction: column;
            }

            .section-actions .btn {
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شريط الحالة -->
        <div class="status-bar">
            <div class="status-left">
                <span><?php echo date('H:i'); ?></span>
            </div>
            <div class="status-right">
                <div class="signal-bars">
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                </div>
                <span>📶</span>
                <span>🔋</span>
                <span>100%</span>
            </div>
        </div>

        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <div class="header-back">
                    <a href="admin_dashboard.php" class="back-btn">←</a>
                </div>
                <h1>➕ إنشاء مستخدم جديد</h1>
                <div class="header-actions">
                    <a href="admin_dashboard.php" class="header-btn">إلغاء</a>
                </div>
            </div>
        </header>

        <div class="page-content">
            <?php echo $message; ?>

            <!-- شريط التقدم -->
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-step active">
                        <div class="step-number">1</div>
                        <div class="step-label">بيانات الدخول</div>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-step">
                        <div class="step-number">2</div>
                        <div class="step-label">المعلومات الشخصية</div>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-step">
                        <div class="step-number">3</div>
                        <div class="step-label">التواصل</div>
                    </div>
                </div>
            </div>

            <!-- نموذج إنشاء المستخدم -->
            <div class="form-container">
                <form method="POST" id="userForm">
                    <!-- القسم الأول: بيانات تسجيل الدخول -->
                    <div class="form-section active" id="section-1">
                        <div class="section-header">
                            <div class="section-icon">🔐</div>
                            <div class="section-info">
                                <h3 class="section-title">بيانات تسجيل الدخول</h3>
                                <p class="section-subtitle">المعلومات المطلوبة للدخول للنظام</p>
                            </div>
                        </div>

                        <div class="form-body">
                            <div class="form-group">
                                <label class="form-label">
                                    <span class="label-icon">👤</span>
                                    اسم المستخدم
                                    <span class="required">*</span>
                                </label>
                                <input type="text" name="username" class="form-input"
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                       placeholder="اسم المستخدم للدخول" required>
                                <span class="form-help">
                                    <span class="help-icon">💡</span>
                                    سيستخدم هذا الاسم لتسجيل الدخول
                                </span>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <span class="label-icon">🔒</span>
                                    كلمة المرور
                                    <span class="required">*</span>
                                </label>
                                <div class="password-container">
                                    <input type="password" name="password" class="form-input" id="password"
                                           placeholder="6 أحرف على الأقل" required minlength="6">
                                    <button type="button" class="password-toggle" onclick="togglePassword()">👁️</button>
                                </div>
                                <div class="password-strength">
                                    <div class="strength-bar">
                                        <div class="strength-fill"></div>
                                    </div>
                                    <span class="strength-text">قوة كلمة المرور</span>
                                </div>
                                <span class="form-help">
                                    <span class="help-icon">🛡️</span>
                                    يجب أن تكون 6 أحرف على الأقل
                                </span>
                            </div>
                        </div>

                        <div class="section-actions">
                            <button type="button" class="btn btn-primary btn-xl" onclick="nextSection(2)">
                                التالي ←
                            </button>
                        </div>
                    </div>

                    <!-- القسم الثاني: المعلومات الشخصية -->
                    <div class="form-section" id="section-2">
                        <div class="section-header">
                            <div class="section-icon">👨‍💼</div>
                            <div class="section-info">
                                <h3 class="section-title">المعلومات الشخصية</h3>
                                <p class="section-subtitle">البيانات الأساسية للمستخدم</p>
                            </div>
                        </div>

                        <div class="form-body">
                            <div class="form-group">
                                <label class="form-label">
                                    <span class="label-icon">📝</span>
                                    الاسم الكامل
                                    <span class="required">*</span>
                                </label>
                                <input type="text" name="full_name" class="form-input"
                                       value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                                       placeholder="الاسم الكامل للمستخدم" required>
                                <span class="form-help">
                                    <span class="help-icon">👤</span>
                                    الاسم الذي سيظهر في النظام
                                </span>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <span class="label-icon">🎭</span>
                                    نوع المستخدم
                                    <span class="required">*</span>
                                </label>
                                <div class="user-type-selector">
                                    <div class="user-type-option" data-value="admin">
                                        <div class="option-icon">👑</div>
                                        <div class="option-content">
                                            <div class="option-title">مدير</div>
                                            <div class="option-desc">صلاحيات كاملة لإدارة النظام</div>
                                        </div>
                                        <div class="option-radio">
                                            <input type="radio" name="user_type" value="admin"
                                                   <?php echo ($_POST['user_type'] ?? '') === 'admin' ? 'checked' : ''; ?>>
                                        </div>
                                    </div>

                                    <div class="user-type-option" data-value="supervisor">
                                        <div class="option-icon">👨‍💼</div>
                                        <div class="option-content">
                                            <div class="option-title">مشرف</div>
                                            <div class="option-desc">إدارة الدعوات وإنشاء حسابات الموظفين</div>
                                        </div>
                                        <div class="option-radio">
                                            <input type="radio" name="user_type" value="supervisor"
                                                   <?php echo ($_POST['user_type'] ?? '') === 'supervisor' ? 'checked' : ''; ?>>
                                        </div>
                                    </div>

                                    <div class="user-type-option" data-value="employee">
                                        <div class="option-icon">📱</div>
                                        <div class="option-content">
                                            <div class="option-title">موظف</div>
                                            <div class="option-desc">مسح QR Code وتسجيل الحضور فقط</div>
                                        </div>
                                        <div class="option-radio">
                                            <input type="radio" name="user_type" value="employee"
                                                   <?php echo ($_POST['user_type'] ?? '') === 'employee' ? 'checked' : ''; ?>>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="section-actions">
                            <button type="button" class="btn btn-flat" onclick="prevSection(1)">
                                → السابق
                            </button>
                            <button type="button" class="btn btn-primary btn-xl" onclick="nextSection(3)">
                                التالي ←
                            </button>
                        </div>
                    </div>

                    <!-- القسم الثالث: معلومات التواصل -->
                    <div class="form-section" id="section-3">
                        <div class="section-header">
                            <div class="section-icon">📞</div>
                            <div class="section-info">
                                <h3 class="section-title">معلومات التواصل</h3>
                                <p class="section-subtitle">بيانات اختيارية للتواصل (يمكن تركها فارغة)</p>
                            </div>
                        </div>

                        <div class="form-body">
                            <div class="form-group">
                                <label class="form-label">
                                    <span class="label-icon">📧</span>
                                    البريد الإلكتروني
                                    <span class="optional">اختياري</span>
                                </label>
                                <input type="email" name="email" class="form-input"
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                       placeholder="<EMAIL>">
                                <span class="form-help">
                                    <span class="help-icon">📬</span>
                                    للتواصل وإرسال الإشعارات
                                </span>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <span class="label-icon">📱</span>
                                    رقم الجوال
                                    <span class="optional">اختياري</span>
                                </label>
                                <input type="tel" name="phone" class="form-input"
                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                       placeholder="05xxxxxxxx">
                                <span class="form-help">
                                    <span class="help-icon">💬</span>
                                    للتواصل السريع عبر الواتساب
                                </span>
                            </div>
                        </div>

                        <div class="section-actions">
                            <button type="button" class="btn btn-flat" onclick="prevSection(2)">
                                → السابق
                            </button>
                            <button type="submit" name="create_user" class="btn btn-success btn-xl">
                                ✅ إنشاء المستخدم
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- معلومات إضافية -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ℹ️ معلومات مهمة</h3>
                </div>
                <div class="card-body">
                    <div class="app-list">
                        <div class="list-item">
                            <div class="list-icon info">👑</div>
                            <div class="list-content">
                                <div class="list-title">المدير</div>
                                <div class="list-subtitle">صلاحيات كاملة لإدارة النظام</div>
                            </div>
                        </div>
                        <div class="list-item">
                            <div class="list-icon warning">👨‍💼</div>
                            <div class="list-content">
                                <div class="list-title">المشرف</div>
                                <div class="list-subtitle">إدارة الدعوات وإنشاء حسابات الموظفين</div>
                            </div>
                        </div>
                        <div class="list-item">
                            <div class="list-icon success">📱</div>
                            <div class="list-content">
                                <div class="list-title">الموظف</div>
                                <div class="list-subtitle">مسح QR Code وتسجيل الحضور فقط</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <?php
            try {
                $stmt = $pdo->query("
                    SELECT 
                        user_type,
                        COUNT(*) as count 
                    FROM users 
                    WHERE status = 'active' 
                    GROUP BY user_type
                ");
                $userStats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            } catch (PDOException $e) {
                $userStats = [];
            }
            ?>

            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">👑</div>
                    <div class="stat-number"><?php echo $userStats['admin'] ?? 0; ?></div>
                    <div class="stat-label">مدراء</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">👨‍💼</div>
                    <div class="stat-number"><?php echo $userStats['supervisor'] ?? 0; ?></div>
                    <div class="stat-label">مشرفين</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-icon">📱</div>
                    <div class="stat-number"><?php echo $userStats['employee'] ?? 0; ?></div>
                    <div class="stat-label">موظفين</div>
                </div>
                <div class="stat-card info">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number"><?php echo array_sum($userStats); ?></div>
                    <div class="stat-label">إجمالي</div>
                </div>
            </div>
        </div>

        <!-- شريط التنقل السفلي -->
        <div class="tab-bar">
            <div class="tab-items">
                <a href="admin_dashboard.php" class="tab-item">
                    <span class="tab-icon">🏠</span>
                    <span class="tab-label">الرئيسية</span>
                </a>
                <a href="create_user.php" class="tab-item active">
                    <span class="tab-icon">➕</span>
                    <span class="tab-label">إضافة</span>
                </a>
                <a href="admin_dashboard.php" class="tab-item">
                    <span class="tab-icon">👥</span>
                    <span class="tab-label">المستخدمين</span>
                </a>
                <a href="supervisor_dashboard.php" class="tab-item">
                    <span class="tab-icon">📊</span>
                    <span class="tab-label">التقارير</span>
                </a>
                <a href="logout.php" class="tab-item">
                    <span class="tab-icon">🚪</span>
                    <span class="tab-label">خروج</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        let currentSection = 1;

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            initializeForm();
            setupPasswordStrength();
            setupUserTypeSelector();
            setupFormValidation();
        });

        // تهيئة النموذج
        function initializeForm() {
            showSection(1);
            updateProgressBar();
        }

        // التنقل بين الأقسام
        function nextSection(sectionNumber) {
            if (validateCurrentSection()) {
                currentSection = sectionNumber;
                showSection(currentSection);
                updateProgressBar();
            }
        }

        function prevSection(sectionNumber) {
            currentSection = sectionNumber;
            showSection(currentSection);
            updateProgressBar();
        }

        function showSection(sectionNumber) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.form-section').forEach(section => {
                section.classList.remove('active');
            });

            // إظهار القسم المحدد
            document.getElementById(`section-${sectionNumber}`).classList.add('active');
        }

        function updateProgressBar() {
            document.querySelectorAll('.progress-step').forEach((step, index) => {
                const stepNumber = index + 1;
                step.classList.remove('active', 'completed');

                if (stepNumber < currentSection) {
                    step.classList.add('completed');
                } else if (stepNumber === currentSection) {
                    step.classList.add('active');
                }
            });

            document.querySelectorAll('.progress-line').forEach((line, index) => {
                line.classList.remove('completed');
                if (index + 1 < currentSection) {
                    line.classList.add('completed');
                }
            });
        }

        // التحقق من صحة القسم الحالي
        function validateCurrentSection() {
            const currentSectionElement = document.getElementById(`section-${currentSection}`);
            const requiredInputs = currentSectionElement.querySelectorAll('input[required], select[required]');
            let isValid = true;

            requiredInputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('error');
                    isValid = false;
                } else {
                    input.classList.remove('error');
                }
            });

            if (!isValid) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
            }

            return isValid;
        }

        // إعداد مؤشر قوة كلمة المرور
        function setupPasswordStrength() {
            const passwordInput = document.getElementById('password');
            const strengthFill = document.querySelector('.strength-fill');
            const strengthText = document.querySelector('.strength-text');

            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);

                strengthFill.style.width = strength.percentage + '%';
                strengthFill.style.background = strength.color;
                strengthText.textContent = strength.text;
                strengthText.style.color = strength.color;

                // تحديث حالة الحقل
                this.classList.remove('error', 'success');
                if (password.length >= 6) {
                    this.classList.add('success');
                } else if (password.length > 0) {
                    this.classList.add('error');
                }
            });
        }

        function calculatePasswordStrength(password) {
            let score = 0;
            let feedback = '';

            if (password.length >= 6) score += 25;
            if (password.length >= 8) score += 25;
            if (/[A-Z]/.test(password)) score += 25;
            if (/[0-9]/.test(password)) score += 25;

            if (score <= 25) {
                return { percentage: 25, color: '#FF3B30', text: 'ضعيفة' };
            } else if (score <= 50) {
                return { percentage: 50, color: '#FF9500', text: 'متوسطة' };
            } else if (score <= 75) {
                return { percentage: 75, color: '#FFCC00', text: 'جيدة' };
            } else {
                return { percentage: 100, color: '#34C759', text: 'قوية' };
            }
        }

        // إعداد اختيار نوع المستخدم
        function setupUserTypeSelector() {
            const options = document.querySelectorAll('.user-type-option');
            const radioInputs = document.querySelectorAll('input[name="user_type"]');

            options.forEach(option => {
                option.addEventListener('click', function() {
                    const value = this.dataset.value;
                    const radio = this.querySelector('input[type="radio"]');

                    // إزالة التحديد من جميع الخيارات
                    options.forEach(opt => opt.classList.remove('selected'));

                    // تحديد الخيار الحالي
                    this.classList.add('selected');
                    radio.checked = true;

                    // تحديث أيقونة القسم
                    updateSectionIcon(value);
                });
            });

            // تحديد الخيار المحدد مسبقاً
            radioInputs.forEach(radio => {
                if (radio.checked) {
                    radio.closest('.user-type-option').classList.add('selected');
                }
            });
        }

        function updateSectionIcon(userType) {
            const sectionIcon = document.querySelector('#section-2 .section-icon');
            const icons = {
                'admin': '👑',
                'supervisor': '👨‍💼',
                'employee': '📱'
            };
            sectionIcon.textContent = icons[userType] || '👨‍💼';
        }

        // إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.password-toggle');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }

        // إعداد التحقق من النموذج
        function setupFormValidation() {
            const form = document.getElementById('userForm');
            const submitBtn = document.querySelector('button[type="submit"]');

            form.addEventListener('submit', function(e) {
                if (!validateAllSections()) {
                    e.preventDefault();
                    return false;
                }

                submitBtn.disabled = true;
                submitBtn.innerHTML = '⏳ جاري الإنشاء...';

                // إضافة تأثير بصري
                submitBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    submitBtn.style.transform = 'scale(1)';
                }, 150);
            });
        }

        function validateAllSections() {
            let isValid = true;

            // التحقق من القسم الأول
            const username = document.querySelector('input[name="username"]').value.trim();
            const password = document.querySelector('input[name="password"]').value;

            if (!username || !password || password.length < 6) {
                showAlert('يرجى التحقق من بيانات تسجيل الدخول', 'error');
                nextSection(1);
                isValid = false;
            }

            // التحقق من القسم الثاني
            const fullName = document.querySelector('input[name="full_name"]').value.trim();
            const userType = document.querySelector('input[name="user_type"]:checked');

            if (!fullName || !userType) {
                showAlert('يرجى التحقق من المعلومات الشخصية', 'error');
                nextSection(2);
                isValid = false;
            }

            return isValid;
        }

        // عرض التنبيهات
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = `
                <span class="alert-icon">${type === 'error' ? '❌' : '✅'}</span>
                ${message}
            `;

            const pageContent = document.querySelector('.page-content');
            pageContent.insertBefore(alertDiv, pageContent.firstChild);

            // إزالة التنبيه بعد 5 ثوان
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // تحسينات إضافية للتفاعل
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                const activeSection = document.querySelector('.form-section.active');
                const nextBtn = activeSection.querySelector('button[onclick*="nextSection"]');
                const submitBtn = activeSection.querySelector('button[type="submit"]');

                if (nextBtn) {
                    e.preventDefault();
                    nextBtn.click();
                } else if (submitBtn) {
                    // السماح بإرسال النموذج
                }
            }
        });
    </script>
</body>
</html>
