<?php
require_once 'config.php';

// التحقق من صلاحيات المدير
requireAdmin();

$pdo = getDBConnection();
$message = '';

// معالجة إنشاء مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_user'])) {
    // تنظيف البيانات المدخلة
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $full_name = sanitizeInput($_POST['full_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $user_type = sanitizeInput($_POST['user_type'] ?? '');

    // تسجيل البيانات المرسلة للتشخيص
    error_log("محاولة إنشاء مستخدم: " . json_encode([
        'username' => $username,
        'full_name' => $full_name,
        'email' => $email,
        'phone' => $phone,
        'user_type' => $user_type,
        'password_length' => strlen($password)
    ]));

    // التحقق من صحة البيانات
    if (empty($username)) {
        $message = showAlert('اسم المستخدم مطلوب', 'error');
    } elseif (empty($password)) {
        $message = showAlert('كلمة المرور مطلوبة', 'error');
    } elseif (empty($full_name)) {
        $message = showAlert('الاسم الكامل مطلوب', 'error');
    } elseif (empty($user_type)) {
        $message = showAlert('نوع المستخدم مطلوب', 'error');
    } elseif (!validatePassword($password)) {
        $message = showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
    } elseif (!in_array($user_type, ['admin', 'supervisor', 'employee'])) {
        $message = showAlert('نوع المستخدم غير صحيح', 'error');
    } else {
        // محاولة إنشاء المستخدم
        $result = createUser($username, $password, $full_name, $email, $phone, $user_type);

        if ($result['success']) {
            $message = showAlert("تم إنشاء المستخدم {$full_name} بنجاح! معرف المستخدم: {$result['user_id']}", 'success');
            // إعادة تعيين النموذج
            $_POST = array();

            // تسجيل النجاح
            error_log("تم إنشاء المستخدم بنجاح: {$username} (ID: {$result['user_id']})");
        } else {
            $message = showAlert($result['message'], 'error');
            // تسجيل الخطأ
            error_log("فشل في إنشاء المستخدم: " . $result['message']);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>إنشاء مستخدم جديد</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        /* تحسينات النموذج */
        .form-container {
            margin: var(--spacing-md);
            background: var(--bg-secondary);
            border-radius: var(--radius-large);
            box-shadow: var(--shadow-light);
            overflow: hidden;
        }

        .form-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: var(--spacing-xl);
            text-align: center;
        }

        .form-icon {
            font-size: 48px;
            margin-bottom: var(--spacing-md);
            display: block;
        }

        .form-title {
            font-size: 24px;
            font-weight: 700;
            margin: 0 0 var(--spacing-sm) 0;
            letter-spacing: -0.5px;
        }

        .form-subtitle {
            font-size: 16px;
            margin: 0;
            opacity: 0.9;
        }

        .form-body {
            padding: var(--spacing-xl);
        }

        /* أقسام النموذج */
        .form-section {
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-xl);
            border-bottom: 1px solid var(--text-tertiary);
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-md) 0;
        }

        .section-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            background: var(--bg-tertiary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .section-title h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            flex: 1;
        }

        .optional-badge {
            background: var(--warning-color);
            color: white;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        /* حقول الإدخال */
        .form-row {
            margin-bottom: var(--spacing-lg);
        }

        .form-group {
            width: 100%;
        }

        .form-label {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .required {
            color: var(--danger-color);
            font-weight: 700;
        }

        .input-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-icon {
            position: absolute;
            right: var(--spacing-md);
            font-size: 18px;
            color: var(--text-secondary);
            z-index: 2;
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 50px;
            border: 2px solid var(--text-tertiary);
            border-radius: var(--radius-medium);
            font-size: 16px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0,122,255,0.1);
        }

        .form-input.success {
            border-color: var(--success-color);
            background: rgba(52,199,89,0.05);
        }

        .form-input.error {
            border-color: var(--danger-color);
            background: rgba(255,59,48,0.05);
        }

        .form-help {
            font-size: 14px;
            color: var(--text-secondary);
            margin-top: var(--spacing-sm);
            display: block;
        }

        /* كلمة المرور */
        .password-toggle {
            position: absolute;
            left: var(--spacing-md);
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: var(--text-secondary);
            z-index: 3;
            padding: var(--spacing-sm);
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .password-toggle:hover {
            background: var(--bg-tertiary);
        }

        .password-strength {
            margin-top: var(--spacing-md);
        }

        .strength-bar {
            width: 100%;
            height: 6px;
            background: var(--text-tertiary);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: var(--spacing-sm);
        }

        .strength-fill {
            height: 100%;
            width: 0%;
            background: var(--danger-color);
            transition: all 0.3s ease;
            border-radius: 3px;
        }

        .strength-text {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* اختيار نوع المستخدم */
        .user-type-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .user-type-card {
            display: block;
            cursor: pointer;
            position: relative;
        }

        .user-type-card input[type="radio"] {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }

        .card-content {
            background: var(--bg-tertiary);
            border: 2px solid var(--text-tertiary);
            border-radius: var(--radius-medium);
            padding: var(--spacing-lg);
            text-align: center;
            transition: all 0.3s ease;
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .user-type-card:hover .card-content {
            border-color: var(--primary-color);
            background: rgba(0,122,255,0.05);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .user-type-card input:checked + .card-content {
            border-color: var(--primary-color);
            background: rgba(0,122,255,0.1);
            box-shadow: var(--shadow-medium);
        }

        .card-icon {
            font-size: 32px;
            margin-bottom: var(--spacing-sm);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .card-desc {
            font-size: 12px;
            color: var(--text-secondary);
            line-height: 1.3;
        }

        /* أزرار الإجراءات */
        .form-actions {
            padding: var(--spacing-xl);
            border-top: 1px solid var(--text-tertiary);
            background: var(--bg-tertiary);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        /* تحسينات للجوال */
        @media (max-width: 768px) {
            .form-container {
                margin: var(--spacing-sm);
            }

            .form-header,
            .form-body,
            .form-actions {
                padding: var(--spacing-lg);
            }

            .form-title {
                font-size: 20px;
            }

            .form-subtitle {
                font-size: 14px;
            }

            .user-type-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-sm);
            }

            .card-content {
                height: auto;
                padding: var(--spacing-md);
            }

            .section-title {
                flex-direction: column;
                text-align: center;
                gap: var(--spacing-sm);
            }

            .section-title h3 {
                text-align: center;
            }
        }

        /* تحسينات إضافية */
        .form-input::placeholder {
            color: var(--text-secondary);
            opacity: 0.7;
        }

        .form-section:hover .section-icon {
            background: var(--primary-color);
            color: white;
            transform: scale(1.1);
        }

        /* تأثيرات التحميل */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شريط الحالة -->
        <div class="status-bar">
            <div class="status-left">
                <span><?php echo date('H:i'); ?></span>
            </div>
            <div class="status-right">
                <div class="signal-bars">
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                </div>
                <span>📶</span>
                <span>🔋</span>
                <span>100%</span>
            </div>
        </div>

        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <div class="header-back">
                    <a href="admin_dashboard.php" class="back-btn">←</a>
                </div>
                <h1>➕ إنشاء مستخدم جديد</h1>
                <div class="header-actions">
                    <a href="admin_dashboard.php" class="header-btn">إلغاء</a>
                </div>
            </div>
        </header>

        <div class="page-content">
            <?php echo $message; ?>

            <!-- نموذج إنشاء المستخدم -->
            <div class="form-container">
                <div class="form-header">
                    <div class="form-icon">👤</div>
                    <h2 class="form-title">إنشاء مستخدم جديد</h2>
                    <p class="form-subtitle">أدخل بيانات المستخدم الجديد</p>
                </div>

                <form method="POST" id="userForm" class="form-body">
                    <!-- بيانات تسجيل الدخول -->
                    <div class="form-section">
                        <div class="section-title">
                            <span class="section-icon">🔐</span>
                            <h3>بيانات تسجيل الدخول</h3>
                        </div>

                        <div class="form-group">
                            <label class="form-label">اسم المستخدم <span class="required">*</span></label>
                            <input type="text" name="username" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                   placeholder="اسم المستخدم للدخول" required>
                            <span class="form-help">سيستخدم هذا الاسم لتسجيل الدخول</span>
                        </div>

                        <div class="form-group">
                            <label class="form-label">كلمة المرور <span class="required">*</span></label>
                            <input type="password" name="password" class="form-input" id="password"
                                   placeholder="6 أحرف على الأقل" required minlength="6">
                            <div class="password-strength">
                                <div class="strength-bar">
                                    <div class="strength-fill" id="strength-fill"></div>
                                </div>
                                <span class="strength-text" id="strength-text">أدخل كلمة المرور</span>
                            </div>
                            <span class="form-help">يجب أن تكون 6 أحرف على الأقل</span>
                        </div>
                    </div>

                    <!-- المعلومات الشخصية -->
                    <div class="form-section">
                        <div class="section-title">
                            <span class="section-icon">👨‍💼</span>
                            <h3>المعلومات الشخصية</h3>
                        </div>

                        <div class="form-group">
                            <label class="form-label">الاسم الكامل <span class="required">*</span></label>
                            <input type="text" name="full_name" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                                   placeholder="الاسم الكامل للمستخدم" required>
                            <span class="form-help">الاسم الذي سيظهر في النظام</span>
                        </div>

                        <div class="form-group">
                            <label class="form-label">نوع المستخدم <span class="required">*</span></label>
                            <select name="user_type" class="form-input" required>
                                <option value="">اختر نوع المستخدم</option>
                                <option value="admin" <?php echo ($_POST['user_type'] ?? '') === 'admin' ? 'selected' : ''; ?>>
                                    👑 مدير - صلاحيات كاملة
                                </option>
                                <option value="supervisor" <?php echo ($_POST['user_type'] ?? '') === 'supervisor' ? 'selected' : ''; ?>>
                                    👨‍💼 مشرف - إدارة الدعوات والموظفين
                                </option>
                                <option value="employee" <?php echo ($_POST['user_type'] ?? '') === 'employee' ? 'selected' : ''; ?>>
                                    📱 موظف - مسح الدعوات فقط
                                </option>
                            </select>
                        </div>
                    </div>

                    <!-- معلومات التواصل -->
                    <div class="form-section">
                        <div class="section-title">
                            <span class="section-icon">📞</span>
                            <h3>معلومات التواصل <span class="optional-badge">اختياري</span></h3>
                        </div>

                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" name="email" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                   placeholder="<EMAIL>">
                            <span class="form-help">للتواصل وإرسال الإشعارات</span>
                        </div>

                        <div class="form-group">
                            <label class="form-label">رقم الجوال</label>
                            <input type="tel" name="phone" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                   placeholder="05xxxxxxxx">
                            <span class="form-help">للتواصل السريع عبر الواتساب</span>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="form-actions">
                        <button type="submit" name="create_user" class="btn btn-success btn-xl" id="submitBtn">
                            ✅ إنشاء المستخدم
                        </button>
                        <a href="admin_dashboard.php" class="btn btn-flat">
                            ❌ إلغاء
                        </a>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="form-actions">
                        <button type="submit" name="create_user" class="btn btn-success btn-xl" id="submitBtn">
                            ✅ إنشاء المستخدم
                        </button>
                        <a href="admin_dashboard.php" class="btn btn-flat">
                            ❌ إلغاء
                        </a>
                    </div>
                </form>
            </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ℹ️ معلومات مهمة</h3>
                </div>
                <div class="card-body">
                    <div class="app-list">
                        <div class="list-item">
                            <div class="list-icon info">👑</div>
                            <div class="list-content">
                                <div class="list-title">المدير</div>
                                <div class="list-subtitle">صلاحيات كاملة لإدارة النظام</div>
                            </div>
                        </div>
                        <div class="list-item">
                            <div class="list-icon warning">👨‍💼</div>
                            <div class="list-content">
                                <div class="list-title">المشرف</div>
                                <div class="list-subtitle">إدارة الدعوات وإنشاء حسابات الموظفين</div>
                            </div>
                        </div>
                        <div class="list-item">
                            <div class="list-icon success">📱</div>
                            <div class="list-content">
                                <div class="list-title">الموظف</div>
                                <div class="list-subtitle">مسح QR Code وتسجيل الحضور فقط</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <?php
            try {
                $stmt = $pdo->query("
                    SELECT 
                        user_type,
                        COUNT(*) as count 
                    FROM users 
                    WHERE status = 'active' 
                    GROUP BY user_type
                ");
                $userStats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            } catch (PDOException $e) {
                $userStats = [];
            }
            ?>

            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">👑</div>
                    <div class="stat-number"><?php echo $userStats['admin'] ?? 0; ?></div>
                    <div class="stat-label">مدراء</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">👨‍💼</div>
                    <div class="stat-number"><?php echo $userStats['supervisor'] ?? 0; ?></div>
                    <div class="stat-label">مشرفين</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-icon">📱</div>
                    <div class="stat-number"><?php echo $userStats['employee'] ?? 0; ?></div>
                    <div class="stat-label">موظفين</div>
                </div>
                <div class="stat-card info">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number"><?php echo array_sum($userStats); ?></div>
                    <div class="stat-label">إجمالي</div>
                </div>
            </div>
        </div>

        <!-- شريط التنقل السفلي -->
        <div class="tab-bar">
            <div class="tab-items">
                <a href="admin_dashboard.php" class="tab-item">
                    <span class="tab-icon">🏠</span>
                    <span class="tab-label">الرئيسية</span>
                </a>
                <a href="create_user.php" class="tab-item active">
                    <span class="tab-icon">➕</span>
                    <span class="tab-label">إضافة</span>
                </a>
                <a href="admin_dashboard.php" class="tab-item">
                    <span class="tab-icon">👥</span>
                    <span class="tab-label">المستخدمين</span>
                </a>
                <a href="supervisor_dashboard.php" class="tab-item">
                    <span class="tab-icon">📊</span>
                    <span class="tab-label">التقارير</span>
                </a>
                <a href="logout.php" class="tab-item">
                    <span class="tab-icon">🚪</span>
                    <span class="tab-label">خروج</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة');
            setupPasswordStrength();
            setupFormValidation();
        });

        // إعداد مؤشر قوة كلمة المرور
        function setupPasswordStrength() {
            const passwordInput = document.getElementById('password');
            const strengthFill = document.getElementById('strength-fill');
            const strengthText = document.getElementById('strength-text');

            if (passwordInput && strengthFill && strengthText) {
                passwordInput.addEventListener('input', function() {
                    const password = this.value;
                    const strength = calculatePasswordStrength(password);

                    strengthFill.style.width = strength.percentage + '%';
                    strengthFill.style.background = strength.color;
                    strengthText.textContent = strength.text;
                    strengthText.style.color = strength.color;

                    // تحديث حالة الحقل
                    this.classList.remove('error', 'success');
                    if (password.length >= 6) {
                        this.classList.add('success');
                    } else if (password.length > 0) {
                        this.classList.add('error');
                    }
                });
            }
        }

        function calculatePasswordStrength(password) {
            if (password.length === 0) {
                return { percentage: 0, color: '#C7C7CC', text: 'أدخل كلمة المرور' };
            }

            let score = 0;

            if (password.length >= 6) score += 25;
            if (password.length >= 8) score += 25;
            if (/[A-Z]/.test(password)) score += 25;
            if (/[0-9]/.test(password)) score += 25;

            if (score <= 25) {
                return { percentage: 25, color: '#FF3B30', text: 'ضعيفة جداً' };
            } else if (score <= 50) {
                return { percentage: 50, color: '#FF9500', text: 'ضعيفة' };
            } else if (score <= 75) {
                return { percentage: 75, color: '#FFCC00', text: 'متوسطة' };
            } else {
                return { percentage: 100, color: '#34C759', text: 'قوية' };
            }
        }

        // إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('password');

            if (passwordInput) {
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                } else {
                    passwordInput.type = 'password';
                }
            }
        }

        // إعداد التحقق من النموذج
        function setupFormValidation() {
            const form = document.getElementById('userForm');
            const submitBtn = document.getElementById('submitBtn');

            console.log('النموذج:', form);
            console.log('زر الإرسال:', submitBtn);

            if (form && submitBtn) {
                form.addEventListener('submit', function(e) {
                    console.log('تم إرسال النموذج');

                    // تأثير التحميل
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '⏳ جاري الإنشاء...';

                    console.log('سيتم إرسال النموذج الآن');
                });
            } else {
                console.error('لم يتم العثور على النموذج أو زر الإرسال');
            }
        }


    </script>
</body>
</html>
