<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الكاميرا وماسح QR</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            margin: 5px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        #cameraContainer {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px dashed #007bff;
            text-align: center;
        }
        
        #scanner {
            position: relative;
            max-width: 400px;
            margin: 0 auto;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
            font-weight: 500;
        }
        
        .status.info { background: #e3f2fd; color: #1976d2; }
        .status.success { background: #e8f5e8; color: #2e7d32; }
        .status.error { background: #ffebee; color: #c62828; }
        .status.warning { background: #fff3e0; color: #f57c00; }
        
        .result-box {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .camera-info {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .camera-item {
            padding: 8px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار الكاميرا وماسح QR</h1>
        <p>هذه الصفحة لاختبار عمل الكاميرا وماسح أكواد QR في المتصفح</p>
        
        <!-- معلومات المتصفح -->
        <div class="camera-info">
            <h3>📋 معلومات المتصفح:</h3>
            <div id="browserInfo">جاري التحقق...</div>
        </div>
        
        <!-- قائمة الكاميرات -->
        <div class="camera-info">
            <h3>📷 الكاميرات المتاحة:</h3>
            <div id="camerasList">جاري البحث عن الكاميرات...</div>
        </div>
        
        <!-- أدوات التحكم -->
        <div style="text-align: center; margin: 20px 0;">
            <button id="startCamera" class="btn btn-success">📷 تشغيل الكاميرا</button>
            <button id="stopCamera" class="btn btn-danger" style="display: none;">⏹️ إيقاف الكاميرا</button>
            <button id="switchCamera" class="btn btn-info" style="display: none;">🔄 تبديل الكاميرا</button>
            <button id="testQR" class="btn btn-primary">🧪 اختبار QR</button>
        </div>
        
        <!-- حالة النظام -->
        <div id="status" class="status info">📱 جاهز للاختبار</div>
        
        <!-- منطقة الكاميرا -->
        <div id="cameraContainer" style="display: none;">
            <div id="scanner"></div>
        </div>
        
        <!-- نتائج المسح -->
        <div id="results" style="display: none;">
            <h3>📊 نتائج المسح:</h3>
            <div id="resultsList"></div>
        </div>
    </div>
    
    <!-- روابط العودة -->
    <div class="test-container">
        <h3>🔗 روابط مفيدة:</h3>
        <a href="employee_dashboard.php" class="btn btn-primary">📱 لوحة تحكم الموظف</a>
        <a href="login.php" class="btn btn-info">🔐 تسجيل الدخول</a>
        <a href="test_system.php" class="btn btn-success">🧪 اختبار النظام</a>
    </div>

    <!-- مكتبات JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
    <script src="assets/qr-scanner.js"></script>
    
    <script>
        let qrScanner = null;
        let cameras = [];
        let scanResults = [];
        
        // تحديث حالة النظام
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = 'status ' + type;
        }
        
        // عرض معلومات المتصفح
        function showBrowserInfo() {
            const info = document.getElementById('browserInfo');
            const userAgent = navigator.userAgent;
            const hasCamera = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
            const hasWebRTC = !!(window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection);
            
            info.innerHTML = `
                <div><strong>المتصفح:</strong> ${navigator.appName} ${navigator.appVersion}</div>
                <div><strong>دعم الكاميرا:</strong> ${hasCamera ? '✅ مدعوم' : '❌ غير مدعوم'}</div>
                <div><strong>دعم WebRTC:</strong> ${hasWebRTC ? '✅ مدعوم' : '❌ غير مدعوم'}</div>
                <div><strong>HTTPS:</strong> ${location.protocol === 'https:' ? '✅ آمن' : '⚠️ غير آمن (قد يمنع الكاميرا)'}</div>
            `;
        }
        
        // الحصول على قائمة الكاميرات
        async function getCameras() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                cameras = devices.filter(device => device.kind === 'videoinput');
                
                const camerasList = document.getElementById('camerasList');
                
                if (cameras.length === 0) {
                    camerasList.innerHTML = '<div class="camera-item">❌ لم يتم العثور على كاميرات</div>';
                } else {
                    let html = '';
                    cameras.forEach((camera, index) => {
                        const label = camera.label || `كاميرا ${index + 1}`;
                        html += `<div class="camera-item">📷 ${label}</div>`;
                    });
                    camerasList.innerHTML = html;
                    
                    if (cameras.length > 1) {
                        document.getElementById('switchCamera').style.display = 'inline-block';
                    }
                }
                
                return cameras;
            } catch (error) {
                console.error('خطأ في الحصول على الكاميرات:', error);
                document.getElementById('camerasList').innerHTML = 
                    '<div class="camera-item">❌ خطأ في الوصول للكاميرات: ' + error.message + '</div>';
                return [];
            }
        }
        
        // تشغيل الكاميرا
        async function startCamera() {
            try {
                updateStatus('🔄 جاري تشغيل الكاميرا...', 'warning');
                
                // إنشاء ماسح جديد
                qrScanner = new QRScanner({
                    onScanSuccess: function(code) {
                        console.log('تم مسح الكود:', code);
                        addScanResult(code, 'success');
                        updateStatus('✅ تم مسح الكود: ' + code, 'success');
                    },
                    onScanError: function(error) {
                        console.error('خطأ في المسح:', error);
                        updateStatus('❌ ' + error, 'error');
                    }
                });
                
                // بدء المسح
                const scannerContainer = document.getElementById('scanner');
                await qrScanner.startScanning(scannerContainer);
                
                // تحديث واجهة المستخدم
                document.getElementById('cameraContainer').style.display = 'block';
                document.getElementById('startCamera').style.display = 'none';
                document.getElementById('stopCamera').style.display = 'inline-block';
                
                updateStatus('🔍 الكاميرا تعمل - وجه الكاميرا نحو كود QR', 'success');
                
            } catch (error) {
                console.error('خطأ في تشغيل الكاميرا:', error);
                updateStatus('❌ فشل في تشغيل الكاميرا: ' + error.message, 'error');
            }
        }
        
        // إيقاف الكاميرا
        function stopCamera() {
            if (qrScanner) {
                qrScanner.stopScanning();
                qrScanner = null;
            }
            
            document.getElementById('cameraContainer').style.display = 'none';
            document.getElementById('startCamera').style.display = 'inline-block';
            document.getElementById('stopCamera').style.display = 'none';
            
            updateStatus('📷 تم إيقاف الكاميرا', 'info');
        }
        
        // تبديل الكاميرا
        async function switchCamera() {
            if (qrScanner) {
                try {
                    await qrScanner.switchCamera();
                    updateStatus('🔄 تم تبديل الكاميرا', 'info');
                } catch (error) {
                    updateStatus('❌ فشل في تبديل الكاميرا: ' + error.message, 'error');
                }
            }
        }
        
        // إضافة نتيجة مسح
        function addScanResult(code, status) {
            scanResults.push({
                code: code,
                status: status,
                time: new Date().toLocaleString('ar-SA')
            });
            
            updateResultsList();
        }
        
        // تحديث قائمة النتائج
        function updateResultsList() {
            const resultsDiv = document.getElementById('results');
            const resultsList = document.getElementById('resultsList');
            
            if (scanResults.length === 0) {
                resultsDiv.style.display = 'none';
                return;
            }
            
            resultsDiv.style.display = 'block';
            
            let html = '';
            scanResults.forEach((result, index) => {
                const statusIcon = result.status === 'success' ? '✅' : '❌';
                html += `
                    <div class="result-box">
                        <strong>${statusIcon} نتيجة ${index + 1}:</strong><br>
                        <strong>الكود:</strong> ${result.code}<br>
                        <strong>الوقت:</strong> ${result.time}
                    </div>
                `;
            });
            
            resultsList.innerHTML = html;
        }
        
        // اختبار QR تجريبي
        function testQR() {
            const testCode = 'INV_TEST_' + Date.now();
            addScanResult(testCode, 'success');
            updateStatus('🧪 تم إضافة كود تجريبي: ' + testCode, 'success');
        }
        
        // ربط الأحداث
        document.getElementById('startCamera').addEventListener('click', startCamera);
        document.getElementById('stopCamera').addEventListener('click', stopCamera);
        document.getElementById('switchCamera').addEventListener('click', switchCamera);
        document.getElementById('testQR').addEventListener('click', testQR);
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            showBrowserInfo();
            getCameras();
            
            // التحقق من دعم الكاميرا
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                updateStatus('❌ المتصفح لا يدعم الكاميرا', 'error');
                document.getElementById('startCamera').disabled = true;
            }
        });
        
        // تنظيف الموارد عند إغلاق الصفحة
        window.addEventListener('beforeunload', function() {
            stopCamera();
        });
    </script>
</body>
</html>
