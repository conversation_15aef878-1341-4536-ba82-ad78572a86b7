/* ===== تصميم تطبيق جوال حديث ===== */

/* إعدادات أساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

:root {
    /* ألوان التطبيق */
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --danger-color: #FF3B30;
    --info-color: #5AC8FA;

    /* ألوان الخلفية */
    --bg-primary: #F2F2F7;
    --bg-secondary: #FFFFFF;
    --bg-tertiary: #F8F9FA;

    /* ألوان النص */
    --text-primary: #000000;
    --text-secondary: #8E8E93;
    --text-tertiary: #C7C7CC;

    /* الظلال */
    --shadow-light: 0 2px 10px rgba(0,0,0,0.08);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.12);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.16);

    /* الحواف */
    --radius-small: 8px;
    --radius-medium: 12px;
    --radius-large: 16px;
    --radius-xl: 20px;

    /* المسافات */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.5;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

.container {
    max-width: 428px; /* iPhone 14 Pro Max width */
    margin: 0 auto;
    padding: 0;
    min-height: 100vh;
    background: var(--bg-primary);
    position: relative;
}

/* تصميم الهاتف الذكي */
@media (min-width: 429px) {
    .container {
        max-width: 428px;
        margin: 20px auto;
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-heavy);
        overflow: hidden;
        border: 1px solid rgba(0,0,0,0.1);
    }

    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px 0;
    }
}

/* ===== شريط التنقل العلوي (Navigation Bar) ===== */
.header {
    background: var(--bg-secondary);
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--text-tertiary);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 44px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    text-align: center;
    flex: 1;
    letter-spacing: -0.3px;
}

.header-back {
    position: absolute;
    left: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
}

.header-actions {
    position: absolute;
    right: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

/* زر العودة */
.back-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: transparent;
    border: none;
    color: var(--primary-color);
    font-size: 18px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.back-btn:hover {
    background: rgba(0,122,255,0.1);
    transform: scale(1.1);
}

/* أزرار الهيدر */
.header-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    padding: 0 var(--spacing-sm);
    border-radius: var(--radius-medium);
    background: transparent;
    border: none;
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    white-space: nowrap;
}

.header-btn:hover {
    background: rgba(0,122,255,0.1);
    transform: scale(1.05);
}

.header-btn.primary {
    background: var(--primary-color);
    color: white;
}

.header-btn.primary:hover {
    background: #0056b3;
    transform: scale(1.05);
}

/* شريط الحالة (Status Bar) */
.status-bar {
    height: 44px;
    background: var(--bg-secondary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-lg);
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--text-tertiary);
}

.status-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.signal-bars {
    display: flex;
    gap: 2px;
    align-items: flex-end;
}

.signal-bar {
    width: 3px;
    background: var(--text-primary);
    border-radius: 1px;
}

.signal-bar:nth-child(1) { height: 4px; }
.signal-bar:nth-child(2) { height: 6px; }
.signal-bar:nth-child(3) { height: 8px; }
.signal-bar:nth-child(4) { height: 10px; }

.battery {
    width: 24px;
    height: 12px;
    border: 1px solid var(--text-primary);
    border-radius: 2px;
    position: relative;
}

.battery::after {
    content: '';
    position: absolute;
    right: -3px;
    top: 3px;
    width: 2px;
    height: 6px;
    background: var(--text-primary);
    border-radius: 0 1px 1px 0;
}

.battery-level {
    height: 100%;
    background: var(--success-color);
    border-radius: 1px;
    width: 80%;
}

/* ===== أزرار التطبيق الحديثة ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-medium);
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    min-height: 50px;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    letter-spacing: -0.2px;
    box-shadow: var(--shadow-light);
}

/* تأثيرات الأزرار */
.btn:active {
    transform: scale(0.96);
    box-shadow: var(--shadow-light);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* أنواع الأزرار */
.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: var(--text-secondary);
    color: white;
}

.btn-secondary:hover {
    background: #6c757d;
    box-shadow: var(--shadow-medium);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #28a745;
    box-shadow: var(--shadow-medium);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #e0a800;
    box-shadow: var(--shadow-medium);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #dc3545;
    box-shadow: var(--shadow-medium);
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-info:hover {
    background: #17a2b8;
    box-shadow: var(--shadow-medium);
}

/* أزرار شفافة */
.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    box-shadow: none;
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* أزرار مسطحة */
.btn-flat {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    box-shadow: none;
}

.btn-flat:hover {
    background: #e9ecef;
}

/* أحجام الأزرار */
.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 14px;
    min-height: 40px;
    border-radius: var(--radius-small);
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 18px;
    min-height: 56px;
    border-radius: var(--radius-large);
    font-weight: 700;
}

.btn-xl {
    width: 100%;
    padding: var(--spacing-lg);
    font-size: 18px;
    min-height: 56px;
    border-radius: var(--radius-large);
    font-weight: 700;
    margin: var(--spacing-md) 0;
}

/* أزرار الأيقونات */
.btn-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.btn-icon-sm {
    width: 40px;
    height: 40px;
    font-size: 16px;
}

/* ===== البطاقات والقوائم (Cards & Lists) ===== */

/* البطاقة الأساسية */
.card {
    background: var(--bg-secondary);
    border-radius: var(--radius-large);
    margin: var(--spacing-md);
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--text-tertiary);
    background: var(--bg-secondary);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.3px;
}

.card-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: var(--spacing-xs) 0 0 0;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--text-tertiary);
    background: var(--bg-tertiary);
}

/* قائمة التطبيق */
.app-list {
    background: var(--bg-secondary);
    border-radius: var(--radius-large);
    margin: var(--spacing-md);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.list-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--text-tertiary);
    transition: background 0.2s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.list-item:last-child {
    border-bottom: none;
}

.list-item:hover {
    background: var(--bg-tertiary);
}

.list-item:active {
    background: rgba(0,122,255,0.1);
}

.list-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: var(--spacing-md);
    font-size: 20px;
    flex-shrink: 0;
}

.list-icon.primary { background: var(--primary-color); color: white; }
.list-icon.success { background: var(--success-color); color: white; }
.list-icon.warning { background: var(--warning-color); color: white; }
.list-icon.danger { background: var(--danger-color); color: white; }
.list-icon.info { background: var(--info-color); color: white; }

.list-content {
    flex: 1;
    min-width: 0;
}

.list-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.list-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.list-action {
    margin-right: var(--spacing-md);
    color: var(--text-secondary);
    font-size: 16px;
    flex-shrink: 0;
}

.list-chevron {
    color: var(--text-tertiary);
    font-size: 14px;
}

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    padding: 0 var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: transform 0.2s ease;
}

.stat-card:active {
    transform: scale(0.98);
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -1px;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    margin: var(--spacing-xs) 0 0 0;
    font-weight: 500;
}

.stat-icon {
    font-size: 24px;
    margin-bottom: var(--spacing-sm);
    opacity: 0.8;
}

/* بطاقة ملونة */
.stat-card.primary { background: linear-gradient(135deg, var(--primary-color), #0056b3); color: white; }
.stat-card.success { background: linear-gradient(135deg, var(--success-color), #28a745); color: white; }
.stat-card.warning { background: linear-gradient(135deg, var(--warning-color), #e0a800); color: white; }
.stat-card.danger { background: linear-gradient(135deg, var(--danger-color), #dc3545); color: white; }

.stat-card.primary .stat-number,
.stat-card.success .stat-number,
.stat-card.warning .stat-number,
.stat-card.danger .stat-number {
    color: white;
}

.stat-card.primary .stat-label,
.stat-card.success .stat-label,
.stat-card.warning .stat-label,
.stat-card.danger .stat-label {
    color: rgba(255,255,255,0.8);
}

/* ===== النماذج (Forms) ===== */

.form-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-large);
    margin: var(--spacing-md);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.form-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--text-tertiary);
    text-align: center;
}

.form-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.4px;
}

.form-body {
    padding: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    letter-spacing: -0.1px;
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--text-tertiary);
    border-radius: var(--radius-medium);
    font-size: 16px;
    font-family: inherit;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: all 0.2s ease;
    -webkit-appearance: none;
    appearance: none;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--bg-secondary);
    box-shadow: 0 0 0 4px rgba(0,122,255,0.1);
}

.form-input::placeholder {
    color: var(--text-secondary);
}

.form-input.error {
    border-color: var(--danger-color);
    background: rgba(255,59,48,0.05);
}

.form-input.success {
    border-color: var(--success-color);
    background: rgba(52,199,89,0.05);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
    font-family: inherit;
    line-height: 1.5;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-left: 40px;
}

.form-error {
    font-size: 12px;
    color: var(--danger-color);
    margin-top: var(--spacing-xs);
    display: block;
}

.form-help {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
    display: block;
}

/* مجموعة الحقول */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.form-row.three {
    grid-template-columns: 1fr 1fr 1fr;
}

/* أزرار النموذج */
.form-actions {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--text-tertiary);
    background: var(--bg-tertiary);
    display: flex;
    gap: var(--spacing-md);
    flex-direction: column;
}

.form-actions.horizontal {
    flex-direction: row;
    justify-content: space-between;
}

/* تبديل (Toggle) */
.toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 30px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--text-tertiary);
    transition: 0.2s;
    border-radius: 30px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 2px;
    bottom: 2px;
    background: white;
    transition: 0.2s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle input:checked + .toggle-slider {
    background: var(--success-color);
}

.toggle input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* خانة الاختيار */
.checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    user-select: none;
}

.checkbox input {
    width: 20px;
    height: 20px;
    border: 2px solid var(--text-tertiary);
    border-radius: var(--spacing-xs);
    background: var(--bg-secondary);
    cursor: pointer;
    position: relative;
    -webkit-appearance: none;
    appearance: none;
}

.checkbox input:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox input:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* ===== شريط التنقل السفلي (Tab Bar) ===== */

.tab-bar {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 428px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--text-tertiary);
    padding: var(--spacing-sm) 0;
    z-index: 100;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.tab-items {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-sm);
    text-decoration: none;
    color: var(--text-secondary);
    transition: color 0.2s ease;
    min-width: 60px;
}

.tab-item.active {
    color: var(--primary-color);
}

.tab-icon {
    font-size: 24px;
    margin-bottom: var(--spacing-xs);
}

.tab-label {
    font-size: 10px;
    font-weight: 500;
    letter-spacing: -0.1px;
}

/* محتوى الصفحة مع مساحة للتنقل */
.page-content {
    padding-bottom: 80px; /* مساحة لشريط التنقل السفلي */
}

/* ===== التنبيهات والرسائل (Alerts) ===== */

.alert {
    margin: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-medium);
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.alert-success {
    background: rgba(52,199,89,0.1);
    color: var(--success-color);
    border: 1px solid rgba(52,199,89,0.2);
}

.alert-error {
    background: rgba(255,59,48,0.1);
    color: var(--danger-color);
    border: 1px solid rgba(255,59,48,0.2);
}

.alert-warning {
    background: rgba(255,149,0,0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255,149,0,0.2);
}

.alert-info {
    background: rgba(90,200,250,0.1);
    color: var(--info-color);
    border: 1px solid rgba(90,200,250,0.2);
}

.alert-icon {
    font-size: 18px;
    flex-shrink: 0;
}

/* ===== المودال (Modal) ===== */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--bg-secondary);
    border-radius: var(--radius-large) var(--radius-large) 0 0;
    width: 100%;
    max-width: 428px;
    max-height: 80vh;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.modal-overlay.active .modal {
    transform: translateY(0);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--text-tertiary);
    text-align: center;
    position: relative;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    position: absolute;
    top: 50%;
    right: var(--spacing-lg);
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-secondary);
    cursor: pointer;
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--text-tertiary);
    display: flex;
    gap: var(--spacing-md);
}

/* ===== شريط البحث (Search Bar) ===== */

.search-container {
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--text-tertiary);
}

.search-bar {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 40px;
    border: none;
    border-radius: var(--radius-medium);
    background: var(--bg-tertiary);
    font-size: 16px;
    color: var(--text-primary);
}

.search-input::placeholder {
    color: var(--text-secondary);
}

.search-icon {
    position: absolute;
    left: var(--spacing-md);
    color: var(--text-secondary);
    font-size: 16px;
    pointer-events: none;
}

.search-clear {
    position: absolute;
    right: var(--spacing-md);
    background: var(--text-secondary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
    display: none;
}

.search-input:not(:placeholder-shown) + .search-icon + .search-clear {
    display: block;
}

/* ===== صفحة تسجيل الدخول (Login) ===== */

.login-page {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.login-container {
    width: 100%;
    max-width: 380px;
}

.login-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-heavy);
    text-align: center;
}

.login-logo {
    font-size: 48px;
    margin-bottom: var(--spacing-lg);
}

.login-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    letter-spacing: -0.5px;
}

.login-subtitle {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

.user-types {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
}

.user-type {
    padding: var(--spacing-md);
    border-radius: var(--radius-medium);
    background: var(--bg-tertiary);
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.user-type:hover {
    background: rgba(0,122,255,0.1);
    transform: translateY(-2px);
}

.user-type-icon {
    font-size: 24px;
    margin-bottom: var(--spacing-xs);
    display: block;
}

.user-type-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-primary);
}

/* ===== صفحة الماسح الضوئي (Scanner) ===== */

.scanner-page {
    background: var(--bg-primary);
    min-height: 100vh;
}

.scanner-container {
    padding: var(--spacing-lg);
}

.scanner-input-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    text-align: center;
}

.scanner-input {
    width: 100%;
    padding: var(--spacing-lg);
    border: 3px solid var(--primary-color);
    border-radius: var(--radius-medium);
    font-size: 18px;
    font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
    text-align: center;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-weight: 600;
    letter-spacing: 1px;
}

.scanner-input:focus {
    outline: none;
    border-color: var(--success-color);
    box-shadow: 0 0 0 4px rgba(52,199,89,0.2);
}

.scanner-input.valid {
    border-color: var(--success-color);
    background: rgba(52,199,89,0.1);
}

.scanner-input.invalid {
    border-color: var(--danger-color);
    background: rgba(255,59,48,0.1);
}

.camera-section {
    background: var(--bg-secondary);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    text-align: center;
}

.camera-preview {
    width: 100%;
    max-width: 300px;
    border-radius: var(--radius-medium);
    margin: var(--spacing-md) auto;
    display: block;
}

.camera-controls {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
}

/* ===== الجداول (Tables) ===== */

.table-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-large);
    margin: var(--spacing-md);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--text-tertiary);
}

.table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
    position: sticky;
    top: 0;
}

.table tr:hover {
    background: var(--bg-tertiary);
}

.table tr.success {
    background: rgba(52,199,89,0.1);
}

.table tr.warning {
    background: rgba(255,149,0,0.1);
}

.table tr.danger {
    background: rgba(255,59,48,0.1);
}

/* ===== التحسينات النهائية ===== */

/* تحسينات الأداء */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

/* تحسينات التمرير */
html {
    scroll-behavior: smooth;
}

body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
}

/* تحسينات الخطوط */
body, input, textarea, select, button {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* تحسينات للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .btn, .form-input {
        border-width: 1px;
    }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #000000;
        --bg-secondary: #1C1C1E;
        --bg-tertiary: #2C2C2E;
        --text-primary: #FFFFFF;
        --text-secondary: #8E8E93;
        --text-tertiary: #48484A;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 375px) {
    .container {
        max-width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .user-types {
        grid-template-columns: 1fr;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .tab-bar {
        display: none;
    }

    .page-content {
        padding-bottom: 0;
    }
}

/* تحسينات للطباعة */
@media print {
    .header, .tab-bar, .btn {
        display: none !important;
    }

    .container {
        max-width: none;
        box-shadow: none;
        border: none;
    }

    .card, .app-list {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* أحجام الأزرار */
.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
    min-height: 36px;
    border-radius: 8px;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
    min-height: 52px;
    border-radius: 16px;
}

/* تحسينات الأزرار للجوال */
@media (max-width: 768px) {
    .btn {
        padding: 14px 20px;
        font-size: 14px;
        min-height: 48px;
        border-radius: 12px;
        width: auto;
        min-width: 120px;
    }

    .btn-sm {
        padding: 10px 16px;
        font-size: 12px;
        min-height: 40px;
        min-width: 100px;
    }

    .btn-lg {
        padding: 18px 32px;
        font-size: 16px;
        min-height: 56px;
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 480px) {
    .btn {
        padding: 12px 16px;
        font-size: 13px;
        min-height: 44px;
        min-width: 100px;
    }

    .header-actions .btn {
        padding: 10px 14px;
        font-size: 12px;
        min-height: 40px;
        min-width: 80px;
    }
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-light {
    background: #f8f9fa;
    color: #212529;
    border: 1px solid #dee2e6;
}

.btn-light:hover {
    background: #e2e6ea;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* التنبيهات */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* صفحة تسجيل الدخول */
.login-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 400px;
}

.login-box {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    color: #333;
    margin-bottom: 10px;
}

.login-header p {
    color: #666;
    font-size: 16px;
}

.login-form .form-group {
    margin-bottom: 20px;
}

.login-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.login-form input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.login-form input:focus {
    outline: none;
    border-color: #007bff;
}

.login-form .btn {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    margin-top: 10px;
}

.login-info {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
}

.login-info code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
}

/* أنواع المستخدمين */
.user-types {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin: 20px 0;
}

.user-type-card {
    text-align: center;
    padding: 15px 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.user-type-card:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.user-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.user-type-card h4 {
    margin: 5px 0;
    font-size: 14px;
    color: #333;
}

.user-type-card p {
    margin: 0;
    font-size: 11px;
    color: #666;
    line-height: 1.3;
}

.default-accounts {
    margin: 10px 0;
}

.account-info {
    background: #fff3cd;
    padding: 10px;
    border-radius: 5px;
    margin: 5px 0;
    border-left: 4px solid #ffc107;
    font-size: 13px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 480px) {
    .user-types {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .user-type-card {
        padding: 12px 8px;
    }

    .user-icon {
        font-size: 20px;
    }

    .user-type-card h4 {
        font-size: 13px;
    }

    .user-type-card p {
        font-size: 10px;
    }
}

/* الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card.success {
    border-left: 5px solid #28a745;
}

.stat-card.warning {
    border-left: 5px solid #ffc107;
}

.stat-icon {
    font-size: 40px;
    margin-left: 20px;
}

.stat-info h3 {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stat-info p {
    color: #666;
    font-size: 14px;
}

/* أدوات التحكم */
.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.controls-left, .controls-right {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-form {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-form input {
    padding: 10px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    min-width: 250px;
}

.search-form input:focus {
    outline: none;
    border-color: #007bff;
}

/* الجداول */
.table-container {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.guests-table {
    width: 100%;
    border-collapse: collapse;
}

.guests-table th,
.guests-table td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #e1e5e9;
}

.guests-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.guests-table tr:hover {
    background: #f8f9fa;
}

.guests-table tr.scanned {
    background: #d4edda;
}

.guest-name {
    font-weight: 500;
    color: #333;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.success {
    background: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.actions {
    display: flex;
    gap: 5px;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

/* النماذج */
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.guest-form .form-group {
    margin-bottom: 25px;
}

.guest-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.guest-form input,
.guest-form textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.guest-form input:focus,
.guest-form textarea:focus {
    outline: none;
    border-color: #007bff;
}

.guest-form textarea {
    resize: vertical;
    min-height: 100px;
}

.required {
    color: #dc3545;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

/* قسم النجاح */
.success-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.success-section h3 {
    color: #28a745;
    margin-bottom: 20px;
}

.qr-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.qr-preview code {
    background: #e9ecef;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: monospace;
    word-break: break-all;
    display: inline-block;
    margin: 10px 0;
}

.qr-image {
    margin: 20px 0;
}

.qr-image img {
    border: 2px solid #dee2e6;
    border-radius: 10px;
    margin-bottom: 15px;
}

.next-actions {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* التجاوب */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .controls-left,
    .controls-right {
        justify-content: center;
    }
    
    .search-form input {
        min-width: auto;
        width: 100%;
    }
    
    .guests-table {
        font-size: 14px;
    }
    
    .guests-table th,
    .guests-table td {
        padding: 10px 8px;
    }
    
    .actions {
        flex-direction: column;
    }
    
    .form-actions,
    .next-actions {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .login-box {
        padding: 20px;
    }

    .form-container {
        padding: 20px;
    }

    .success-section {
        padding: 20px;
    }
}

/* ===== تحسينات شاملة للجوال ===== */

/* النماذج المحسنة للجوال */
.form-container {
    background: white;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    border: 1px solid rgba(255,255,255,0.2);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    font-family: inherit;
    transition: all 0.3s ease;
    background: #fafbfc;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
    background: white;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
    transform: translateY(-1px);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    line-height: 1.5;
}

/* الجداول المحسنة للجوال */
.table-container {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    border: 1px solid rgba(255,255,255,0.2);
}

.guests-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.guests-table th,
.guests-table td {
    padding: 16px 12px;
    text-align: right;
    border-bottom: 1px solid #f0f2f5;
    vertical-align: middle;
}

.guests-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 700;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.guests-table tr {
    transition: all 0.3s ease;
}

.guests-table tr:hover {
    background: #f8f9fa;
    transform: scale(1.01);
}

.guests-table tr.scanned {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

/* البطاقات والإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 20px;
    padding: 25px;
    display: flex;
    align-items: center;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-icon {
    font-size: 48px;
    margin-left: 20px;
    opacity: 0.8;
}

.stat-content h3 {
    font-size: 32px;
    font-weight: 700;
    margin: 0;
    color: #333;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

/* أدوات التحكم */
.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
    flex-wrap: wrap;
}

.controls-left {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.controls-right {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-form {
    display: flex;
    gap: 8px;
    align-items: center;
    background: white;
    padding: 8px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.2);
}

.search-form input {
    padding: 12px 16px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    min-width: 200px;
    background: #fafbfc;
    transition: all 0.3s ease;
}

.search-form input:focus {
    outline: none;
    background: white;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.2);
}

/* ===== تحسينات خاصة للجوال ===== */

/* للشاشات المتوسطة (التابلت) */
@media (max-width: 1024px) {
    .container {
        padding: 15px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .controls-left,
    .controls-right {
        justify-content: center;
    }
}

/* للشاشات الصغيرة (الجوال الكبير) */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    /* الهيدر */
    .header {
        padding: 20px 15px;
        margin-bottom: 15px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header h1 {
        font-size: 24px;
        margin-bottom: 10px;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }

    /* الأزرار */
    .btn {
        min-width: 120px;
        padding: 14px 18px;
        font-size: 14px;
    }

    .header-actions .btn {
        min-width: 100px;
        padding: 12px 16px;
        font-size: 13px;
    }

    /* الإحصائيات */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        margin: 0 0 15px 0;
        font-size: 40px;
    }

    /* أدوات التحكم */
    .controls {
        flex-direction: column;
        gap: 15px;
    }

    .controls-left {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .controls-left .btn {
        flex: 1;
        min-width: 140px;
        max-width: 200px;
    }

    .controls-right {
        width: 100%;
        justify-content: center;
    }

    .search-form {
        width: 100%;
        max-width: 400px;
        padding: 10px;
    }

    .search-form input {
        min-width: auto;
        flex: 1;
        font-size: 16px; /* منع التكبير في iOS */
    }

    /* النماذج */
    .form-container {
        padding: 20px 15px;
        margin-bottom: 15px;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 18px 16px;
        font-size: 16px; /* منع التكبير في iOS */
        border-radius: 12px;
    }

    /* الجداول */
    .table-container {
        margin-bottom: 15px;
        border-radius: 15px;
    }

    .guests-table {
        font-size: 13px;
    }

    .guests-table th,
    .guests-table td {
        padding: 12px 8px;
    }

    .guests-table th {
        font-size: 11px;
        padding: 15px 8px;
    }

    /* الإجراءات في الجدول */
    .actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
        min-width: 120px;
    }

    .actions .btn {
        width: 100%;
        min-width: auto;
        padding: 10px 12px;
        font-size: 12px;
    }
}

/* للشاشات الصغيرة جداً (الجوال الصغير) */
@media (max-width: 480px) {
    .container {
        padding: 8px;
    }

    /* الهيدر */
    .header {
        padding: 15px 12px;
        margin-bottom: 12px;
        border-radius: 15px;
    }

    .header h1 {
        font-size: 20px;
        margin-bottom: 8px;
    }

    .header-actions .btn {
        min-width: 80px;
        padding: 10px 12px;
        font-size: 12px;
        border-radius: 10px;
    }

    /* النماذج */
    .form-container {
        padding: 15px 12px;
        border-radius: 15px;
    }

    .form-group {
        margin-bottom: 18px;
    }

    .form-group label {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 16px 14px;
        border-radius: 10px;
    }

    /* الأزرار */
    .btn {
        min-width: 100px;
        padding: 12px 16px;
        font-size: 13px;
        border-radius: 10px;
    }

    .btn-lg {
        width: 100%;
        max-width: none;
        padding: 16px 20px;
        font-size: 15px;
    }

    /* الإحصائيات */
    .stat-card {
        padding: 18px 15px;
        border-radius: 15px;
    }

    .stat-icon {
        font-size: 36px;
        margin-bottom: 12px;
    }

    .stat-content h3 {
        font-size: 28px;
    }

    .stat-content p {
        font-size: 13px;
    }

    /* أدوات التحكم */
    .controls-left .btn {
        min-width: 120px;
        font-size: 12px;
    }

    .search-form {
        padding: 8px;
    }

    .search-form input {
        padding: 14px 12px;
        font-size: 16px;
    }

    /* الجداول */
    .guests-table {
        font-size: 12px;
    }

    .guests-table th,
    .guests-table td {
        padding: 10px 6px;
    }

    .guests-table th {
        font-size: 10px;
        padding: 12px 6px;
    }

    .actions .btn {
        padding: 8px 10px;
        font-size: 11px;
        min-height: 36px;
    }
}

/* تحسينات خاصة للمس */
@media (hover: none) and (pointer: coarse) {
    .btn:hover {
        transform: none;
        box-shadow: inherit;
    }

    .btn:active {
        transform: scale(0.95);
    }

    .stat-card:hover {
        transform: none;
    }

    .guests-table tr:hover {
        background: inherit;
        transform: none;
    }
}

/* تحسينات للوضع الأفقي على الجوال */
@media (max-width: 768px) and (orientation: landscape) {
    .header-content {
        flex-direction: row;
        justify-content: space-between;
    }

    .header h1 {
        font-size: 22px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .controls {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .controls-left {
        flex: 1;
        min-width: 300px;
    }

    .controls-right {
        flex: 0 0 auto;
    }
}

/* ===== تحسينات صفحة تسجيل الدخول للجوال ===== */

.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
}

.login-box {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 40px 30px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    text-align: center;
}

.login-header h1 {
    font-size: clamp(24px, 6vw, 32px);
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
}

.login-header p {
    color: #666;
    font-size: 16px;
    margin-bottom: 30px;
    font-weight: 500;
}

.user-types {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 30px;
}

.user-type-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 20px 10px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.user-type-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-color: rgba(0,123,255,0.3);
}

.user-icon {
    font-size: 32px;
    margin-bottom: 10px;
    display: block;
}

.user-type-card h4 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.user-type-card p {
    font-size: 11px;
    color: #666;
    margin: 0;
    line-height: 1.3;
}

.login-form {
    text-align: right;
}

.login-form .form-group {
    margin-bottom: 20px;
    text-align: right;
}

.login-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.login-form input {
    width: 100%;
    padding: 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fafbfc;
    text-align: right;
}

.login-form input:focus {
    outline: none;
    border-color: #007bff;
    background: white;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
    transform: translateY(-1px);
}

.login-form .btn {
    width: 100%;
    padding: 18px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 15px;
    border-radius: 12px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    box-shadow: 0 6px 20px rgba(0,123,255,0.3);
}

.login-form .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.4);
}

/* تحسينات صفحة تسجيل الدخول للجوال */
@media (max-width: 768px) {
    .login-page {
        padding: 15px;
        align-items: flex-start;
        padding-top: 40px;
    }

    .login-box {
        padding: 30px 20px;
        border-radius: 20px;
    }

    .login-header h1 {
        font-size: 26px;
    }

    .login-header p {
        font-size: 14px;
        margin-bottom: 25px;
    }

    .user-types {
        grid-template-columns: 1fr;
        gap: 12px;
        margin-bottom: 25px;
    }

    .user-type-card {
        padding: 15px;
        display: flex;
        align-items: center;
        text-align: right;
        gap: 15px;
    }

    .user-icon {
        font-size: 28px;
        margin-bottom: 0;
        flex-shrink: 0;
    }

    .user-type-card h4 {
        font-size: 15px;
        margin-bottom: 3px;
    }

    .user-type-card p {
        font-size: 12px;
    }

    .login-form input {
        padding: 18px 16px;
        font-size: 16px; /* منع التكبير في iOS */
    }

    .login-form .btn {
        padding: 20px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .login-page {
        padding: 10px;
        padding-top: 30px;
    }

    .login-box {
        padding: 25px 15px;
        border-radius: 18px;
    }

    .login-header h1 {
        font-size: 24px;
    }

    .login-header p {
        font-size: 13px;
        margin-bottom: 20px;
    }

    .user-types {
        gap: 10px;
        margin-bottom: 20px;
    }

    .user-type-card {
        padding: 12px;
        gap: 12px;
        border-radius: 12px;
    }

    .user-icon {
        font-size: 24px;
    }

    .user-type-card h4 {
        font-size: 14px;
    }

    .user-type-card p {
        font-size: 11px;
    }

    .login-form .form-group {
        margin-bottom: 18px;
    }

    .login-form label {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .login-form input {
        padding: 16px 14px;
        border-radius: 10px;
    }

    .login-form .btn {
        padding: 18px;
        font-size: 15px;
        border-radius: 10px;
    }
}

/* ===== تحسينات صفحة الموظف (الماسح الضوئي) للجوال ===== */

.scanner-container {
    background: white;
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    text-align: center;
    border: 1px solid rgba(255,255,255,0.2);
}

.scanner-input {
    font-size: 18px;
    padding: 20px;
    border: 3px solid #007bff;
    border-radius: 15px;
    width: 100%;
    max-width: 500px;
    text-align: center;
    font-family: 'Courier New', monospace;
    margin: 20px auto;
    background: #fafbfc;
    transition: all 0.3s ease;
    font-weight: 600;
    letter-spacing: 1px;
}

.scanner-input:focus {
    outline: none;
    border-color: #0056b3;
    background: white;
    box-shadow: 0 0 0 4px rgba(0,123,255,0.2);
    transform: translateY(-2px);
}

.scanner-input.valid {
    border-color: #28a745;
    background: #f8fff9;
}

.scanner-input.invalid {
    border-color: #dc3545;
    background: #fff8f8;
}

.camera-section {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.camera-section.active {
    border-color: #007bff;
    background: #f0f8ff;
}

.camera-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin: 20px 0;
    flex-wrap: wrap;
}

.camera-preview {
    max-width: 100%;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    margin: 15px 0;
}

.scan-result {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    border-left: 5px solid #28a745;
}

.scan-result.error {
    border-left-color: #dc3545;
    background: #fff5f5;
}

.scan-result.warning {
    border-left-color: #ffc107;
    background: #fffbf0;
}

.input-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    border: 1px solid #dee2e6;
    word-break: break-all;
}

.input-preview.link {
    color: #007bff;
    border-color: #007bff;
    background: #f0f8ff;
}

.input-preview.code {
    color: #28a745;
    border-color: #28a745;
    background: #f8fff9;
}

.input-preview.partial {
    color: #ffc107;
    border-color: #ffc107;
    background: #fffbf0;
}

/* تحسينات صفحة الموظف للجوال */
@media (max-width: 768px) {
    .scanner-container {
        padding: 20px 15px;
        margin-bottom: 15px;
        border-radius: 15px;
    }

    .scanner-input {
        font-size: 16px;
        padding: 18px 15px;
        border-radius: 12px;
        margin: 15px 0;
        max-width: 100%;
    }

    .camera-section {
        padding: 15px;
        margin: 15px 0;
        border-radius: 12px;
    }

    .camera-controls {
        gap: 10px;
        margin: 15px 0;
    }

    .camera-controls .btn {
        flex: 1;
        min-width: 120px;
        max-width: 200px;
        padding: 14px 16px;
        font-size: 14px;
    }

    .camera-preview {
        width: 100%;
        max-width: 100%;
        height: auto;
        border-radius: 10px;
    }

    .scan-result {
        padding: 15px;
        margin: 15px 0;
        border-radius: 12px;
    }

    .input-preview {
        padding: 12px;
        margin: 12px 0;
        font-size: 13px;
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .scanner-container {
        padding: 15px 12px;
        border-radius: 12px;
    }

    .scanner-input {
        font-size: 15px;
        padding: 16px 12px;
        border-radius: 10px;
        letter-spacing: 0.5px;
    }

    .camera-section {
        padding: 12px;
        margin: 12px 0;
        border-radius: 10px;
    }

    .camera-controls {
        flex-direction: column;
        gap: 8px;
        margin: 12px 0;
    }

    .camera-controls .btn {
        width: 100%;
        max-width: none;
        padding: 16px;
        font-size: 14px;
        border-radius: 10px;
    }

    .scan-result {
        padding: 12px;
        margin: 12px 0;
        border-radius: 10px;
    }

    .input-preview {
        padding: 10px;
        margin: 10px 0;
        font-size: 12px;
        border-radius: 6px;
    }
}

/* تحسينات خاصة للكاميرا على الجوال */
@media (max-width: 768px) {
    #camera-preview {
        width: 100% !important;
        height: auto !important;
        max-height: 300px;
        object-fit: cover;
        border-radius: 12px;
    }

    .camera-container {
        position: relative;
        width: 100%;
        max-width: 400px;
        margin: 0 auto;
    }

    .camera-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 200px;
        height: 200px;
        border: 3px solid #007bff;
        border-radius: 15px;
        pointer-events: none;
        opacity: 0.8;
    }

    .camera-overlay::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        border: 2px solid rgba(255,255,255,0.8);
        border-radius: 15px;
    }
}

/* تحسينات للوضع الأفقي على الجوال */
@media (max-width: 768px) and (orientation: landscape) {
    .scanner-container {
        padding: 15px;
    }

    .camera-controls {
        flex-direction: row;
        justify-content: center;
    }

    .camera-controls .btn {
        flex: 0 1 auto;
        min-width: 120px;
        max-width: 150px;
    }

    #camera-preview {
        max-height: 200px;
    }

    .camera-overlay {
        width: 150px;
        height: 150px;
    }
}

/* ===== تحسينات عامة للأداء والتجربة ===== */

/* تحسينات الأداء */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

input, textarea, select {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* تحسينات التمرير */
html {
    scroll-behavior: smooth;
}

body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
}

/* تحسينات الخطوط */
body, input, textarea, select, button {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* تحسينات الانتقالات */
.btn, .form-group input, .form-group textarea, .form-group select,
.stat-card, .table-container, .form-container, .header {
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* تحسينات للشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .btn {
        border: 0.5px solid transparent;
    }

    .form-group input, .form-group textarea, .form-group select {
        border-width: 1.5px;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .form-group input, .form-group textarea, .form-group select {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
        background: #1a202c;
        border-color: #4299e1;
    }
}

/* تحسينات لتوفير البيانات */
@media (prefers-reduced-data: reduce) {
    .btn::before,
    .stat-card::before {
        display: none;
    }

    .btn, .stat-card {
        box-shadow: none;
    }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .btn:hover, .stat-card:hover {
        transform: none;
    }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid currentColor;
    }

    .form-group input, .form-group textarea, .form-group select {
        border-width: 3px;
    }

    .stat-card {
        border: 2px solid #333;
    }
}

/* تحسينات إضافية للجوال */
@media (max-width: 768px) {
    /* تحسين التمرير */
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .guests-table {
        min-width: 600px;
    }

    /* تحسين النصوص */
    h1, h2, h3, h4, h5, h6 {
        word-wrap: break-word;
        hyphens: auto;
    }

    /* تحسين الصور */
    img {
        max-width: 100%;
        height: auto;
    }

    /* تحسين الفيديو */
    video {
        max-width: 100%;
        height: auto;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 320px) {
    .container {
        padding: 5px;
    }

    .header {
        padding: 10px;
        margin-bottom: 10px;
    }

    .form-container {
        padding: 12px;
        margin-bottom: 10px;
    }

    .btn {
        min-width: 80px;
        padding: 10px 12px;
        font-size: 12px;
    }

    .stat-card {
        padding: 12px;
    }

    .guests-table th,
    .guests-table td {
        padding: 8px 4px;
        font-size: 11px;
    }
}

/* تحسينات للطباعة */
@media print {
    .header-actions,
    .controls,
    .btn {
        display: none !important;
    }

    .container {
        max-width: none;
        padding: 0;
    }

    .table-container,
    .form-container {
        box-shadow: none;
        border: 1px solid #333;
    }

    .guests-table th,
    .guests-table td {
        border: 1px solid #333;
    }
}

/* تحسينات للوصولية */
.btn:focus,
.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: 3px solid #4299e1;
    outline-offset: 2px;
}

/* إخفاء outline للماوس فقط */
.btn:focus:not(:focus-visible),
.form-group input:focus:not(:focus-visible),
.form-group textarea:focus:not(:focus-visible),
.form-group select:focus:not(:focus-visible) {
    outline: none;
}

/* تحسينات للقراءة */
.guests-table,
.form-container,
.stat-card {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
}

/* تحسينات نهائية للجوال */
@supports (env(safe-area-inset-top)) {
    .container {
        padding-top: max(15px, env(safe-area-inset-top));
        padding-bottom: max(15px, env(safe-area-inset-bottom));
        padding-left: max(15px, env(safe-area-inset-left));
        padding-right: max(15px, env(safe-area-inset-right));
    }
}
