/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* الهيدر */
.header {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.header h1 {
    color: #333;
    font-size: 28px;
    margin-bottom: 5px;
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* الأزرار */
.btn {
    display: inline-block;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-light {
    background: #f8f9fa;
    color: #212529;
    border: 1px solid #dee2e6;
}

.btn-light:hover {
    background: #e2e6ea;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* التنبيهات */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* صفحة تسجيل الدخول */
.login-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 400px;
}

.login-box {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    color: #333;
    margin-bottom: 10px;
}

.login-header p {
    color: #666;
    font-size: 16px;
}

.login-form .form-group {
    margin-bottom: 20px;
}

.login-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.login-form input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.login-form input:focus {
    outline: none;
    border-color: #007bff;
}

.login-form .btn {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    margin-top: 10px;
}

.login-info {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
}

.login-info code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
}

/* أنواع المستخدمين */
.user-types {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin: 20px 0;
}

.user-type-card {
    text-align: center;
    padding: 15px 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.user-type-card:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.user-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.user-type-card h4 {
    margin: 5px 0;
    font-size: 14px;
    color: #333;
}

.user-type-card p {
    margin: 0;
    font-size: 11px;
    color: #666;
    line-height: 1.3;
}

.default-accounts {
    margin: 10px 0;
}

.account-info {
    background: #fff3cd;
    padding: 10px;
    border-radius: 5px;
    margin: 5px 0;
    border-left: 4px solid #ffc107;
    font-size: 13px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 480px) {
    .user-types {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .user-type-card {
        padding: 12px 8px;
    }

    .user-icon {
        font-size: 20px;
    }

    .user-type-card h4 {
        font-size: 13px;
    }

    .user-type-card p {
        font-size: 10px;
    }
}

/* الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card.success {
    border-left: 5px solid #28a745;
}

.stat-card.warning {
    border-left: 5px solid #ffc107;
}

.stat-icon {
    font-size: 40px;
    margin-left: 20px;
}

.stat-info h3 {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stat-info p {
    color: #666;
    font-size: 14px;
}

/* أدوات التحكم */
.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.controls-left, .controls-right {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-form {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-form input {
    padding: 10px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    min-width: 250px;
}

.search-form input:focus {
    outline: none;
    border-color: #007bff;
}

/* الجداول */
.table-container {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.guests-table {
    width: 100%;
    border-collapse: collapse;
}

.guests-table th,
.guests-table td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #e1e5e9;
}

.guests-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.guests-table tr:hover {
    background: #f8f9fa;
}

.guests-table tr.scanned {
    background: #d4edda;
}

.guest-name {
    font-weight: 500;
    color: #333;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.success {
    background: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.actions {
    display: flex;
    gap: 5px;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

/* النماذج */
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.guest-form .form-group {
    margin-bottom: 25px;
}

.guest-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.guest-form input,
.guest-form textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.guest-form input:focus,
.guest-form textarea:focus {
    outline: none;
    border-color: #007bff;
}

.guest-form textarea {
    resize: vertical;
    min-height: 100px;
}

.required {
    color: #dc3545;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

/* قسم النجاح */
.success-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.success-section h3 {
    color: #28a745;
    margin-bottom: 20px;
}

.qr-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.qr-preview code {
    background: #e9ecef;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: monospace;
    word-break: break-all;
    display: inline-block;
    margin: 10px 0;
}

.qr-image {
    margin: 20px 0;
}

.qr-image img {
    border: 2px solid #dee2e6;
    border-radius: 10px;
    margin-bottom: 15px;
}

.next-actions {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* التجاوب */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .controls-left,
    .controls-right {
        justify-content: center;
    }
    
    .search-form input {
        min-width: auto;
        width: 100%;
    }
    
    .guests-table {
        font-size: 14px;
    }
    
    .guests-table th,
    .guests-table td {
        padding: 10px 8px;
    }
    
    .actions {
        flex-direction: column;
    }
    
    .form-actions,
    .next-actions {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .login-box {
        padding: 20px;
    }
    
    .form-container {
        padding: 20px;
    }
    
    .success-section {
        padding: 20px;
    }
}
