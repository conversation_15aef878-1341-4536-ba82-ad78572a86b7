# 🎫 نظام إدارة الدعوات بكود QR - متعدد المستويات

نظام شامل ومتطور لإدارة الدعوات باستخدام أكواد QR، مطور بـ PHP وMySQL مع نظام مستخدمين متعدد المستويات وواجهة عربية احترافية.

## ✨ المميزات

### 👥 نظام المستخدمين متعدد المستويات
- **👑 المدير:** إدارة كاملة للنظام والمستخدمين
- **👨‍💼 المشرف:** إنشاء وإدارة الدعوات الخاصة به
- **📱 الموظف:** مسح أكواد QR لتسجيل الحضور
- **🔐 نظام صلاحيات متقدم** مع فصل كامل للأدوار
- **📊 تتبع العمليات** وتسجيل جميع الأنشطة

### 👥 إدارة الضيوف
- **إضافة ضيوف فرديين** مع بيانات مفصلة
- **إنشاء دعوات متعددة بالعدد** (حتى 1000 دعوة)
- **إدارة مجموعات الدعوات** مع إحصائيات مفصلة
- **البحث المتقدم** في قائمة الضيوف

### 📱 أكواد QR المتطورة
- **توليد أكواد QR متعددة الطرق** (PNG/HTML/Text)
- **إعادة توليد الأكواد المفقودة** جماعياً
- **التحقق من الحضور** عبر مسح كود QR
- **روابط احتياطية** تعمل دائماً

### 📊 التقارير والطباعة
- **تصدير البيانات** (CSV/JSON)
- **طباعة الدعوات** بتصميم احترافي
- **إحصائيات مفصلة** لكل مجموعة
- **تقارير الحضور** الفورية

## 🛠️ متطلبات التشغيل

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- إضافة PDO لـ PHP

## 📦 التثبيت

### 1. تحضير قاعدة البيانات

1. افتح phpMyAdmin أو أي أداة إدارة MySQL
2. قم بتشغيل الملف `database.sql` لإنشاء قاعدة البيانات والجداول

### 2. إعداد الملفات

1. انسخ جميع الملفات إلى مجلد الخادم
2. تأكد من وجود المجلدات التالية:
   - `qrcodes/` (لحفظ صور أكواد QR)
   - `assets/` (للملفات المساعدة)

### 3. إعداد الصلاحيات

```bash
chmod 755 qrcodes/
chmod 755 assets/
```

### 4. تعديل إعدادات قاعدة البيانات

افتح ملف `config.php` وعدل الإعدادات التالية:

```php
define('DB_HOST', 'localhost');     // عنوان الخادم
define('DB_NAME', 'invitation_system'); // اسم قاعدة البيانات
define('DB_USER', 'root');          // اسم المستخدم
define('DB_PASS', '');              // كلمة المرور
```

## 🚀 الاستخدام

### تسجيل الدخول

- اذهب إلى `http://yoursite.com/login.php`
- **👑 المدير الافتراضي:**
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`

### إنشاء مستخدمين جدد

**للمدير فقط:**
1. سجل دخول كمدير
2. اذهب إلى لوحة تحكم المدير
3. استخدم نموذج "إنشاء مستخدم جديد"
4. اختر نوع المستخدم (مشرف/موظف)
5. أدخل البيانات المطلوبة

### إضافة ضيف واحد

1. من لوحة التحكم، اضغط "➕ إضافة ضيف جديد"
2. أدخل اسم الضيف (مطلوب)
3. أدخل رقم الجوال (اختياري)
4. أضف أي ملاحظات (اختياري)
5. اضغط "إضافة الضيف وتوليد QR"

### إنشاء دعوات متعددة

1. من لوحة التحكم، اضغط "📦 إنشاء دعوات متعددة"
2. أدخل عدد الدعوات المطلوبة (1-1000)
3. اختر بادئة الاسم (مثل: ضيف، مدعو)
4. أضف ملاحظات عامة (اختياري)
5. اختر توليد أكواد QR (اختياري)
6. اضغط "إنشاء الدعوات"

### إدارة المجموعات

1. من لوحة التحكم، اضغط "📊 إدارة المجموعات"
2. عرض إحصائيات كل مجموعة
3. طباعة أو تصدير مجموعة محددة
4. حذف مجموعات كاملة

### التحقق من الحضور

- يمكن للضيوف مسح كود QR باستخدام أي تطبيق قارئ QR
- أو زيارة الرابط المطبوع على الدعوة
- سيتم تسجيل الحضور تلقائياً في النظام

## 📁 هيكل الملفات

```
├── index.php              # الصفحة الرئيسية (توجيه تلقائي)
├── login.php              # صفحة تسجيل الدخول الموحدة
├── logout.php             # تسجيل الخروج
├── unauthorized.php       # صفحة عدم التصريح
├── admin_dashboard.php    # لوحة تحكم المدير
├── supervisor_dashboard.php # لوحة تحكم المشرف
├── employee_dashboard.php # لوحة تحكم الموظف
├── dashboard.php          # لوحة التحكم القديمة (للتوافق)
├── add_guest.php          # إضافة ضيف واحد
├── bulk_invitations.php   # إنشاء دعوات متعددة
├── manage_bulk.php        # إدارة مجموعات الدعوات
├── verify.php             # التحقق من كود QR
├── export.php             # تصدير البيانات
├── print_invitations.php  # طباعة الدعوات
├── regenerate_qr.php      # إعادة توليد أكواد QR
├── qr_generator.php       # مولد أكواد QR المحسن
├── test_system.php        # اختبار النظام
├── migrate_database.php   # ترحيل قاعدة البيانات
├── config.php             # إعدادات النظام (محدث)
├── database.sql           # ملف قاعدة البيانات (محدث)
├── assets/
│   └── style.css          # ملف التصميم
├── qrcodes/               # مجلد أكواد QR
├── README.md              # دليل المشروع
├── INSTALL.txt            # تعليمات التثبيت
├── UPDATE_NOTES.md        # ملاحظات التحديثات
├── QR_TROUBLESHOOTING.md  # حل مشاكل QR
├── BULK_INVITATIONS_GUIDE.md # دليل الدعوات المتعددة
└── MULTI_USER_SYSTEM_GUIDE.md # دليل النظام متعدد المستويات
```

## 🔧 التخصيص

### تغيير بيانات المسؤول

في ملف `config.php`:

```php
define('ADMIN_USERNAME', 'your_username');
define('ADMIN_PASSWORD', 'your_password');
```

### تغيير رابط الموقع

```php
define('SITE_URL', 'http://yoursite.com');
```

## 📊 قاعدة البيانات

### جدول `guests`

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INT | المعرف الفريد |
| name | VARCHAR(100) | اسم الضيف |
| phone | VARCHAR(20) | رقم الجوال |
| notes | TEXT | ملاحظات |
| code | VARCHAR(50) | الكود الفريد |
| scanned | TINYINT | حالة المسح (0/1) |
| scanned_at | TIMESTAMP | تاريخ المسح |
| created_at | TIMESTAMP | تاريخ الإنشاء |

## 🔒 الأمان

- كلمات المرور محمية بـ hashing
- حماية من SQL Injection
- تنظيف البيانات المدخلة
- جلسات آمنة

## 🐛 استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
- تأكد من صحة بيانات الاتصال في `config.php`
- تأكد من تشغيل خدمة MySQL

### لا تظهر أكواد QR
- تأكد من وجود مجلد `qrcodes/`
- تأكد من صلاحيات الكتابة على المجلد
- تأكد من الاتصال بالإنترنت (يستخدم Google Charts API)

### مشاكل في التصميم
- تأكد من وجود ملف `assets/style.css`
- تأكد من صحة مسار الملف

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يمكنك:
- فتح issue في المشروع
- التواصل مع المطور

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

---

**تم التطوير بـ ❤️ للمجتمع العربي**
