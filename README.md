# 🎫 نظام إدارة الدعوات بكود QR

نظام شامل لإدارة الدعوات باستخدام أكواد QR، مطور بـ PHP وMySQL مع واجهة عربية سهلة الاستخدام.

## ✨ المميزات

- 🔐 **نظام تسجيل دخول آمن للمسؤول**
- ➕ **إضافة ضيوف جدد مع توليد كود QR فريد**
- 📱 **التحقق من الحضور عبر مسح كود QR**
- 📊 **لوحة تحكم شاملة مع الإحصائيات**
- 🔍 **البحث في قائمة الضيوف**
- 📥 **تصدير البيانات (CSV/JSON)**
- 📱 **تصميم متجاوب يعمل على جميع الأجهزة**
- 🌐 **واجهة عربية كاملة**

## 🛠️ متطلبات التشغيل

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- إضافة PDO لـ PHP

## 📦 التثبيت

### 1. تحضير قاعدة البيانات

1. افتح phpMyAdmin أو أي أداة إدارة MySQL
2. قم بتشغيل الملف `database.sql` لإنشاء قاعدة البيانات والجداول

### 2. إعداد الملفات

1. انسخ جميع الملفات إلى مجلد الخادم
2. تأكد من وجود المجلدات التالية:
   - `qrcodes/` (لحفظ صور أكواد QR)
   - `assets/` (للملفات المساعدة)

### 3. إعداد الصلاحيات

```bash
chmod 755 qrcodes/
chmod 755 assets/
```

### 4. تعديل إعدادات قاعدة البيانات

افتح ملف `config.php` وعدل الإعدادات التالية:

```php
define('DB_HOST', 'localhost');     // عنوان الخادم
define('DB_NAME', 'invitation_system'); // اسم قاعدة البيانات
define('DB_USER', 'root');          // اسم المستخدم
define('DB_PASS', '');              // كلمة المرور
```

## 🚀 الاستخدام

### تسجيل الدخول

- اذهب إلى `http://yoursite.com/login.php`
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

### إضافة ضيف جديد

1. من لوحة التحكم، اضغط "إضافة ضيف جديد"
2. أدخل اسم الضيف (مطلوب)
3. أدخل رقم الجوال (اختياري)
4. أضف أي ملاحظات (اختياري)
5. اضغط "إضافة الضيف وتوليد QR"

### التحقق من الحضور

- يمكن للضيوف مسح كود QR باستخدام أي تطبيق قارئ QR
- أو زيارة الرابط المطبوع على الدعوة
- سيتم تسجيل الحضور تلقائياً في النظام

## 📁 هيكل الملفات

```
├── index.php              # الصفحة الرئيسية
├── login.php              # صفحة تسجيل الدخول
├── dashboard.php          # لوحة التحكم
├── add_guest.php          # إضافة ضيف جديد
├── verify.php             # التحقق من كود QR
├── export.php             # تصدير البيانات
├── logout.php             # تسجيل الخروج
├── config.php             # إعدادات النظام
├── database.sql           # ملف قاعدة البيانات
├── assets/
│   └── style.css          # ملف التصميم
├── qrcodes/               # مجلد أكواد QR
└── README.md              # هذا الملف
```

## 🔧 التخصيص

### تغيير بيانات المسؤول

في ملف `config.php`:

```php
define('ADMIN_USERNAME', 'your_username');
define('ADMIN_PASSWORD', 'your_password');
```

### تغيير رابط الموقع

```php
define('SITE_URL', 'http://yoursite.com');
```

## 📊 قاعدة البيانات

### جدول `guests`

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INT | المعرف الفريد |
| name | VARCHAR(100) | اسم الضيف |
| phone | VARCHAR(20) | رقم الجوال |
| notes | TEXT | ملاحظات |
| code | VARCHAR(50) | الكود الفريد |
| scanned | TINYINT | حالة المسح (0/1) |
| scanned_at | TIMESTAMP | تاريخ المسح |
| created_at | TIMESTAMP | تاريخ الإنشاء |

## 🔒 الأمان

- كلمات المرور محمية بـ hashing
- حماية من SQL Injection
- تنظيف البيانات المدخلة
- جلسات آمنة

## 🐛 استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
- تأكد من صحة بيانات الاتصال في `config.php`
- تأكد من تشغيل خدمة MySQL

### لا تظهر أكواد QR
- تأكد من وجود مجلد `qrcodes/`
- تأكد من صلاحيات الكتابة على المجلد
- تأكد من الاتصال بالإنترنت (يستخدم Google Charts API)

### مشاكل في التصميم
- تأكد من وجود ملف `assets/style.css`
- تأكد من صحة مسار الملف

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يمكنك:
- فتح issue في المشروع
- التواصل مع المطور

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

---

**تم التطوير بـ ❤️ للمجتمع العربي**
