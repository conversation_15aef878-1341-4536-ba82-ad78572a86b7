<?php
require_once 'config.php';

// التحقق من صلاحيات المدير
requireAdmin();

$message = '';

// معالجة إنشاء مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_user'])) {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $full_name = sanitizeInput($_POST['full_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $user_type = sanitizeInput($_POST['user_type'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($username) || empty($password) || empty($full_name) || empty($user_type)) {
        $message = showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
    } elseif (strlen($password) < 6) {
        $message = showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
    } else {
        $result = createUser($username, $password, $full_name, $email, $phone, $user_type);
        
        if ($result['success']) {
            $message = showAlert("تم إنشاء المستخدم {$full_name} بنجاح! معرف المستخدم: {$result['user_id']}", 'success');
            // إعادة تعيين النموذج
            $_POST = array();
        } else {
            $message = showAlert($result['message'], 'error');
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>إنشاء مستخدم جديد - مبسط</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <!-- شريط الحالة -->
        <div class="status-bar">
            <div class="status-left">
                <span><?php echo date('H:i'); ?></span>
            </div>
            <div class="status-right">
                <div class="signal-bars">
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                </div>
                <span>📶</span>
                <span>🔋</span>
                <span>100%</span>
            </div>
        </div>

        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <div class="header-back">
                    <a href="admin_dashboard.php" class="back-btn">←</a>
                </div>
                <h1>➕ إنشاء مستخدم جديد</h1>
                <div class="header-actions">
                    <a href="debug_form.php" class="header-btn">تشخيص</a>
                </div>
            </div>
        </header>

        <div class="page-content">
            <?php echo $message; ?>

            <!-- نموذج إنشاء المستخدم المبسط -->
            <div class="form-container">
                <div class="form-header">
                    <div class="form-icon">👤</div>
                    <h2 class="form-title">إنشاء مستخدم جديد</h2>
                    <p class="form-subtitle">نموذج مبسط لاختبار الوظيفة</p>
                </div>
                
                <form method="POST" class="form-body">
                    <div class="form-group">
                        <label class="form-label">اسم المستخدم <span class="required">*</span></label>
                        <input type="text" name="username" class="form-input" 
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                               placeholder="اسم المستخدم للدخول" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">كلمة المرور <span class="required">*</span></label>
                        <input type="password" name="password" class="form-input" 
                               placeholder="6 أحرف على الأقل" required minlength="6">
                    </div>

                    <div class="form-group">
                        <label class="form-label">الاسم الكامل <span class="required">*</span></label>
                        <input type="text" name="full_name" class="form-input" 
                               value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                               placeholder="الاسم الكامل للمستخدم" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">نوع المستخدم <span class="required">*</span></label>
                        <select name="user_type" class="form-input" required>
                            <option value="">اختر نوع المستخدم</option>
                            <option value="admin" <?php echo ($_POST['user_type'] ?? '') === 'admin' ? 'selected' : ''; ?>>
                                👑 مدير - صلاحيات كاملة
                            </option>
                            <option value="supervisor" <?php echo ($_POST['user_type'] ?? '') === 'supervisor' ? 'selected' : ''; ?>>
                                👨‍💼 مشرف - إدارة الدعوات والموظفين
                            </option>
                            <option value="employee" <?php echo ($_POST['user_type'] ?? '') === 'employee' ? 'selected' : ''; ?>>
                                📱 موظف - مسح الدعوات فقط
                            </option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="email" class="form-input" 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               placeholder="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label class="form-label">رقم الجوال</label>
                        <input type="tel" name="phone" class="form-input" 
                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                               placeholder="05xxxxxxxx">
                    </div>

                    <div class="form-actions">
                        <button type="submit" name="create_user" class="btn btn-success btn-xl">
                            ✅ إنشاء المستخدم
                        </button>
                        <a href="admin_dashboard.php" class="btn btn-flat">
                            ❌ إلغاء
                        </a>
                    </div>
                </form>
            </div>

            <!-- معلومات مفيدة -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ℹ️ معلومات</h3>
                </div>
                <div class="card-body">
                    <div class="app-list">
                        <div class="list-item">
                            <div class="list-icon primary">🔧</div>
                            <div class="list-content">
                                <div class="list-title">نموذج مبسط</div>
                                <div class="list-subtitle">هذا نموذج مبسط لاختبار وظيفة إنشاء المستخدمين</div>
                            </div>
                        </div>
                        <div class="list-item">
                            <div class="list-icon info">🧪</div>
                            <div class="list-content">
                                <div class="list-title">تشخيص المشاكل</div>
                                <div class="list-subtitle">استخدم صفحة التشخيص لمعرفة سبب المشكلة</div>
                            </div>
                        </div>
                        <div class="list-item">
                            <div class="list-icon success">✅</div>
                            <div class="list-content">
                                <div class="list-title">اختبار النجاح</div>
                                <div class="list-subtitle">إذا نجح هذا النموذج، فالمشكلة في النموذج المعقد</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- روابط مفيدة -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🔗 روابط مفيدة</h3>
                </div>
                <div class="card-body">
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                        <a href="debug_form.php" class="btn btn-warning">
                            🔧 صفحة التشخيص المفصلة
                        </a>
                        <a href="test_create_user.php" class="btn btn-info">
                            🧪 اختبار قاعدة البيانات
                        </a>
                        <a href="create_user.php" class="btn btn-primary">
                            📝 النموذج الأصلي المعقد
                        </a>
                        <a href="admin_dashboard.php" class="btn btn-secondary">
                            🏠 لوحة الإدارة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط التنقل السفلي -->
        <div class="tab-bar">
            <div class="tab-items">
                <a href="admin_dashboard.php" class="tab-item">
                    <span class="tab-icon">🏠</span>
                    <span class="tab-label">الرئيسية</span>
                </a>
                <a href="simple_create_user.php" class="tab-item active">
                    <span class="tab-icon">➕</span>
                    <span class="tab-label">مبسط</span>
                </a>
                <a href="debug_form.php" class="tab-item">
                    <span class="tab-icon">🔧</span>
                    <span class="tab-label">تشخيص</span>
                </a>
                <a href="create_user.php" class="tab-item">
                    <span class="tab-icon">📝</span>
                    <span class="tab-label">معقد</span>
                </a>
                <a href="logout.php" class="tab-item">
                    <span class="tab-icon">🚪</span>
                    <span class="tab-label">خروج</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        // تشخيص JavaScript بسيط
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل النموذج المبسط');
            
            const form = document.querySelector('form');
            const submitBtn = document.querySelector('button[type="submit"]');
            
            if (form && submitBtn) {
                console.log('تم العثور على النموذج والزر');
                
                form.addEventListener('submit', function(e) {
                    console.log('تم إرسال النموذج المبسط');
                    
                    // تأثير التحميل
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '⏳ جاري الإنشاء...';
                });
            } else {
                console.error('لم يتم العثور على النموذج أو الزر');
            }
        });
    </script>
</body>
</html>
