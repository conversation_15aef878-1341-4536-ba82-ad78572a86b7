<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
requireLogin();

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار إصلاح Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
        .test-box { background: white; border-radius: 10px; padding: 20px; margin: 15px 0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .success { border-left: 4px solid #28a745; background: #d4edda; color: #155724; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; color: #721c24; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; color: #856404; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; color: #0c5460; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 8px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>";

echo "<h1>🔧 اختبار إصلاح Dashboard</h1>";

echo "<div class='test-box info'>
        <h3>📋 معلومات الجلسة</h3>
        <p><strong>معرف المستخدم:</strong> " . ($_SESSION['user_id'] ?? 'غير محدد') . "</p>
        <p><strong>اسم المستخدم:</strong> " . ($_SESSION['username'] ?? 'غير محدد') . "</p>
        <p><strong>الاسم الكامل:</strong> " . ($_SESSION['full_name'] ?? 'غير محدد') . "</p>
        <p><strong>نوع المستخدم:</strong> " . ($_SESSION['user_type'] ?? 'غير محدد') . "</p>
        <p><strong>البريد الإلكتروني:</strong> " . ($_SESSION['email'] ?? 'غير محدد') . "</p>
      </div>";

echo "<h2>🧪 اختبار المتغيرات المطلوبة</h2>";

// اختبار 1: فحص المتغيرات المطلوبة
echo "<div class='test-box'>
        <h4>🔍 اختبار 1: فحص متغيرات الجلسة</h4>";

$requiredVars = ['user_id', 'username', 'user_type'];
$optionalVars = ['full_name', 'email', 'phone'];
$allGood = true;

echo "<h5>المتغيرات المطلوبة:</h5><ul>";
foreach ($requiredVars as $var) {
    if (isset($_SESSION[$var])) {
        echo "<li class='success'>✅ <strong>$var:</strong> " . htmlspecialchars($_SESSION[$var]) . "</li>";
    } else {
        echo "<li class='error'>❌ <strong>$var:</strong> غير موجود</li>";
        $allGood = false;
    }
}
echo "</ul>";

echo "<h5>المتغيرات الاختيارية:</h5><ul>";
foreach ($optionalVars as $var) {
    if (isset($_SESSION[$var]) && !empty($_SESSION[$var])) {
        echo "<li class='success'>✅ <strong>$var:</strong> " . htmlspecialchars($_SESSION[$var]) . "</li>";
    } else {
        echo "<li class='warning'>⚠️ <strong>$var:</strong> غير موجود أو فارغ</li>";
    }
}
echo "</ul>";

if ($allGood) {
    echo "<div class='success'>
            <h5>✅ جميع المتغيرات المطلوبة موجودة!</h5>
            <p>يمكن لـ dashboard.php العمل بشكل صحيح</p>
          </div>";
} else {
    echo "<div class='error'>
            <h5>❌ بعض المتغيرات المطلوبة مفقودة!</h5>
            <p>قد تحدث أخطاء في dashboard.php</p>
          </div>";
}

echo "</div>";

// اختبار 2: فحص المتغير المحدث
echo "<div class='test-box'>
        <h4>🔧 اختبار 2: المتغير المحدث في Dashboard</h4>";

$displayName = $_SESSION['full_name'] ?? $_SESSION['username'] ?? 'المستخدم';

echo "<div class='success'>
        <h5>✅ المتغير الجديد يعمل!</h5>
        <p><strong>النص المعروض:</strong> مرحباً، " . htmlspecialchars($displayName) . "</p>
        <p><strong>المنطق:</strong> يستخدم full_name إذا كان موجود، وإلا username، وإلا 'المستخدم'</p>
      </div>";

echo "<pre>// الكود المحدث في dashboard.php
\$_SESSION['full_name'] ?? \$_SESSION['username'] ?? 'المستخدم'

// بدلاً من الكود القديم الذي يسبب خطأ
\$_SESSION['admin_username'] // ❌ غير موجود</pre>";

echo "</div>";

// اختبار 3: فحص الصلاحيات
echo "<div class='test-box'>
        <h4>🔐 اختبار 3: فحص الصلاحيات</h4>";

if (isset($_SESSION['user_type'])) {
    $userType = $_SESSION['user_type'];
    
    switch ($userType) {
        case 'admin':
            echo "<div class='success'>
                    <h5>👑 مدير النظام</h5>
                    <p>لديك صلاحيات كاملة للوصول لجميع الصفحات</p>
                  </div>";
            break;
        case 'supervisor':
            echo "<div class='info'>
                    <h5>👨‍💼 مشرف</h5>
                    <p>لديك صلاحيات إدارة الدعوات الخاصة بك</p>
                  </div>";
            break;
        case 'employee':
            echo "<div class='warning'>
                    <h5>📱 موظف</h5>
                    <p>لديك صلاحيات مسح أكواد QR فقط</p>
                    <p><strong>ملاحظة:</strong> dashboard.php قد لا يكون مناسب لك</p>
                  </div>";
            break;
        default:
            echo "<div class='error'>
                    <h5>❓ نوع مستخدم غير معروف</h5>
                    <p><strong>النوع:</strong> $userType</p>
                  </div>";
    }
} else {
    echo "<div class='error'>
            <h5>❌ نوع المستخدم غير محدد</h5>
            <p>لا يمكن تحديد الصلاحيات</p>
          </div>";
}

echo "</div>";

// اختبار 4: اختبار الوصول للصفحات
echo "<div class='test-box'>
        <h4>🌐 اختبار 4: روابط الصفحات المناسبة</h4>";

if (isset($_SESSION['user_type'])) {
    $userType = $_SESSION['user_type'];
    
    echo "<h5>الصفحات المناسبة لك:</h5><ul>";
    
    switch ($userType) {
        case 'admin':
            echo "<li><a href='admin_dashboard.php' class='btn btn-primary'>👑 لوحة تحكم الإدارة</a></li>";
            echo "<li><a href='supervisor_dashboard.php' class='btn btn-success'>👨‍💼 لوحة تحكم المشرف</a></li>";
            echo "<li><a href='employee_dashboard.php' class='btn btn-warning'>📱 لوحة تحكم الموظف</a></li>";
            echo "<li><a href='dashboard.php' class='btn btn-info'>🎫 لوحة التحكم الكلاسيكية</a></li>";
            break;
        case 'supervisor':
            echo "<li><a href='supervisor_dashboard.php' class='btn btn-primary'>👨‍💼 لوحة تحكم المشرف</a></li>";
            echo "<li><a href='dashboard.php' class='btn btn-info'>🎫 لوحة التحكم الكلاسيكية</a></li>";
            break;
        case 'employee':
            echo "<li><a href='employee_dashboard.php' class='btn btn-primary'>📱 لوحة تحكم الموظف</a></li>";
            break;
    }
    
    echo "</ul>";
} else {
    echo "<p>لا يمكن تحديد الصفحات المناسبة</p>";
}

echo "</div>";

echo "<div class='test-box info'>
        <h3>🎯 النتيجة النهائية</h3>
        <p>إذا ظهرت جميع الاختبارات بعلامة ✅ فإن مشكلة dashboard.php تم حلها بنجاح!</p>
        <p>الآن يمكن الوصول للوحة التحكم بدون أخطاء undefined array key.</p>
        <br>
        <a href='dashboard.php' class='btn btn-primary'>🎫 اختبر Dashboard الآن</a>
        <a href='login.php' class='btn btn-warning'>🔐 صفحة تسجيل الدخول</a>
      </div>";

echo "</body></html>";
?>
