<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
requireLogin();

$pdo = getDBConnection();
$message = '';
$scanResult = null;

// معالجة مسح الكود
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['scan_code'])) {
    $code = sanitizeInput($_POST['code'] ?? '');
    
    if (empty($code)) {
        $message = showAlert('يرجى إدخال كود الدعوة', 'error');
    } else {
        try {
            // البحث عن الضيف بالكود
            $stmt = $pdo->prepare("SELECT * FROM guests WHERE code = ?");
            $stmt->execute([$code]);
            $guest = $stmt->fetch();
            
            if (!$guest) {
                $scanResult = [
                    'status' => 'error',
                    'message' => 'كود الدعوة غير صالح أو غير موجود',
                    'code' => $code
                ];
            } elseif ($guest['scanned'] == 1) {
                $scanResult = [
                    'status' => 'warning',
                    'message' => 'تم استخدام هذا الكود من قبل',
                    'guest' => $guest,
                    'code' => $code
                ];
            } else {
                // تسجيل الحضور
                $stmt = $pdo->prepare("UPDATE guests SET scanned = 1, scanned_at = NOW(), scanned_by = ? WHERE id = ?");
                $stmt->execute([$_SESSION['user_id'], $guest['id']]);
                
                logActivity('scan_guest', 'guest', $guest['id'], "تم تسجيل حضور الضيف: {$guest['name']}");
                
                $scanResult = [
                    'status' => 'success',
                    'message' => 'تم تسجيل الحضور بنجاح',
                    'guest' => $guest,
                    'code' => $code
                ];
            }
            
        } catch (PDOException $e) {
            $scanResult = [
                'status' => 'error',
                'message' => 'حدث خطأ في النظام، يرجى المحاولة مرة أخرى',
                'code' => $code
            ];
        }
    }
}

// جلب إحصائيات الموظف
try {
    $stats = [
        'total_scanned_today' => 0,
        'total_scanned_by_me' => 0,
        'total_guests' => 0,
        'total_scanned' => 0
    ];
    
    // إجمالي ما مسحه هذا الموظف اليوم
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM guests 
        WHERE scanned_by = ? AND DATE(scanned_at) = CURDATE()
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats['total_scanned_today'] = $stmt->fetchColumn();
    
    // إجمالي ما مسحه هذا الموظف
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE scanned_by = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $stats['total_scanned_by_me'] = $stmt->fetchColumn();
    
    // إحصائيات عامة
    $stats['total_guests'] = $pdo->query("SELECT COUNT(*) FROM guests")->fetchColumn();
    $stats['total_scanned'] = $pdo->query("SELECT COUNT(*) FROM guests WHERE scanned = 1")->fetchColumn();
    
} catch (PDOException $e) {
    $stats = array_fill_keys(['total_scanned_today', 'total_scanned_by_me', 'total_guests', 'total_scanned'], 0);
}

// جلب آخر عمليات المسح
try {
    $stmt = $pdo->prepare("
        SELECT g.*, u.full_name as scanned_by_name 
        FROM guests g 
        LEFT JOIN users u ON g.scanned_by = u.id 
        WHERE g.scanned_by = ? 
        ORDER BY g.scanned_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $recentScans = $stmt->fetchAll();
} catch (PDOException $e) {
    $recentScans = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الموظف - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .scanner-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .scanner-input {
            font-size: 24px;
            padding: 15px;
            border: 3px solid #007bff;
            border-radius: 10px;
            width: 100%;
            max-width: 400px;
            text-align: center;
            font-family: monospace;
            margin: 20px 0;
        }
        
        .scanner-input:focus {
            outline: none;
            border-color: #0056b3;
            box-shadow: 0 0 10px rgba(0,123,255,0.3);
        }
        
        .scan-result {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            font-size: 18px;
        }
        
        .scan-result.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .scan-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .scan-result.warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .guest-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            text-align: right;
        }
        
        .scan-button {
            font-size: 20px;
            padding: 15px 30px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .scan-button:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .recent-scans {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .scan-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .scan-item:last-child {
            border-bottom: none;
        }
        
        .scan-time {
            color: #666;
            font-size: 12px;
        }
        
        @media (max-width: 768px) {
            .scanner-input {
                font-size: 18px;
                padding: 12px;
            }
            
            .scan-button {
                font-size: 16px;
                padding: 12px 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>📱 لوحة تحكم الموظف</h1>
                <div class="header-actions">
                    <span>مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                    <a href="logout.php" class="btn btn-secondary">تسجيل الخروج</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <!-- الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card success">
                <div class="stat-icon">📅</div>
                <div class="stat-info">
                    <h3><?php echo $stats['total_scanned_today']; ?></h3>
                    <p>مسحت اليوم</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-info">
                    <h3><?php echo $stats['total_scanned_by_me']; ?></h3>
                    <p>إجمالي مسحي</p>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">✅</div>
                <div class="stat-info">
                    <h3><?php echo $stats['total_scanned']; ?></h3>
                    <p>إجمالي الحضور</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-info">
                    <h3><?php echo $stats['total_guests']; ?></h3>
                    <p>إجمالي المدعوين</p>
                </div>
            </div>
        </div>

        <!-- ماسح الباركود -->
        <div class="scanner-container">
            <h2>📱 ماسح أكواد الحضور</h2>
            <p>امسح كود QR أو أدخل كود الدعوة يدوياً</p>
            
            <form method="POST" id="scanForm">
                <input type="text" 
                       name="code" 
                       id="codeInput" 
                       class="scanner-input" 
                       placeholder="امسح الكود أو اكتبه هنا..."
                       autocomplete="off"
                       autofocus>
                
                <br>
                
                <button type="submit" name="scan_code" class="scan-button">
                    ✅ تسجيل الحضور
                </button>
            </form>
            
            <!-- نتيجة المسح -->
            <?php if ($scanResult): ?>
                <div class="scan-result <?php echo $scanResult['status']; ?>">
                    <div style="font-size: 24px; margin-bottom: 10px;">
                        <?php
                        switch ($scanResult['status']) {
                            case 'success':
                                echo '🎉 ' . $scanResult['message'];
                                break;
                            case 'warning':
                                echo '⚠️ ' . $scanResult['message'];
                                break;
                            case 'error':
                                echo '❌ ' . $scanResult['message'];
                                break;
                        }
                        ?>
                    </div>
                    
                    <?php if (isset($scanResult['guest'])): ?>
                        <div class="guest-info">
                            <h4>معلومات الضيف:</h4>
                            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($scanResult['guest']['name']); ?></p>
                            <?php if (!empty($scanResult['guest']['phone'])): ?>
                                <p><strong>الجوال:</strong> <?php echo htmlspecialchars($scanResult['guest']['phone']); ?></p>
                            <?php endif; ?>
                            <p><strong>كود الدعوة:</strong> <?php echo htmlspecialchars($scanResult['guest']['code']); ?></p>
                            <?php if ($scanResult['guest']['scanned_at']): ?>
                                <p><strong>وقت الحضور:</strong> <?php echo date('Y-m-d H:i:s', strtotime($scanResult['guest']['scanned_at'])); ?></p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- آخر عمليات المسح -->
        <?php if (!empty($recentScans)): ?>
            <div class="recent-scans">
                <h3>📋 آخر عمليات المسح</h3>
                
                <?php foreach ($recentScans as $scan): ?>
                    <div class="scan-item">
                        <div>
                            <strong><?php echo htmlspecialchars($scan['name']); ?></strong>
                            <br>
                            <small>كود: <?php echo htmlspecialchars($scan['code']); ?></small>
                        </div>
                        <div class="scan-time">
                            <?php echo date('Y-m-d H:i', strtotime($scan['scanned_at'])); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // تركيز تلقائي على حقل الإدخال
        document.getElementById('codeInput').focus();
        
        // مسح الحقل بعد الإرسال الناجح
        <?php if ($scanResult && $scanResult['status'] === 'success'): ?>
            document.getElementById('codeInput').value = '';
            document.getElementById('codeInput').focus();
        <?php endif; ?>
        
        // إرسال تلقائي عند إدخال كود كامل (افتراض أن الكود 20 حرف)
        document.getElementById('codeInput').addEventListener('input', function(e) {
            const value = e.target.value.trim();
            
            // إذا كان الكود يبدأ بـ INV_ ويحتوي على 20 حرف أو أكثر، أرسل تلقائياً
            if (value.startsWith('INV_') && value.length >= 20) {
                setTimeout(() => {
                    document.getElementById('scanForm').submit();
                }, 500);
            }
        });
        
        // منع إرسال النموذج إذا كان الحقل فارغ
        document.getElementById('scanForm').addEventListener('submit', function(e) {
            const code = document.getElementById('codeInput').value.trim();
            if (!code) {
                e.preventDefault();
                alert('يرجى إدخال كود الدعوة');
                document.getElementById('codeInput').focus();
            }
        });
        
        // تحديث الصفحة كل 30 ثانية للحصول على إحصائيات محدثة
        setInterval(function() {
            // تحديث الإحصائيات فقط إذا لم يكن هناك نشاط حديث
            const lastActivity = Date.now() - (window.lastInputTime || 0);
            if (lastActivity > 30000) { // 30 ثانية
                location.reload();
            }
        }, 30000);
        
        // تتبع آخر نشاط
        document.getElementById('codeInput').addEventListener('input', function() {
            window.lastInputTime = Date.now();
        });
    </script>
</body>
</html>
