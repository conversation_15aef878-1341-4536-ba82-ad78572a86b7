<?php
require_once 'config.php';

// التحقق من تسجيل الدخول
requireLogin();

$pdo = getDBConnection();

// التأكد من وجود معرف المشرف في الجلسة
if (!isset($_SESSION['created_by'])) {
    // جلب معرف المشرف من قاعدة البيانات
    try {
        $stmt = $pdo->prepare("SELECT created_by FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
        if ($user) {
            $_SESSION['created_by'] = $user['created_by'];
        } else {
            $_SESSION['created_by'] = null;
        }
    } catch (PDOException $e) {
        $_SESSION['created_by'] = null;
    }
}

$message = '';
$scanResult = null;

/**
 * استخراج كود الدعوة من النص المدخل
 * يدعم الكود المباشر والروابط
 */
function extractCodeFromInput($input) {
    $input = trim($input);

    // إذا كان رابطاً، استخرج الكود من المعامل
    if (strpos($input, 'verify.php?code=') !== false) {
        // استخراج الكود من الرابط
        $parsed = parse_url($input);
        if (isset($parsed['query'])) {
            parse_str($parsed['query'], $params);
            if (isset($params['code'])) {
                return $params['code'];
            }
        }
    }

    // إذا كان رابطاً بصيغة أخرى، حاول استخراج الكود
    if (preg_match('/code=([^&\s]+)/', $input, $matches)) {
        return $matches[1];
    }

    // إذا كان كود مباشر، أرجعه كما هو
    return $input;
}

// معالجة مسح الكود
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['scan_code'])) {
    $rawCode = sanitizeInput($_POST['code'] ?? '');

    if (empty($rawCode)) {
        $message = showAlert('يرجى إدخال كود الدعوة', 'error');
    } else {
        try {
            // استخراج الكود من الرابط إذا كان رابطاً
            $code = extractCodeFromInput($rawCode);

            // البحث عن الضيف بالكود مع التحقق من صلاحيات الموظف
            // الموظف يمكنه فقط مسح الدعوات المصدرة من المشرف الذي أنشأه
            $employeeCreatedBy = isset($_SESSION['created_by']) ? $_SESSION['created_by'] : null; // المشرف الذي أنشأ الموظف

            if ($employeeCreatedBy) {
                // البحث عن الضيف مع التأكد أنه من نفس المشرف
                $stmt = $pdo->prepare("
                    SELECT g.*, u.full_name as creator_name
                    FROM guests g
                    LEFT JOIN users u ON g.created_by = u.id
                    WHERE g.code = ? AND g.created_by = ?
                ");
                $stmt->execute([$code, $employeeCreatedBy]);
                $guest = $stmt->fetch();

                // إذا لم يجد الضيف، تحقق من وجوده في النظام لإظهار رسالة مناسبة
                if (!$guest) {
                    $stmt = $pdo->prepare("
                        SELECT g.*, u.full_name as creator_name
                        FROM guests g
                        LEFT JOIN users u ON g.created_by = u.id
                        WHERE g.code = ?
                    ");
                    $stmt->execute([$code]);
                    $guestExists = $stmt->fetch();

                    if ($guestExists) {
                        // الضيف موجود لكن من مشرف آخر
                        $scanResult = [
                            'status' => 'error',
                            'message' => 'هذه الدعوة مصدرة من مشرف آخر ولا يمكنك مسحها',
                            'code' => $code,
                            'guest_info' => [
                                'name' => $guestExists['name'],
                                'creator' => $guestExists['creator_name'] ?: 'غير محدد'
                            ]
                        ];
                    } else {
                        // الضيف غير موجود في النظام
                        $scanResult = [
                            'status' => 'error',
                            'message' => 'كود الدعوة غير صالح أو غير موجود',
                            'code' => $code,
                            'raw_input' => $rawCode
                        ];
                    }
                }
            } else {
                // الموظف لم ينشأ من قبل مشرف (حالة خاصة)
                $stmt = $pdo->prepare("SELECT * FROM guests WHERE code = ?");
                $stmt->execute([$code]);
                $guest = $stmt->fetch();
            }
            
            if (!$guest) {
                // التحقق من سبب عدم وجود الضيف
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests");
                $stmt->execute();
                $totalGuests = $stmt->fetchColumn();

                $scanResult = [
                    'status' => 'error',
                    'message' => 'كود الدعوة غير صالح أو غير موجود',
                    'code' => $code,
                    'raw_input' => $rawCode,
                    'debug_info' => "إجمالي الدعوات في النظام: $totalGuests"
                ];
            } elseif ($guest['scanned'] == 1) {
                $scanResult = [
                    'status' => 'warning',
                    'message' => 'تم استخدام هذا الكود من قبل',
                    'guest' => $guest,
                    'code' => $code
                ];
            } else {
                // تسجيل الحضور
                $stmt = $pdo->prepare("UPDATE guests SET scanned = 1, scanned_at = NOW(), scanned_by = ? WHERE id = ?");
                $updateResult = $stmt->execute([$_SESSION['user_id'], $guest['id']]);

                if ($updateResult) {
                    // تسجيل العملية في سجل الأنشطة
                    try {
                        $creatorName = $guest['creator_name'] ?: 'غير محدد';
                        logActivity('scan_guest', 'guest', $guest['id'], "تم تسجيل حضور الضيف: {$guest['name']} (دعوة من: {$creatorName})");
                    } catch (Exception $e) {
                        // تجاهل خطأ التسجيل
                    }

                    $scanResult = [
                        'status' => 'success',
                        'message' => 'تم تسجيل الحضور بنجاح',
                        'guest' => $guest,
                        'code' => $code,
                        'scanned_at' => date('Y-m-d H:i:s'),
                        'scanned_by' => $_SESSION['full_name']
                    ];
                } else {
                    $scanResult = [
                        'status' => 'error',
                        'message' => 'فشل في تحديث قاعدة البيانات',
                        'code' => $code
                    ];
                }
            }
            
        } catch (PDOException $e) {
            // تسجيل الخطأ للمطور
            error_log("Database error in employee scan: " . $e->getMessage());

            $scanResult = [
                'status' => 'error',
                'message' => 'حدث خطأ في النظام، يرجى المحاولة مرة أخرى',
                'code' => $code,
                'technical_error' => $e->getMessage() // للمطور فقط
            ];
        }
    }
}

// جلب إحصائيات الموظف (مقتصرة على دعوات المشرف الذي أنشأه)
try {
    $stats = [
        'total_scanned_today' => 0,
        'total_scanned_by_me' => 0,
        'total_guests' => 0,
        'total_scanned' => 0
    ];

    $employeeCreatedBy = isset($_SESSION['created_by']) ? $_SESSION['created_by'] : null; // المشرف الذي أنشأ الموظف

    if ($employeeCreatedBy) {
        // إجمالي ما مسحه هذا الموظف اليوم (من دعوات نفس المشرف)
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM guests
            WHERE scanned_by = ? AND DATE(scanned_at) = CURDATE() AND guests.created_by = ?
        ");
        $stmt->execute([$_SESSION['user_id'], $employeeCreatedBy]);
        $stats['total_scanned_today'] = $stmt->fetchColumn();

        // إجمالي ما مسحه هذا الموظف (من دعوات نفس المشرف)
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM guests
            WHERE scanned_by = ? AND guests.created_by = ?
        ");
        $stmt->execute([$_SESSION['user_id'], $employeeCreatedBy]);
        $stats['total_scanned_by_me'] = $stmt->fetchColumn();

        // إحصائيات دعوات المشرف
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE guests.created_by = ?");
        $stmt->execute([$employeeCreatedBy]);
        $stats['total_guests'] = $stmt->fetchColumn();

        $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE scanned = 1 AND guests.created_by = ?");
        $stmt->execute([$employeeCreatedBy]);
        $stats['total_scanned'] = $stmt->fetchColumn();
    } else {
        // إذا لم يكن للموظف مشرف محدد، عرض الإحصائيات العامة
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM guests
            WHERE scanned_by = ? AND DATE(scanned_at) = CURDATE()
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $stats['total_scanned_today'] = $stmt->fetchColumn();

        $stmt = $pdo->prepare("SELECT COUNT(*) FROM guests WHERE scanned_by = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $stats['total_scanned_by_me'] = $stmt->fetchColumn();

        $stats['total_guests'] = $pdo->query("SELECT COUNT(*) FROM guests")->fetchColumn();
        $stats['total_scanned'] = $pdo->query("SELECT COUNT(*) FROM guests WHERE scanned = 1")->fetchColumn();
    }

} catch (PDOException $e) {
    $stats = array_fill_keys(['total_scanned_today', 'total_scanned_by_me', 'total_guests', 'total_scanned'], 0);
}

// جلب آخر عمليات المسح (فقط الدعوات من نفس المشرف)
try {
    $employeeCreatedBy = isset($_SESSION['created_by']) ? $_SESSION['created_by'] : null; // المشرف الذي أنشأ الموظف

    if ($employeeCreatedBy) {
        // عرض فقط الدعوات من نفس المشرف
        $stmt = $pdo->prepare("
            SELECT g.id, g.name, g.phone, g.code, g.scanned, g.scanned_at, g.created_at,
                   u.full_name as scanned_by_name, creator.full_name as creator_name
            FROM guests g
            LEFT JOIN users u ON g.scanned_by = u.id
            LEFT JOIN users creator ON g.created_by = creator.id
            WHERE g.scanned_by = ? AND g.created_by = ?
            ORDER BY g.scanned_at DESC
            LIMIT 10
        ");
        $stmt->execute([$_SESSION['user_id'], $employeeCreatedBy]);
    } else {
        // إذا لم يكن للموظف مشرف محدد، عرض جميع عمليات المسح
        $stmt = $pdo->prepare("
            SELECT g.id, g.name, g.phone, g.code, g.scanned, g.scanned_at, g.created_at,
                   u.full_name as scanned_by_name, creator.full_name as creator_name
            FROM guests g
            LEFT JOIN users u ON g.scanned_by = u.id
            LEFT JOIN users creator ON g.created_by = creator.id
            WHERE g.scanned_by = ?
            ORDER BY g.scanned_at DESC
            LIMIT 10
        ");
        $stmt->execute([$_SESSION['user_id']]);
    }

    $recentScans = $stmt->fetchAll();
} catch (PDOException $e) {
    $recentScans = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الموظف - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
    <!-- مكتبات مسح الباركود -->
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
    <script src="assets/qr-scanner.js"></script>
    <style>
        .scanner-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .scanner-input {
            font-size: 24px;
            padding: 15px;
            border: 3px solid #007bff;
            border-radius: 10px;
            width: 100%;
            max-width: 400px;
            text-align: center;
            font-family: monospace;
            margin: 20px 0;
        }
        
        .scanner-input:focus {
            outline: none;
            border-color: #0056b3;
            box-shadow: 0 0 10px rgba(0,123,255,0.3);
        }
        
        .scan-result {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            font-size: 18px;
        }
        
        .scan-result.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .scan-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .scan-result.warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .guest-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            text-align: right;
        }
        
        .scan-button {
            font-size: 20px;
            padding: 15px 30px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .scan-button:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .recent-scans {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .scan-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .scan-item:last-child {
            border-bottom: none;
        }
        
        .scan-time {
            color: #666;
            font-size: 12px;
        }

        /* مساعدة الإدخال */
        .input-help {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-top: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 3px solid #17a2b8;
        }

        /* حالة الكود */
        #codeStatus {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* تحسينات زر الإرسال */
        .scan-button.ready {
            background: #28a745 !important;
            transform: scale(1.05) !important;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4) !important;
            animation: glow 2s infinite;
        }

        @keyframes glow {
            0% { box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4); }
            50% { box-shadow: 0 4px 20px rgba(40, 167, 69, 0.6); }
            100% { box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4); }
        }

        /* رسائل النتائج */
        .scan-result-container {
            margin: 20px 0;
        }

        .alert {
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
            border: 2px solid;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #28a745;
            color: #155724;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffc107;
            color: #856404;
        }

        .alert-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-color: #dc3545;
            color: #721c24;
        }

        .alert h3 {
            margin: 0 0 15px 0;
            font-size: 18px;
        }

        .alert code {
            background: rgba(0,0,0,0.1);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }

        /* أدوات الكاميرا */
        .camera-controls {
            margin: 20px 0;
            text-align: center;
        }

        .camera-controls .btn {
            margin: 5px;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        #cameraContainer {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px dashed #007bff;
        }

        #scanner {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            background: #000;
        }

        #scanner video {
            width: 100%;
            height: auto;
            border-radius: 10px;
        }

        #scanner canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .scanner-status {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            background: #e3f2fd;
            border-radius: 8px;
            color: #1976d2;
            font-weight: 500;
        }

        .scanner-status.scanning {
            background: #fff3e0;
            color: #f57c00;
        }

        .scanner-status.success {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .scanner-status.error {
            background: #ffebee;
            color: #c62828;
        }

        /* تأثيرات المسح */
        .scan-line {
            position: absolute;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00ff00, transparent);
            animation: scan 2s linear infinite;
        }

        @keyframes scan {
            0% { top: 0%; }
            100% { top: 100%; }
        }

        /* إطار المسح */
        .scan-frame {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 250px;
            height: 250px;
            border: 2px solid #00ff00;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        }

        .scan-frame::before,
        .scan-frame::after {
            content: '';
            position: absolute;
            width: 30px;
            height: 30px;
            border: 3px solid #00ff00;
        }

        .scan-frame::before {
            top: -3px;
            left: -3px;
            border-right: none;
            border-bottom: none;
        }

        .scan-frame::after {
            bottom: -3px;
            right: -3px;
            border-left: none;
            border-top: none;
        }

        @media (max-width: 768px) {
            .scanner-input {
                font-size: 18px;
                padding: 12px;
            }

            .scan-button {
                font-size: 16px;
                padding: 12px 24px;
            }

            .camera-controls .btn {
                padding: 8px 16px;
                font-size: 12px;
                margin: 3px;
            }

            .scan-frame {
                width: 200px;
                height: 200px;
            }
        }

        /* تأثير النبض لجذب الانتباه */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>📱 لوحة تحكم الموظف</h1>
                <div class="header-actions">
                    <span>مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                    <a href="logout.php" class="btn btn-secondary">تسجيل الخروج</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <!-- الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card success">
                <div class="stat-icon">📅</div>
                <div class="stat-info">
                    <h3><?php echo $stats['total_scanned_today']; ?></h3>
                    <p>مسحت اليوم</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-info">
                    <h3><?php echo $stats['total_scanned_by_me']; ?></h3>
                    <p>إجمالي مسحي</p>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">✅</div>
                <div class="stat-info">
                    <h3><?php echo $stats['total_scanned']; ?></h3>
                    <p>إجمالي الحضور</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-info">
                    <h3><?php echo $stats['total_guests']; ?></h3>
                    <p>إجمالي المدعوين</p>
                </div>
            </div>
        </div>

        <!-- نتيجة المسح -->
        <?php if ($scanResult): ?>
            <div class="scan-result-container" id="scanResultContainer" style="margin-bottom: 20px;">
                <?php if ($scanResult['status'] === 'success'): ?>
                    <div class="alert alert-success" style="position: relative;">
                        <button type="button" onclick="hideScanResult()" style="position: absolute; top: 10px; left: 10px; background: none; border: none; font-size: 20px; cursor: pointer; color: #155724;">×</button>
                        <h3>✅ تم تسجيل الحضور بنجاح!</h3>
                        <div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <strong>📋 بيانات الضيف:</strong><br>
                            <strong>الاسم:</strong> <?php echo htmlspecialchars($scanResult['guest']['name']); ?><br>
                            <?php if (!empty($scanResult['guest']['phone'])): ?>
                                <strong>الهاتف:</strong> <?php echo htmlspecialchars($scanResult['guest']['phone']); ?><br>
                            <?php endif; ?>
                            <?php if (isset($scanResult['guest']['creator_name'])): ?>
                                <strong>الدعوة من:</strong> <?php echo htmlspecialchars($scanResult['guest']['creator_name']); ?><br>
                            <?php endif; ?>
                            <strong>وقت الحضور:</strong> <?php echo $scanResult['scanned_at'] ?? 'الآن'; ?><br>
                            <strong>مسجل بواسطة:</strong> <?php echo $scanResult['scanned_by'] ?? $_SESSION['full_name']; ?>
                        </div>
                    </div>
                <?php elseif ($scanResult['status'] === 'warning'): ?>
                    <div class="alert alert-warning" style="position: relative;">
                        <button type="button" onclick="hideScanResult()" style="position: absolute; top: 10px; left: 10px; background: none; border: none; font-size: 20px; cursor: pointer; color: #856404;">×</button>
                        <h3>⚠️ تم استخدام هذا الكود من قبل</h3>
                        <div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <strong>📋 بيانات الضيف:</strong><br>
                            <strong>الاسم:</strong> <?php echo htmlspecialchars($scanResult['guest']['name']); ?><br>
                            <?php if (isset($scanResult['guest']['creator_name'])): ?>
                                <strong>الدعوة من:</strong> <?php echo htmlspecialchars($scanResult['guest']['creator_name']); ?><br>
                            <?php endif; ?>
                            <strong>تم تسجيل الحضور في:</strong> <?php echo $scanResult['guest']['scanned_at']; ?><br>
                            <strong>الكود:</strong> <code><?php echo htmlspecialchars($scanResult['code']); ?></code>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-error" style="position: relative;">
                        <button type="button" onclick="hideScanResult()" style="position: absolute; top: 10px; left: 10px; background: none; border: none; font-size: 20px; cursor: pointer; color: #721c24;">×</button>
                        <h3>❌ فشل في تسجيل الحضور</h3>
                        <p><strong>السبب:</strong> <?php echo htmlspecialchars($scanResult['message']); ?></p>

                        <?php if (isset($scanResult['guest_info'])): ?>
                            <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
                                <strong>📋 معلومات الدعوة:</strong><br>
                                <strong>اسم الضيف:</strong> <?php echo htmlspecialchars($scanResult['guest_info']['name']); ?><br>
                                <strong>الدعوة مصدرة من:</strong> <?php echo htmlspecialchars($scanResult['guest_info']['creator']); ?><br>
                                <strong>الكود:</strong> <code><?php echo htmlspecialchars($scanResult['code']); ?></code>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($scanResult['raw_input']) && isset($scanResult['code'])): ?>
                            <?php if ($scanResult['raw_input'] !== $scanResult['code']): ?>
                                <p><strong>النص المدخل:</strong> <code><?php echo htmlspecialchars($scanResult['raw_input']); ?></code></p>
                                <p><strong>الكود المستخرج:</strong> <code><?php echo htmlspecialchars($scanResult['code']); ?></code></p>
                            <?php else: ?>
                                <p><strong>الكود المدخل:</strong> <code><?php echo htmlspecialchars($scanResult['code']); ?></code></p>
                            <?php endif; ?>
                        <?php elseif (isset($scanResult['code'])): ?>
                            <p><strong>الكود المدخل:</strong> <code><?php echo htmlspecialchars($scanResult['code']); ?></code></p>
                        <?php endif; ?>

                        <?php if (isset($scanResult['debug_info'])): ?>
                            <div style="margin-top: 15px; padding: 10px; background: rgba(0,0,0,0.1); border-radius: 5px; font-size: 12px;">
                                <strong>معلومات التشخيص:</strong><br>
                                <?php echo htmlspecialchars($scanResult['debug_info']); ?>
                            </div>
                        <?php endif; ?>

                        <div style="margin-top: 15px; padding: 10px; background: rgba(0,0,0,0.1); border-radius: 5px;">
                            <strong>💡 اقتراحات للحل:</strong><br>
                            • تأكد من صحة رقم الإجازة المدخل<br>
                            • تحقق من أن رقم الإجازة يبدأ بـ INV_<br>
                            • جرب مسح QR Code بالكاميرا لاستخراج الرقم تلقائياً
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- ماسح الباركود -->
        <div class="scanner-container">
            <h2>📱 ماسح أكواد الحضور</h2>
            <p>امسح كود QR بالكاميرا أو أدخل رقم الإجازة يدوياً، ثم اضغط "تسجيل الحضور"</p>

            <!-- أزرار التحكم في الكاميرا -->
            <div class="camera-controls">
                <button type="button" id="startCamera" class="btn btn-success">
                    📷 تشغيل الكاميرا
                </button>
                <button type="button" id="stopCamera" class="btn btn-danger" style="display: none;">
                    ⏹️ إيقاف الكاميرا
                </button>
                <button type="button" id="switchCamera" class="btn btn-info" style="display: none;">
                    🔄 تبديل الكاميرا
                </button>
            </div>

            <!-- منطقة الكاميرا -->
            <div id="cameraContainer" style="display: none;">
                <div id="scanner" style="width: 100%; max-width: 400px; margin: 20px auto;"></div>
                <div id="scannerStatus" class="scanner-status">
                    🔍 وجه الكاميرا نحو كود QR...
                </div>
            </div>

            <!-- نموذج الإدخال اليدوي -->
            <form method="POST" id="scanForm">
                <input type="hidden" name="scan_code" value="1">
                <div style="position: relative;">
                    <input type="text"
                           name="code"
                           id="codeInput"
                           class="scanner-input"
                           placeholder="اكتب رقم الإجازة أو امسح QR Code..."
                           autocomplete="off">

                    <!-- مؤشر حالة الكود -->
                    <div id="codeStatus" style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); font-size: 24px; display: none;">
                        ✅
                    </div>
                </div>

                <div id="inputHelp" class="input-help">
                    💡 امسح QR Code أو اكتب رقم الإجازة (INV_...)، ثم اضغط "تسجيل الحضور"
                </div>

                <!-- معاينة الكود -->
                <div id="codePreview" style="display: none; margin-top: 10px; padding: 10px; background: #e9ecef; border-radius: 5px; font-family: monospace; font-size: 14px; text-align: center; border: 1px dashed #6c757d;">
                    <strong>معاينة الكود:</strong><br>
                    <span id="codePreviewText"></span>
                </div>

                <br>

                <button type="submit" name="scan_code" class="scan-button" id="submitButton">
                    ✅ تسجيل الحضور
                </button>
            </form>
            
            <!-- نتيجة المسح -->
            <?php if ($scanResult): ?>
                <div class="scan-result <?php echo $scanResult['status']; ?>">
                    <div style="font-size: 24px; margin-bottom: 10px;">
                        <?php
                        switch ($scanResult['status']) {
                            case 'success':
                                echo '🎉 ' . $scanResult['message'];
                                break;
                            case 'warning':
                                echo '⚠️ ' . $scanResult['message'];
                                break;
                            case 'error':
                                echo '❌ ' . $scanResult['message'];
                                break;
                        }
                        ?>
                    </div>
                    
                    <?php if (isset($scanResult['guest'])): ?>
                        <div class="guest-info">
                            <h4>معلومات الضيف:</h4>
                            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($scanResult['guest']['name']); ?></p>
                            <?php if (!empty($scanResult['guest']['phone'])): ?>
                                <p><strong>الجوال:</strong> <?php echo htmlspecialchars($scanResult['guest']['phone']); ?></p>
                            <?php endif; ?>
                            <p><strong>كود الدعوة:</strong> <?php echo htmlspecialchars($scanResult['guest']['code']); ?></p>
                            <?php if ($scanResult['guest']['scanned_at']): ?>
                                <p><strong>وقت الحضور:</strong> <?php echo date('Y-m-d H:i:s', strtotime($scanResult['guest']['scanned_at'])); ?></p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- آخر عمليات المسح -->
        <?php if (!empty($recentScans)): ?>
            <div class="recent-scans">
                <h3>📋 آخر عمليات المسح</h3>
                
                <?php foreach ($recentScans as $scan): ?>
                    <div class="scan-item">
                        <div>
                            <strong><?php echo htmlspecialchars($scan['name']); ?></strong>
                            <br>
                            <small>كود: <?php echo htmlspecialchars($scan['code']); ?></small>
                            <?php if (!empty($scan['creator_name'])): ?>
                                <br>
                                <small style="color: #666;">دعوة من: <?php echo htmlspecialchars($scan['creator_name']); ?></small>
                            <?php endif; ?>
                        </div>
                        <div class="scan-time">
                            <?php echo date('Y-m-d H:i', strtotime($scan['scanned_at'])); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // متغيرات الماسح
        let qrScanner = null;
        let isScanning = false;

        // وظيفة استخراج الكود من البيانات الممسوحة
        function extractCodeFromScannedData(scannedData) {
            const data = scannedData.trim();

            // إذا كان رابطاً، استخرج الكود من المعامل
            if (data.includes('verify.php?code=')) {
                const match = data.match(/code=([^&\s]+)/);
                if (match) {
                    return match[1];
                }
            }

            // إذا كان رابطاً بصيغة أخرى، حاول استخراج الكود
            if (data.includes('code=')) {
                const match = data.match(/code=([^&\s]+)/);
                if (match) {
                    return match[1];
                }
            }

            // إذا كان كود مباشر، أرجعه كما هو
            return data;
        }

        // تركيز تلقائي على حقل الإدخال
        document.getElementById('codeInput').focus();

        // تسجيل حالة الصفحة عند التحميل
        console.log('📄 تحميل صفحة الموظف');

        // مسح الحقل فقط بعد التسجيل الناجح
        <?php if ($scanResult): ?>
            console.log('📊 نتيجة المسح:', <?php echo json_encode($scanResult); ?>);
        <?php endif; ?>

        <?php if ($scanResult && $scanResult['status'] === 'success'): ?>
            console.log('✅ نجح التسجيل، مسح الحقل');
            // مسح الحقل فقط عند النجاح
            document.getElementById('codeInput').value = '';
            document.getElementById('codeInput').focus();
            resetSubmitButton();
            updateStatus('✅ تم تسجيل الحضور بنجاح! جاهز لمسح كود جديد', 'success');

            // السماح بفتح الكاميرا في أي وقت

            // إعادة تعيين حالة الإرسال
            isSubmitting = false;
            document.getElementById('submitButton').disabled = false;
        <?php elseif ($scanResult && $scanResult['status'] === 'warning'): ?>
            console.log('⚠️ تحذير، الاحتفاظ بالحقل');
            // في حالة التحذير (كود مستخدم مسبقاً)، لا نمسح الحقل
            isSubmitting = false;
            document.getElementById('submitButton').disabled = false;
            resetSubmitButton();
            updateStatus('⚠️ <?php echo addslashes($scanResult['message']); ?>', 'warning');
        <?php elseif ($scanResult && $scanResult['status'] === 'error'): ?>
            console.log('❌ خطأ، الاحتفاظ بالحقل');
            // في حالة الخطأ، لا نمسح الحقل ليتمكن المستخدم من المراجعة
            isSubmitting = false;
            document.getElementById('submitButton').disabled = false;
            resetSubmitButton();
            updateStatus('❌ <?php echo addslashes($scanResult['message']); ?>', 'error');
        <?php else: ?>
            console.log('📝 لا توجد نتيجة مسح');
        <?php endif; ?>

        // تشغيل الكاميرا والماسح
        async function startCamera() {
            console.log('🔍 محاولة تشغيل الكاميرا...');

            // إزالة قيود منع فتح الكاميرا - السماح بالفتح في جميع الأوقات
            console.log('✅ السماح بفتح الكاميرا في جميع الأوقات');

            try {
                updateScannerStatus('🔄 جاري تشغيل الكاميرا...', 'scanning');

                // إنشاء ماسح جديد
                qrScanner = new QRScanner({
                    onScanSuccess: function(scannedData) {
                        console.log('تم مسح البيانات:', scannedData);

                        // استخراج الكود من البيانات الممسوحة
                        const extractedCode = extractCodeFromScannedData(scannedData);

                        console.log('الكود المستخرج:', extractedCode);

                        // تعبئة الحقل بالكود المستخرج فقط (بدون تفعيل الإدخال اليدوي)
                        isManualInput = false;
                        document.getElementById('codeInput').value = extractedCode;

                        // إظهار رسالة توضح ما تم مسحه وما تم استخراجه
                        if (scannedData !== extractedCode) {
                            updateScannerStatus(`✅ تم مسح رابط واستخراج رقم الإجازة: ${extractedCode} - اضغط "تسجيل الحضور"`, 'success');
                        } else {
                            updateScannerStatus(`✅ تم مسح رقم الإجازة: ${extractedCode} - اضغط "تسجيل الحضور"`, 'success');
                        }

                        // إظهار أن الكود جاهز للإرسال
                        showCodeReady();

                        // إيقاف الكاميرا بعد المسح الناجح لتوفير الموارد
                        setTimeout(() => {
                            stopCamera();
                        }, 2000);

                        // لا نرسل تلقائياً - ننتظر ضغط الزر
                        // setTimeout(() => {
                        //     document.getElementById('scanForm').submit();
                        // }, 1500);
                    },
                    onScanError: function(error) {
                        console.error('خطأ في المسح:', error);
                        updateScannerStatus('❌ ' + error, 'error');
                    }
                });

                // بدء المسح
                const scannerContainer = document.getElementById('scanner');
                await qrScanner.startScanning(scannerContainer);

                // تحديث واجهة المستخدم
                document.getElementById('cameraContainer').style.display = 'block';
                document.getElementById('startCamera').style.display = 'none';
                document.getElementById('stopCamera').style.display = 'inline-block';

                // إظهار زر تبديل الكاميرا إذا كان هناك أكثر من كاميرا
                const cameras = await qrScanner.getCameras();
                if (cameras.length > 1) {
                    document.getElementById('switchCamera').style.display = 'inline-block';
                }

                updateScannerStatus('🔍 وجه الكاميرا نحو كود QR - بعد المسح اضغط "تسجيل الحضور"', 'scanning');
                isScanning = true;

            } catch (error) {
                console.error('خطأ في تشغيل الكاميرا:', error);
                updateScannerStatus('❌ فشل في تشغيل الكاميرا. تأكد من السماح بالوصول للكاميرا.', 'error');
            }
        }

        // إيقاف الكاميرا
        function stopCamera() {
            if (qrScanner) {
                qrScanner.stopScanning();
                qrScanner = null;
            }

            document.getElementById('cameraContainer').style.display = 'none';
            document.getElementById('startCamera').style.display = 'inline-block';
            document.getElementById('stopCamera').style.display = 'none';
            document.getElementById('switchCamera').style.display = 'none';

            updateScannerStatus('📷 الكاميرا متوقفة', 'info');
            isScanning = false;
        }

        // تبديل الكاميرا
        async function switchCamera() {
            if (qrScanner && isScanning) {
                try {
                    await qrScanner.switchCamera();
                    updateScannerStatus('🔄 تم تبديل الكاميرا', 'info');
                } catch (error) {
                    console.error('خطأ في تبديل الكاميرا:', error);
                    updateScannerStatus('❌ فشل في تبديل الكاميرا', 'error');
                }
            }
        }

        // تحديث حالة الماسح
        function updateScannerStatus(message, type = 'info') {
            const statusElement = document.getElementById('scannerStatus');
            statusElement.textContent = message;
            statusElement.className = 'scanner-status ' + type;
        }

        // وظيفة إخفاء نتيجة المسح
        function hideScanResult() {
            const resultContainer = document.getElementById('scanResultContainer');
            if (resultContainer) {
                console.log('🗑️ إخفاء نتيجة المسح...');

                // إخفاء النتيجة
                resultContainer.style.display = 'none';

                // إزالة التأثيرات البصرية (إن وجدت)
                resultContainer.style.border = '';
                resultContainer.style.animation = '';

                console.log('✅ تم إخفاء النتيجة');
                updateScannerStatus('📷 يمكنك تشغيل الكاميرا أو إدخال كود جديد', 'info');
            }
        }

        // إخفاء النتيجة تلقائياً بعد 10 ثوان للنجاح
        <?php if ($scanResult && $scanResult['status'] === 'success'): ?>
            setTimeout(() => {
                hideScanResult();
            }, 10000);
        <?php endif; ?>

        // أحداث الأزرار
        document.getElementById('startCamera').addEventListener('click', startCamera);
        document.getElementById('stopCamera').addEventListener('click', stopCamera);
        document.getElementById('switchCamera').addEventListener('click', switchCamera);

        // إرسال تلقائي عند إدخال كود كامل (معطل للإدخال اليدوي)
        let autoSubmitTimeout = null;
        let isManualInput = false;

        document.getElementById('codeInput').addEventListener('input', function(e) {
            const value = e.target.value.trim();

            // تحديد إذا كان الإدخال يدوي (المستخدم يكتب)
            isManualInput = true;

            // مسح المؤقت السابق
            if (autoSubmitTimeout) {
                clearTimeout(autoSubmitTimeout);
                autoSubmitTimeout = null;
            }

            // التحقق من صحة الإدخال (كود أو رابط)
            const isValidCode = value.startsWith('INV_') && value.length >= 15;
            const isValidLink = value.includes('verify.php?code=') || value.includes('code=');

            if (isValidCode || isValidLink) {
                // إظهار رسالة تأكيد للإدخال اليدوي
                if (isManualInput) {
                    updateStatus('✅ رقم إجازة صحيح - اضغط "تسجيل الحضور" للمتابعة', 'success');
                    showCodeReady();
                } else {
                    // إرسال تلقائي فقط للمسح بالكاميرا
                    autoSubmitTimeout = setTimeout(() => {
                        document.getElementById('scanForm').submit();
                    }, 1000);
                }
            } else if (value.length > 0) {
                if (value.startsWith('INV_')) {
                    updateStatus(`⏳ أكمل كتابة رقم الإجازة... (${value.length}/20)`, 'warning');
                    showCodePreview(value);
                } else if (value.includes('verify.php') || value.includes('code=')) {
                    updateStatus('🔗 تم اكتشاف رابط - سيتم استخراج رقم الإجازة تلقائياً', 'info');
                    showCodePreview(value);
                } else {
                    updateStatus('⚠️ أدخل رقم إجازة صحيح يبدأ بـ INV_', 'warning');
                    hideCodePreview();
                }
                resetSubmitButton();
            } else {
                updateStatus('📝 أدخل رقم الإجازة أو امسح QR Code بالكاميرا', 'info');
                resetSubmitButton();
                hideCodePreview();
            }

            // إعادة تعيين حالة الإدخال اليدوي بعد فترة
            setTimeout(() => {
                isManualInput = false;
            }, 1000);
        });

        // إظهار أن الكود جاهز
        function showCodeReady() {
            const submitBtn = document.getElementById('submitButton');
            const codeStatus = document.getElementById('codeStatus');
            const inputHelp = document.getElementById('inputHelp');

            // تمييز زر الإرسال
            submitBtn.classList.add('ready');
            submitBtn.textContent = '🚀 تسجيل الحضور - رقم الإجازة جاهز!';

            // إظهار مؤشر الحالة
            codeStatus.style.display = 'block';

            // تحديث رسالة المساعدة
            inputHelp.innerHTML = '🎉 ممتاز! رقم الإجازة جاهز - اضغط "تسجيل الحضور" للمتابعة';
            inputHelp.style.background = '#d4edda';
            inputHelp.style.borderLeftColor = '#28a745';
            inputHelp.style.color = '#155724';
        }

        // إعادة تعيين زر الإرسال
        function resetSubmitButton() {
            const submitBtn = document.getElementById('submitButton');
            const codeStatus = document.getElementById('codeStatus');
            const inputHelp = document.getElementById('inputHelp');

            // إعادة تعيين زر الإرسال
            submitBtn.classList.remove('ready');
            submitBtn.style.background = '';
            submitBtn.style.transform = '';
            submitBtn.textContent = '✅ تسجيل الحضور';
            submitBtn.disabled = false;

            // إخفاء مؤشر الحالة
            codeStatus.style.display = 'none';

            // إعادة تعيين رسالة المساعدة
            inputHelp.innerHTML = '💡 امسح QR Code أو اكتب رقم الإجازة (INV_...)، ثم اضغط "تسجيل الحضور"';
            inputHelp.style.background = '#f8f9fa';
            inputHelp.style.borderLeftColor = '#17a2b8';
            inputHelp.style.color = '#666';

            // إعادة تعيين حالة الإرسال
            isSubmitting = false;
        }

        // إظهار معاينة الكود
        function showCodePreview(input) {
            const preview = document.getElementById('codePreview');
            const previewText = document.getElementById('codePreviewText');

            let displayText = input;
            let isLink = false;

            // التحقق من نوع الإدخال
            if (input.includes('verify.php?code=') || input.includes('code=')) {
                isLink = true;
                // محاولة استخراج الكود من الرابط
                const match = input.match(/code=([^&\s]+)/);
                if (match) {
                    displayText = `🔗 رابط: ${input.substring(0, 50)}${input.length > 50 ? '...' : ''}\n📋 رقم الإجازة: ${match[1]}`;
                }
            } else if (input.startsWith('INV_') && input.length > 4) {
                // تنسيق الكود لسهولة القراءة
                displayText = input.substring(0, 4) + '_' + input.substring(4);
            }

            previewText.textContent = displayText;
            preview.style.display = 'block';

            // تلوين حسب الحالة
            if (isLink) {
                preview.style.background = '#e3f2fd';
                preview.style.borderColor = '#2196f3';
                preview.style.color = '#1565c0';
            } else if (input.length >= 20) {
                preview.style.background = '#d4edda';
                preview.style.borderColor = '#28a745';
                preview.style.color = '#155724';
            } else if (input.length >= 10) {
                preview.style.background = '#fff3cd';
                preview.style.borderColor = '#ffc107';
                preview.style.color = '#856404';
            } else {
                preview.style.background = '#e9ecef';
                preview.style.borderColor = '#6c757d';
                preview.style.color = '#495057';
            }
        }

        // إخفاء معاينة الكود
        function hideCodePreview() {
            const preview = document.getElementById('codePreview');
            preview.style.display = 'none';
        }

        // دالة لتحديث الحالة (إذا لم تكن موجودة)
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('scannerStatus');
            if (statusElement) {
                statusElement.textContent = message;
                statusElement.className = 'scanner-status ' + type;
            }
        }

        // منع إرسال النموذج إذا كان الحقل فارغ أو غير صحيح
        let isSubmitting = false;

        document.getElementById('scanForm').addEventListener('submit', function(e) {
            const code = document.getElementById('codeInput').value.trim();

            console.log('🔍 محاولة إرسال النموذج مع الكود:', code);

            // منع الإرسال المتكرر
            if (isSubmitting) {
                console.log('❌ منع الإرسال المتكرر');
                e.preventDefault();
                return;
            }

            if (!code) {
                console.log('❌ الحقل فارغ');
                e.preventDefault();
                updateStatus('❌ يرجى إدخال رقم الإجازة أو مسحه بالكاميرا', 'error');
                document.getElementById('codeInput').focus();
                return;
            }

            // التحقق من صحة الكود أو الرابط
            const isValidCode = code.startsWith('INV_') && code.length >= 10; // تقليل الحد الأدنى إلى 10
            const isValidLink = code.includes('verify.php?code=') || code.includes('code=');

            console.log('🔍 فحص الكود:', {
                code: code,
                length: code.length,
                startsWithINV: code.startsWith('INV_'),
                isValidCode: isValidCode,
                isValidLink: isValidLink
            });

            if (!isValidCode && !isValidLink) {
                console.log('❌ كود غير صحيح');
                e.preventDefault();
                updateStatus('❌ رقم إجازة غير صحيح. يجب أن يبدأ بـ INV_ ويحتوي على 10 أحرف على الأقل', 'error');
                document.getElementById('codeInput').focus();
                return;
            }

            console.log('✅ الكود صحيح، جاري الإرسال...');

            // تعيين حالة الإرسال
            isSubmitting = true;
            const submitBtn = document.getElementById('submitButton');
            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ جاري التحقق...';
            updateStatus('🔄 جاري التحقق من الكود...', 'warning');
        });

        // تنظيف الموارد عند إغلاق الصفحة
        window.addEventListener('beforeunload', function() {
            stopCamera();
        });

        // تتبع آخر نشاط
        document.getElementById('codeInput').addEventListener('input', function() {
            window.lastInputTime = Date.now();
        });

        // التحقق من دعم الكاميرا
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            document.getElementById('startCamera').style.display = 'none';
            updateScannerStatus('❌ المتصفح لا يدعم الكاميرا', 'error');
        } else {
            updateScannerStatus('📷 اضغط "تشغيل الكاميرا" لمسح QR Code - يمكن فتح الكاميرا في أي وقت', 'info');
        }
    </script>
</body>
</html>
