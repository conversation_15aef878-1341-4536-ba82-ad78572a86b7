<?php
require_once 'config.php';

$pdo = getDBConnection();
$message = '';
$messageType = '';
$guest = null;
$isValid = false;

// التحقق من وجود الكود في الرابط
$code = sanitizeInput($_GET['code'] ?? '');

if (empty($code)) {
    $message = 'لم يتم تمرير كود صالح';
    $messageType = 'error';
} else {
    try {
        // البحث عن الضيف بالكود
        $stmt = $pdo->prepare("SELECT * FROM guests WHERE code = ?");
        $stmt->execute([$code]);
        $guest = $stmt->fetch();
        
        if (!$guest) {
            $message = 'كود الدعوة غير صالح أو غير موجود';
            $messageType = 'error';
        } elseif ($guest['scanned'] == 1) {
            $message = 'تم استخدام هذا الكود من قبل في تاريخ: ' . date('Y-m-d H:i', strtotime($guest['scanned_at']));
            $messageType = 'warning';
        } else {
            // تسجيل الحضور
            $stmt = $pdo->prepare("UPDATE guests SET scanned = 1, scanned_at = NOW() WHERE id = ?");
            $stmt->execute([$guest['id']]);
            
            $message = 'تم تسجيل حضورك بنجاح';
            $messageType = 'success';
            $isValid = true;
            
            // تحديث بيانات الضيف
            $guest['scanned'] = 1;
            $guest['scanned_at'] = date('Y-m-d H:i:s');
        }
        
    } catch (PDOException $e) {
        $message = 'حدث خطأ في النظام، يرجى المحاولة مرة أخرى';
        $messageType = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحقق من الدعوة - نظام إدارة الدعوات</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .verify-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
        }
        
        .verify-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .verify-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }
        
        .verify-icon.success { color: #28a745; }
        .verify-icon.error { color: #dc3545; }
        .verify-icon.warning { color: #ffc107; }
        
        .guest-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }
        
        .guest-info h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: bold;
            color: #666;
        }
        
        .info-value {
            color: #333;
        }
        
        .verify-message {
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .verify-message.success { color: #28a745; }
        .verify-message.error { color: #dc3545; }
        .verify-message.warning { color: #ffc107; }
        
        .back-link {
            margin-top: 30px;
        }
        
        @media (max-width: 768px) {
            .verify-container {
                margin: 20px;
                padding: 10px;
            }
            
            .verify-card {
                padding: 20px;
            }
            
            .verify-icon {
                font-size: 60px;
            }
            
            .verify-message {
                font-size: 20px;
            }
        }
    </style>
</head>
<body class="verify-page">
    <div class="verify-container">
        <div class="verify-card">
            <!-- أيقونة الحالة -->
            <div class="verify-icon <?php echo $messageType; ?>">
                <?php
                switch ($messageType) {
                    case 'success':
                        echo '✅';
                        break;
                    case 'error':
                        echo '❌';
                        break;
                    case 'warning':
                        echo '⚠️';
                        break;
                    default:
                        echo '❓';
                }
                ?>
            </div>
            
            <!-- رسالة الحالة -->
            <div class="verify-message <?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
            
            <!-- معلومات الضيف -->
            <?php if ($guest): ?>
                <div class="guest-info">
                    <h3>🎫 معلومات الدعوة</h3>
                    
                    <div class="info-row">
                        <span class="info-label">اسم الضيف:</span>
                        <span class="info-value"><?php echo htmlspecialchars($guest['name']); ?></span>
                    </div>
                    
                    <?php if (!empty($guest['phone'])): ?>
                        <div class="info-row">
                            <span class="info-label">رقم الجوال:</span>
                            <span class="info-value"><?php echo htmlspecialchars($guest['phone']); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="info-row">
                        <span class="info-label">كود الدعوة:</span>
                        <span class="info-value"><?php echo htmlspecialchars($guest['code']); ?></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">تاريخ الإنشاء:</span>
                        <span class="info-value"><?php echo date('Y-m-d H:i', strtotime($guest['created_at'])); ?></span>
                    </div>
                    
                    <?php if ($guest['scanned']): ?>
                        <div class="info-row">
                            <span class="info-label">تاريخ الحضور:</span>
                            <span class="info-value"><?php echo date('Y-m-d H:i', strtotime($guest['scanned_at'])); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="info-row">
                        <span class="info-label">حالة الحضور:</span>
                        <span class="info-value">
                            <?php echo $guest['scanned'] ? '✅ تم الحضور' : '⏳ لم يحضر بعد'; ?>
                        </span>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- رسالة ترحيبية للضيوف الجدد -->
            <?php if ($isValid): ?>
                <div class="welcome-message">
                    <h2>🎉 مرحباً <?php echo htmlspecialchars($guest['name']); ?>!</h2>
                    <p>نتشرف بحضورك معنا</p>
                    <p>تم تسجيل حضورك بنجاح في النظام</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="additional-info">
            <?php if ($messageType === 'error'): ?>
                <p>إذا كنت تعتقد أن هناك خطأ، يرجى التواصل مع منظم الفعالية</p>
            <?php elseif ($messageType === 'warning'): ?>
                <p>إذا لم تقم بمسح الكود من قبل، يرجى التواصل مع منظم الفعالية</p>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // إضافة تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.verify-card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
