# 🔧 دليل استكشاف أخطاء نظام المسح

## ❌ المشكلة الرئيسية:
**"عند المسح لا يقوم بتحديث الدعوة في قاعدة البيانات وتأكيد حضورها"**

---

## 🔍 خطوات التشخيص:

### 1️⃣ **اختبار النظام أولاً:**
```
http://localhost/اسم_المجلد/test_scan_system.php
```

هذه الصفحة ستختبر:
- ✅ وجود الجداول المطلوبة
- ✅ بنية جدول guests
- ✅ إنشاء ومسح دعوة تجريبية
- ✅ تحديث قاعدة البيانات
- ✅ سجل الأنشطة
- ✅ الإحصائيات

### 2️⃣ **فحص قاعدة البيانات:**
افتح phpMyAdmin وتحقق من:

#### جدول `guests`:
```sql
SELECT * FROM guests ORDER BY id DESC LIMIT 10;
```

**الأعمدة المطلوبة:**
- `id` - معرف الضيف
- `name` - اسم الضيف
- `code` - كود الدعوة
- `scanned` - حالة المسح (0 = لم يتم، 1 = تم)
- `scanned_at` - وقت المسح
- `scanned_by` - من قام بالمسح
- `created_by` - من أنشأ الدعوة

#### جدول `users`:
```sql
SELECT id, username, full_name, user_type FROM users;
```

#### جدول `activity_log`:
```sql
SELECT * FROM activity_log WHERE action = 'scan_guest' ORDER BY created_at DESC LIMIT 10;
```

---

## 🛠️ الحلول الشائعة:

### ❌ **"الجداول غير موجودة"**
**الحل:**
```
http://localhost/اسم_المجلد/setup_database.php
```

### ❌ **"الأعمدة مفقودة في جدول guests"**
**الحل:** تشغيل SQL التالي في phpMyAdmin:
```sql
ALTER TABLE guests 
ADD COLUMN scanned_by INT(11) DEFAULT NULL,
ADD COLUMN created_by INT(11) DEFAULT NULL,
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
```

### ❌ **"خطأ في الاتصال بقاعدة البيانات"**
**الحل:** تحقق من إعدادات config.php:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'invitation_system');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### ❌ **"الكود غير موجود"**
**الأسباب المحتملة:**
1. **الكود خاطئ** - تأكد من صحة النسخ
2. **قاعدة بيانات فارغة** - أنشئ دعوات أولاً
3. **مشكلة في الترميز** - تأكد من UTF-8

**الحل:**
```sql
-- البحث عن الكود
SELECT * FROM guests WHERE code LIKE '%INV_%';

-- إنشاء دعوة تجريبية
INSERT INTO guests (name, code, created_by) 
VALUES ('ضيف تجريبي', 'INV_TEST_123456789_0000', 1);
```

### ❌ **"لا يتم تحديث حالة المسح"**
**الحل:** تحقق من صلاحيات المستخدم:
```sql
-- منح صلاحيات كاملة
GRANT ALL PRIVILEGES ON invitation_system.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

---

## 🧪 اختبار يدوي خطوة بخطوة:

### الخطوة 1: إنشاء دعوة تجريبية
```sql
INSERT INTO guests (name, phone, code, created_by) 
VALUES ('اختبار المسح', '123456789', 'INV_TEST_' + UNIX_TIMESTAMP() + '_1234', 1);
```

### الخطوة 2: نسخ الكود
```sql
SELECT code FROM guests WHERE name = 'اختبار المسح' ORDER BY id DESC LIMIT 1;
```

### الخطوة 3: اختبار المسح
1. اذهب إلى لوحة تحكم الموظف
2. الصق الكود في الحقل
3. اضغط "تسجيل الحضور"

### الخطوة 4: التحقق من النتيجة
```sql
SELECT * FROM guests WHERE name = 'اختبار المسح' ORDER BY id DESC LIMIT 1;
```

**النتيجة المتوقعة:**
- `scanned = 1`
- `scanned_at` = وقت المسح
- `scanned_by` = معرف الموظف

---

## 📊 فحص السجلات:

### سجل أخطاء PHP:
```bash
# في XAMPP
tail -f C:\xampp\php\logs\php_error_log
```

### سجل أخطاء MySQL:
```bash
# في XAMPP
tail -f C:\xampp\mysql\data\mysql_error.log
```

### سجل أنشطة النظام:
```sql
SELECT * FROM activity_log 
WHERE action IN ('scan_guest', 'login', 'create_guest') 
ORDER BY created_at DESC LIMIT 20;
```

---

## 🔧 إصلاحات متقدمة:

### إعادة إنشاء جدول guests:
```sql
DROP TABLE IF EXISTS guests;

CREATE TABLE guests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    notes TEXT,
    code VARCHAR(50) UNIQUE NOT NULL,
    scanned TINYINT(1) DEFAULT 0,
    scanned_at TIMESTAMP NULL,
    scanned_by INT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_scanned (scanned),
    INDEX idx_created_by (created_by),
    INDEX idx_scanned_by (scanned_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### إعادة إنشاء جدول activity_log:
```sql
CREATE TABLE IF NOT EXISTS activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50),
    target_id INT,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

---

## 🎯 نقاط فحص سريعة:

### ✅ **قائمة التحقق:**
- [ ] XAMPP يعمل (Apache + MySQL)
- [ ] قاعدة البيانات `invitation_system` موجودة
- [ ] جدول `guests` موجود مع الأعمدة الصحيحة
- [ ] جدول `users` موجود مع المستخدمين
- [ ] جدول `activity_log` موجود
- [ ] هناك دعوات في جدول `guests`
- [ ] المستخدم مسجل دخول كموظف
- [ ] الكود المدخل صحيح ويبدأ بـ INV_

### 🔍 **أوامر فحص سريعة:**
```sql
-- فحص عدد الدعوات
SELECT COUNT(*) as total_guests FROM guests;

-- فحص الدعوات غير الممسوحة
SELECT COUNT(*) as unscanned FROM guests WHERE scanned = 0;

-- فحص آخر عمليات المسح
SELECT g.name, g.code, g.scanned_at, u.full_name as scanned_by_name
FROM guests g 
LEFT JOIN users u ON g.scanned_by = u.id 
WHERE g.scanned = 1 
ORDER BY g.scanned_at DESC 
LIMIT 5;
```

---

## 🎉 إذا كان كل شيء يعمل:

**النظام سيقوم بـ:**
1. ✅ **قبول الكود** المدخل أو الممسوح
2. ✅ **البحث في قاعدة البيانات** عن الضيف
3. ✅ **تحديث حالة المسح** إلى 1
4. ✅ **تسجيل وقت المسح** في scanned_at
5. ✅ **تسجيل من قام بالمسح** في scanned_by
6. ✅ **إظهار رسالة نجاح** مع بيانات الضيف
7. ✅ **تسجيل العملية** في activity_log
8. ✅ **تحديث الإحصائيات** في لوحة التحكم

**وفي المرة الثانية:**
- ⚠️ **رسالة تحذير** "تم استخدام هذا الكود من قبل"
- 📋 **عرض بيانات المسح السابق**

---

## 📞 إذا استمرت المشكلة:

1. **شغل صفحة الاختبار:** `test_scan_system.php`
2. **تحقق من سجل الأخطاء** في XAMPP
3. **أعد إنشاء قاعدة البيانات:** `setup_database.php`
4. **جرب دعوة جديدة** من لوحة تحكم المشرف
5. **تأكد من صحة الكود** المنسوخ

**النظام مصمم ليعمل بشكل مثالي!** 🚀
