<?php
require_once 'config.php';

// التحقق من تسجيل الدخول (مشرف أو مدير)
requireSupervisor();

$pdo = getDBConnection();
$message = '';

// معالجة حذف الموظف
if (isset($_GET['delete_employee'])) {
    $employeeId = (int)$_GET['delete_employee'];

    try {
        // التحقق من صلاحية الحذف
        if (isAdmin()) {
            // المدير يمكنه حذف أي موظف
            $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND user_type = 'employee'");
            $stmt->execute([$employeeId]);
        } else {
            // المشرف يمكنه حذف موظفيه فقط
            $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND user_type = 'employee' AND created_by = ?");
            $stmt->execute([$employeeId, $_SESSION['user_id']]);
        }

        $employee = $stmt->fetch();

        if ($employee) {
            // حذف الموظف
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $success = $stmt->execute([$employeeId]);

            if ($success) {
                // تسجيل النشاط
                try {
                    logActivity('delete_employee', 'user', $employeeId, "تم حذف حساب الموظف: {$employee['full_name']} ({$employee['username']})");
                } catch (Exception $e) {
                    // تجاهل خطأ التسجيل
                }

                $message = showAlert("تم حذف حساب الموظف '{$employee['full_name']}' بنجاح", 'success');
            } else {
                $message = showAlert('حدث خطأ أثناء حذف الحساب', 'error');
            }
        } else {
            $message = showAlert('الموظف غير موجود أو ليس لديك صلاحية لحذفه', 'error');
        }
    } catch (PDOException $e) {
        $message = showAlert('خطأ في قاعدة البيانات: ' . $e->getMessage(), 'error');
    }
}

// معالجة إضافة الموظف
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($username) || empty($password) || empty($full_name)) {
        $message = showAlert('جميع الحقول المطلوبة يجب ملؤها', 'error');
    } elseif (strlen($password) < 6) {
        $message = showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
    } else {
        try {
            // التحقق من عدم وجود اسم المستخدم مسبقاً
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                $message = showAlert('اسم المستخدم موجود مسبقاً، اختر اسم آخر', 'error');
            } else {
                // إضافة الموظف الجديد
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $createdBy = $_SESSION['user_id']; // المشرف الذي ينشئ الموظف
                
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, password, user_type, full_name, email, phone, created_by, created_at) 
                    VALUES (?, ?, 'employee', ?, ?, ?, ?, NOW())
                ");
                
                $success = $stmt->execute([
                    $username, 
                    $hashedPassword, 
                    $full_name, 
                    $email, 
                    $phone, 
                    $createdBy
                ]);
                
                if ($success) {
                    $employeeId = $pdo->lastInsertId();
                    
                    // تسجيل النشاط
                    try {
                        logActivity('create_employee', 'user', $employeeId, "تم إنشاء حساب موظف جديد: {$full_name} ({$username})");
                    } catch (Exception $e) {
                        // تجاهل خطأ التسجيل
                    }
                    
                    $message = showAlert("تم إنشاء حساب الموظف بنجاح!<br><strong>اسم المستخدم:</strong> {$username}<br><strong>الاسم:</strong> {$full_name}", 'success');
                    
                    // إعادة تعيين النموذج
                    $username = $password = $full_name = $email = $phone = '';
                } else {
                    $message = showAlert('حدث خطأ أثناء إنشاء الحساب، حاول مرة أخرى', 'error');
                }
            }
        } catch (PDOException $e) {
            $message = showAlert('خطأ في قاعدة البيانات: ' . $e->getMessage(), 'error');
        }
    }
}

// جلب قائمة الموظفين التابعين لهذا المشرف
try {
    if (isAdmin()) {
        // المدير يرى جميع الموظفين
        $stmt = $pdo->prepare("
            SELECT u.*, creator.full_name as created_by_name 
            FROM users u 
            LEFT JOIN users creator ON u.created_by = creator.id 
            WHERE u.user_type = 'employee' 
            ORDER BY u.created_at DESC
        ");
        $stmt->execute();
    } else {
        // المشرف يرى موظفيه فقط
        $stmt = $pdo->prepare("
            SELECT u.*, creator.full_name as created_by_name 
            FROM users u 
            LEFT JOIN users creator ON u.created_by = creator.id 
            WHERE u.user_type = 'employee' AND u.created_by = ? 
            ORDER BY u.created_at DESC
        ");
        $stmt->execute([$_SESSION['user_id']]);
    }
    $employees = $stmt->fetchAll();
} catch (PDOException $e) {
    $employees = [];
    if (empty($message)) {
        $message = showAlert('خطأ في جلب قائمة الموظفين: ' . $e->getMessage(), 'error');
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة موظف جديد</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .form-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        .form-group.required label::after {
            content: " *";
            color: #dc3545;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .employees-list {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .employee-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        
        .employee-info {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 15px;
            align-items: center;
        }
        
        .employee-details h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .employee-details p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        .employee-actions {
            display: flex;
            gap: 10px;
        }
        
        .password-hint {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .employee-info {
                grid-template-columns: 1fr;
            }
            
            .employee-actions {
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الهيدر -->
        <header class="header">
            <div class="header-content">
                <h1>👨‍💼 إضافة موظف جديد</h1>
                <div class="header-actions">
                    <span>مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                    <a href="supervisor_dashboard.php" class="btn btn-secondary">← العودة للوحة المشرف</a>
                    <a href="logout.php" class="btn btn-secondary">تسجيل الخروج</a>
                </div>
            </div>
        </header>

        <?php echo $message; ?>

        <!-- نموذج إضافة الموظف -->
        <div class="form-container">
            <h2>📝 بيانات الموظف الجديد</h2>
            <form method="POST" action="">
                <div class="form-row">
                    <div class="form-group required">
                        <label for="username">اسم المستخدم</label>
                        <input type="text" id="username" name="username" 
                               value="<?php echo htmlspecialchars($username ?? ''); ?>" 
                               required maxlength="50">
                        <div class="password-hint">سيستخدم الموظف هذا الاسم لتسجيل الدخول</div>
                    </div>
                    
                    <div class="form-group required">
                        <label for="password">كلمة المرور</label>
                        <input type="password" id="password" name="password" required minlength="6">
                        <div class="password-hint">6 أحرف على الأقل</div>
                    </div>
                </div>
                
                <div class="form-group required">
                    <label for="full_name">الاسم الكامل</label>
                    <input type="text" id="full_name" name="full_name" 
                           value="<?php echo htmlspecialchars($full_name ?? ''); ?>" 
                           required maxlength="100">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" 
                               value="<?php echo htmlspecialchars($email ?? ''); ?>" 
                               maxlength="100">
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">رقم الجوال</label>
                        <input type="tel" id="phone" name="phone" 
                               value="<?php echo htmlspecialchars($phone ?? ''); ?>" 
                               maxlength="20">
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary">
                        ➕ إنشاء حساب الموظف
                    </button>
                    <a href="supervisor_dashboard.php" class="btn btn-secondary">
                        ❌ إلغاء
                    </a>
                </div>
            </form>
        </div>

        <!-- قائمة الموظفين -->
        <div class="employees-list">
            <h2>👥 الموظفين التابعين <?php echo isAdmin() ? '(جميع الموظفين)' : '(موظفيك)'; ?></h2>
            
            <?php if (!empty($employees)): ?>
                <?php foreach ($employees as $employee): ?>
                    <div class="employee-card">
                        <div class="employee-info">
                            <div class="employee-details">
                                <h4>👤 <?php echo htmlspecialchars($employee['full_name']); ?></h4>
                                <p><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($employee['username']); ?></p>
                                <?php if (!empty($employee['email'])): ?>
                                    <p><strong>البريد:</strong> <?php echo htmlspecialchars($employee['email']); ?></p>
                                <?php endif; ?>
                                <?php if (!empty($employee['phone'])): ?>
                                    <p><strong>الجوال:</strong> <?php echo htmlspecialchars($employee['phone']); ?></p>
                                <?php endif; ?>
                                <p><strong>أنشأه:</strong> <?php echo htmlspecialchars($employee['created_by_name'] ?: 'غير محدد'); ?></p>
                                <p><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d H:i', strtotime($employee['created_at'])); ?></p>
                            </div>
                            
                            <div class="employee-actions">
                                <?php if (isAdmin() || $employee['created_by'] == $_SESSION['user_id']): ?>
                                    <a href="edit_employee.php?id=<?php echo $employee['id']; ?>" 
                                       class="btn btn-sm btn-warning">✏️ تعديل</a>
                                    <a href="?delete_employee=<?php echo $employee['id']; ?>" 
                                       class="btn btn-sm btn-danger"
                                       onclick="return confirm('هل أنت متأكد من حذف هذا الموظف؟\nسيتم حذف الحساب نهائياً!')">
                                        🗑️ حذف
                                    </a>
                                <?php else: ?>
                                    <span class="btn btn-sm btn-light">🔒 محمي</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div style="text-align: center; padding: 40px; color: #666;">
                    <h3>📭 لا توجد موظفين</h3>
                    <p>لم تقم بإنشاء أي حسابات موظفين بعد</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
