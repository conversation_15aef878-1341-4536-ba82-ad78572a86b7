<?php
/**
 * مولد أكواد QR محلي
 * يعمل بدون الحاجة للإنترنت
 */

/**
 * إنشاء QR Code باستخدام HTML/CSS (للعرض فقط)
 */
function generateHTMLQRCode($code, $url) {
    $html = '
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>كود QR - ' . htmlspecialchars($code) . '</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                text-align: center;
                padding: 20px;
                background: #f5f5f5;
            }
            .qr-container {
                background: white;
                border-radius: 15px;
                padding: 30px;
                margin: 20px auto;
                max-width: 400px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            }
            .qr-placeholder {
                width: 300px;
                height: 300px;
                border: 3px dashed #007bff;
                margin: 20px auto;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8f9fa;
                border-radius: 10px;
                font-size: 18px;
                color: #666;
                text-align: center;
                line-height: 1.4;
            }
            .code-info {
                background: #e9ecef;
                padding: 15px;
                border-radius: 8px;
                margin: 20px 0;
                font-family: monospace;
            }
            .instructions {
                color: #666;
                font-size: 14px;
                margin-top: 20px;
            }
            .url-link {
                word-break: break-all;
                background: #fff3cd;
                padding: 10px;
                border-radius: 5px;
                margin: 10px 0;
                border: 1px solid #ffeaa7;
            }
        </style>
    </head>
    <body>
        <div class="qr-container">
            <h2>🎫 دعوة حضور</h2>
            
            <div class="qr-placeholder">
                <div>
                    <strong>كود QR</strong><br>
                    (يتطلب اتصال بالإنترنت لتوليد الصورة)
                </div>
            </div>
            
            <div class="code-info">
                <strong>كود الدعوة:</strong><br>
                ' . htmlspecialchars($code) . '
            </div>
            
            <div class="url-link">
                <strong>رابط التحقق:</strong><br>
                <a href="' . htmlspecialchars($url) . '" target="_blank">' . htmlspecialchars($url) . '</a>
            </div>
            
            <div class="instructions">
                <p><strong>تعليمات الاستخدام:</strong></p>
                <ol style="text-align: right;">
                    <li>امسح كود QR باستخدام تطبيق قارئ الأكواد</li>
                    <li>أو اضغط على الرابط أعلاه</li>
                    <li>أو ادخل الكود يدوياً في الموقع</li>
                </ol>
            </div>
        </div>
        
        <script>
            // محاولة تحميل QR Code من خدمة خارجية
            function loadQRCode() {
                const placeholder = document.querySelector(".qr-placeholder");
                const qrUrl = "https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=" + encodeURIComponent("' . $url . '");
                
                const img = new Image();
                img.onload = function() {
                    placeholder.innerHTML = "";
                    img.style.maxWidth = "100%";
                    img.style.height = "auto";
                    placeholder.appendChild(img);
                };
                img.onerror = function() {
                    placeholder.innerHTML = "<div><strong>⚠️ تعذر تحميل كود QR</strong><br>يرجى استخدام الرابط أدناه</div>";
                };
                img.src = qrUrl;
            }
            
            // تحميل QR Code عند تحميل الصفحة
            window.onload = loadQRCode;
        </script>
    </body>
    </html>';
    
    return $html;
}

/**
 * إنشاء ملف QR Code HTML
 */
function createQRCodeFile($code, $url) {
    try {
        $html = generateHTMLQRCode($code, $url);
        $filePath = "qrcodes/{$code}.html";
        file_put_contents($filePath, $html);
        return $filePath;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * توليد QR Code محسن مع عدة خيارات
 */
function generateAdvancedQRCode($code, $url) {
    // الطريقة الأولى: QR Server API
    $methods = [
        "https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=" . urlencode($url),
        "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=" . urlencode($url),
        "https://quickchart.io/qr?text=" . urlencode($url) . "&size=300"
    ];
    
    foreach ($methods as $qrUrl) {
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ]
            ]);
            
            $qrImage = @file_get_contents($qrUrl, false, $context);
            
            if ($qrImage !== false && strlen($qrImage) > 100) {
                $filePath = "qrcodes/{$code}.png";
                file_put_contents($filePath, $qrImage);
                return $filePath;
            }
        } catch (Exception $e) {
            continue;
        }
    }
    
    // إذا فشلت جميع الطرق، إنشاء ملف HTML
    return createQRCodeFile($code, $url);
}

/**
 * إنشاء QR Code بسيط باستخدام ASCII (للاختبار)
 */
function generateASCIIQR($code) {
    $ascii = "
    ████████████████████████████
    ██                        ██
    ██  ████  ██████  ████    ██
    ██  ████  ██████  ████    ██
    ██  ████  ██████  ████    ██
    ██                        ██
    ██  ████████████████████  ██
    ██                        ██
    ██    " . str_pad($code, 16, ' ', STR_PAD_BOTH) . "    ██
    ██                        ██
    ██  ████████████████████  ██
    ██                        ██
    ██  ████  ██████  ████    ██
    ██  ████  ██████  ████    ██
    ██  ████  ██████  ████    ██
    ██                        ██
    ████████████████████████████
    ";
    
    return $ascii;
}

// إذا تم استدعاء الملف مباشرة
if (isset($_GET['code']) && isset($_GET['url'])) {
    $code = $_GET['code'];
    $url = $_GET['url'];
    
    header('Content-Type: text/html; charset=utf-8');
    echo generateHTMLQRCode($code, $url);
    exit();
}
?>
